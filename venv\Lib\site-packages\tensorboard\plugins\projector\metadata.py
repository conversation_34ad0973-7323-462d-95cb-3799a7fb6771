# Copyright 2020 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Internal information about the projector plugin."""


PLUGIN_NAME = "projector"

PLUGINS_DIR = "plugins"  # must match plugin_asset_util.PLUGINS_DIR
PLUGIN_ASSETS_NAME = "org_tensorflow_tensorboard_projector"

# FYI - the PROJECTOR_FILENAME is hardcoded in the visualize_embeddings
# method in tf.contrib.tensorboard.plugins.projector module.
# TODO(@decentralion): Fix duplication when we find a permanent home for the
# projector module.
PROJECTOR_FILENAME = "projector_config.pbtxt"
