"""
Assists Model (Step 6 of 9) - Playmaking-Focused Architecture
=============================================================

This module implements a playmaking-focused assists prediction model with:
- Teammate interaction modeling
- Playmaking-specific features
- Usage rate and assist opportunity modeling
- Contextual assist predictions

Key Components:
- AssistsModel: Main PyTorch Lightning model
- TeammateInteractionModule: Models teammate synergy
- PlaymakingFeatures: Assist-specific feature engineering
- AssistOpportunityProcessor: Opportunity-based modeling
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import logging

logger = logging.getLogger(__name__)


class TeammateInteractionModule(nn.Module):
    """
    Neural network module for modeling teammate interactions.
    
    This module processes teammate features to understand how
    teammate abilities affect assist opportunities.
    """
    
    def __init__(self, teammate_dim: int = 16, hidden_dim: int = 32):
        super().__init__()
        
        self.teammate_net = nn.Sequential(
            nn.Linear(teammate_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # Teammate synergy score
        )
        
    def forward(self, teammate_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            teammate_features: [batch_size, num_teammates, teammate_dim]
            
        Returns:
            teammate_synergy: [batch_size, 1] - pooled teammate synergy
        """
        batch_size, num_teammates, teammate_dim = teammate_features.shape
        
        # Reshape for processing
        teammate_flat = teammate_features.view(-1, teammate_dim)
        
        # Process through network
        synergy_scores = self.teammate_net(teammate_flat)
        
        # Reshape back and pool
        synergy_scores = synergy_scores.view(batch_size, num_teammates, 1)
        
        # Weighted average (can be improved with attention)
        teammate_synergy = synergy_scores.mean(dim=1)
        
        return teammate_synergy


class AssistsModel(pl.LightningModule):
    """
    Playmaking-focused assists prediction model.
    
    This model focuses on predicting assists by incorporating:
    - Individual playmaking abilities
    - Teammate interaction effects
    - Usage rate and opportunity modeling
    - Contextual game situations
    """
    
    def __init__(
        self,
        input_dim: int = 42,
        teammate_dim: int = 16,
        hidden_dim: int = 256,
        teammate_hidden_dim: int = 32,
        dropout: float = 0.2,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-4,
        delta: float = 1.0
    ):
        super().__init__()
        self.save_hyperparameters()
        
        # Teammate interaction module
        self.teammate_interaction = TeammateInteractionModule(
            teammate_dim=teammate_dim,
            hidden_dim=teammate_hidden_dim
        )
        
        # Main assist prediction network
        self.assist_net = nn.Sequential(
            nn.Linear(input_dim + 1, hidden_dim),  # +1 for teammate synergy
            nn.BatchNorm1d(hidden_dim),
            nn.SiLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.SiLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.BatchNorm1d(hidden_dim // 4),
            nn.SiLU(),
            
            nn.Linear(hidden_dim // 4, 1),
            nn.ReLU()  # Assists are non-negative
        )
        
        # Usage rate attention mechanism
        self.usage_attention = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.Tanh(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # Loss function - Huber loss for robust prediction
        self.loss_fn = nn.HuberLoss(delta=delta)
        
        # Metrics tracking
        self.train_losses = []
        self.val_losses = []
        
    def forward(self, player_features: torch.Tensor, 
                teammate_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass for assists prediction
        
        Args:
            player_features: [batch_size, input_dim] - Player features
            teammate_features: [batch_size, num_teammates, teammate_dim] - Teammate features
            
        Returns:
            Dictionary with assists prediction and intermediate values
        """
        # Process teammate interactions
        teammate_synergy = self.teammate_interaction(teammate_features)
        
        # Usage rate attention
        usage_weight = self.usage_attention(player_features)
        
        # Combine player features with teammate synergy
        combined_features = torch.cat([player_features, teammate_synergy], dim=1)
        
        # Main assists prediction
        base_assists = self.assist_net(combined_features)
        
        # Apply usage rate weighting
        assists_prediction = base_assists * usage_weight
        
        return {
            'assists': assists_prediction,
            'base_assists': base_assists,
            'teammate_synergy': teammate_synergy,
            'usage_weight': usage_weight
        }
    
    def calculate_assist_to_usage_ratio(self, assists_pred: torch.Tensor, 
                                      usage_rate: torch.Tensor) -> torch.Tensor:
        """
        Calculate assist-to-usage ratio for validation
        
        Args:
            assists_pred: Predicted assists
            usage_rate: Player usage rate
            
        Returns:
            Assist-to-usage ratio
        """
        return assists_pred / (usage_rate + 1e-6)  # Avoid division by zero
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Training step"""
        (player_features, teammate_features), target = batch
        
        # Forward pass
        outputs = self(player_features, teammate_features)
        assists_pred = outputs['assists']
        
        # Calculate loss
        loss = self.loss_fn(assists_pred.squeeze(), target)
        
        # Calculate additional metrics
        usage_rate = player_features[:, 0]  # Assuming usage rate is first feature
        assist_usage_ratio = self.calculate_assist_to_usage_ratio(
            assists_pred.squeeze(), usage_rate
        )
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_assists_mean', assists_pred.mean(), on_epoch=True)
        self.log('train_assists_std', assists_pred.std(), on_epoch=True)
        self.log('train_usage_weight', outputs['usage_weight'].mean(), on_epoch=True)
        self.log('train_teammate_synergy', outputs['teammate_synergy'].mean(), on_epoch=True)
        self.log('train_assist_usage_ratio', assist_usage_ratio.mean(), on_epoch=True)
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Validation step"""
        (player_features, teammate_features), target = batch
        
        # Forward pass
        outputs = self(player_features, teammate_features)
        assists_pred = outputs['assists']
        
        # Calculate loss
        loss = self.loss_fn(assists_pred.squeeze(), target)
        
        # Calculate additional metrics
        mae = F.l1_loss(assists_pred.squeeze(), target)
        mse = F.mse_loss(assists_pred.squeeze(), target)
        
        # Usage rate validation
        usage_rate = player_features[:, 0]
        assist_usage_ratio = self.calculate_assist_to_usage_ratio(
            assists_pred.squeeze(), usage_rate
        )
        
        # Log metrics
        self.log('val_loss', loss, on_epoch=True, prog_bar=True)
        self.log('val_mae', mae, on_epoch=True)
        self.log('val_mse', mse, on_epoch=True)
        self.log('val_assists_mean', assists_pred.mean(), on_epoch=True)
        self.log('val_assists_std', assists_pred.std(), on_epoch=True)
        self.log('val_usage_weight', outputs['usage_weight'].mean(), on_epoch=True)
        self.log('val_teammate_synergy', outputs['teammate_synergy'].mean(), on_epoch=True)
        self.log('val_assist_usage_ratio', assist_usage_ratio.mean(), on_epoch=True)
        
        return loss
    
    def test_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Test step"""
        (player_features, teammate_features), target = batch
        
        # Forward pass
        outputs = self(player_features, teammate_features)
        assists_pred = outputs['assists']
        
        # Calculate loss
        loss = self.loss_fn(assists_pred.squeeze(), target)
        
        # Calculate metrics
        mae = F.l1_loss(assists_pred.squeeze(), target)
        
        # Log metrics
        self.log('test_loss', loss, on_epoch=True)
        self.log('test_mae', mae, on_epoch=True)
        
        return loss
    
    def configure_optimizers(self):
        """Configure optimizer and learning rate scheduler"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=self.hparams.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }
    
    def predict_assists(self, player_features: torch.Tensor, 
                       teammate_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Generate assists predictions with insights
        
        Args:
            player_features: Player features
            teammate_features: Teammate features
            
        Returns:
            Dictionary with predictions and insights
        """
        self.eval()
        with torch.no_grad():
            outputs = self(player_features, teammate_features)
            
            # Calculate confidence intervals (simple approach)
            assists_pred = outputs['assists']
            assists_std = torch.std(assists_pred) if assists_pred.numel() > 1 else torch.tensor(1.0)
            
            confidence_lower = torch.clamp(assists_pred - 1.96 * assists_std, min=0)
            confidence_upper = assists_pred + 1.96 * assists_std
            
            # Calculate playmaking efficiency
            usage_rate = player_features[:, 0]
            playmaking_efficiency = outputs['assists'] / (usage_rate + 1e-6)
            
            return {
                'assists': assists_pred,
                'confidence_lower': confidence_lower,
                'confidence_upper': confidence_upper,
                'base_assists': outputs['base_assists'],
                'teammate_synergy': outputs['teammate_synergy'],
                'usage_weight': outputs['usage_weight'],
                'playmaking_efficiency': playmaking_efficiency
            }


# Assist-specific feature definitions
ASSIST_FEATURES = [
    # Core playmaking features
    'usage_rate',
    'assist_rate_ema10',
    'potential_assists',
    'secondary_assists',
    'hockey_assists',
    'pass_accuracy',
    'drive_and_kick_rate',
    'pick_and_roll_frequency',
    'assist_to_usage_ratio',
    
    # Ball handling and creation
    'time_of_possession',
    'dribbles_per_possession',
    'passes_per_possession',
    'touches_per_possession',
    'frontcourt_touches',
    'paint_touches',
    'post_touches',
    'elbow_touches',
    
    # Playmaking context
    'minutes_per_game',
    'pace_adjusted_possessions',
    'teammate_fg_pct',
    'teammate_3p_pct',
    'teammate_ft_pct',
    'open_teammate_opportunities',
    'contested_pass_rate',
    
    # Advanced metrics
    'assist_points_created',
    'assist_adjusted_plus_minus',
    'on_off_assist_diff',
    'clutch_assist_rate',
    'transition_assist_rate',
    'half_court_assist_rate',
    
    # Positional context
    'position_versatility',
    'primary_position_assists',
    'secondary_position_assists',
    'off_ball_movement_assists',
    'screen_assists',
    'cut_assists',
    'spot_up_assists',
    
    # Team context
    'team_assist_rate',
    'team_pace',
    'team_offensive_rating',
    'team_ball_movement_index'
]

# Teammate feature definitions
TEAMMATE_FEATURES = [
    # Shooting ability
    'teammate_catch_and_shoot_pct',
    'teammate_spot_up_pct',
    'teammate_corner_3_pct',
    'teammate_open_shot_pct',
    'teammate_finishing_ability',
    'teammate_free_throw_rate',
    
    # Movement and positioning
    'teammate_gravity_score',
    'teammate_off_movement',
    'teammate_screen_usage',
    'teammate_cutting_frequency',
    'teammate_spacing_score',
    
    # Synergy indicators
    'teammate_assist_reception_rate',
    'teammate_turnover_rate',
    'teammate_offensive_awareness',
    'teammate_shot_selection',
    'teammate_rhythm_shooting',
    'teammate_clutch_shooting'
]

# Feature categories for better organization
ASSIST_FEATURE_CATEGORIES = {
    'core_playmaking': [
        'usage_rate',
        'assist_rate_ema10',
        'potential_assists',
        'secondary_assists',
        'hockey_assists',
        'pass_accuracy',
        'drive_and_kick_rate',
        'pick_and_roll_frequency',
        'assist_to_usage_ratio'
    ],
    'ball_handling': [
        'time_of_possession',
        'dribbles_per_possession',
        'passes_per_possession',
        'touches_per_possession',
        'frontcourt_touches',
        'paint_touches',
        'post_touches',
        'elbow_touches'
    ],
    'playmaking_context': [
        'minutes_per_game',
        'pace_adjusted_possessions',
        'teammate_fg_pct',
        'teammate_3p_pct',
        'teammate_ft_pct',
        'open_teammate_opportunities',
        'contested_pass_rate'
    ],
    'advanced_metrics': [
        'assist_points_created',
        'assist_adjusted_plus_minus',
        'on_off_assist_diff',
        'clutch_assist_rate',
        'transition_assist_rate',
        'half_court_assist_rate'
    ],
    'positional_context': [
        'position_versatility',
        'primary_position_assists',
        'secondary_position_assists',
        'off_ball_movement_assists',
        'screen_assists',
        'cut_assists',
        'spot_up_assists'
    ],
    'team_context': [
        'team_assist_rate',
        'team_pace',
        'team_offensive_rating',
        'team_ball_movement_index'
    ]
}

TEAMMATE_FEATURE_CATEGORIES = {
    'shooting_ability': [
        'teammate_catch_and_shoot_pct',
        'teammate_spot_up_pct',
        'teammate_corner_3_pct',
        'teammate_open_shot_pct',
        'teammate_finishing_ability',
        'teammate_free_throw_rate'
    ],
    'movement_positioning': [
        'teammate_gravity_score',
        'teammate_off_movement',
        'teammate_screen_usage',
        'teammate_cutting_frequency',
        'teammate_spacing_score'
    ],
    'synergy_indicators': [
        'teammate_assist_reception_rate',
        'teammate_turnover_rate',
        'teammate_offensive_awareness',
        'teammate_shot_selection',
        'teammate_rhythm_shooting',
        'teammate_clutch_shooting'
    ]
}
