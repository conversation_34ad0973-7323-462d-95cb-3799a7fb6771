# Requirements for Assists Model (Step 6 of 9)
# Basic ML and data processing
torch>=1.9.0
pytorch-lightning>=1.5.0
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Data visualization and analysis
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Model tracking and logging
tensorboard>=2.7.0
wandb>=0.12.0

# Utility libraries
tqdm>=4.62.0
pathlib2>=2.3.0
pyyaml>=6.0
joblib>=1.1.0

# Development and testing
pytest>=6.2.0
black>=21.0.0
flake8>=4.0.0
mypy>=0.910

# Optional: For advanced features
optuna>=2.10.0  # Hyperparameter optimization
hydra-core>=1.1.0  # Configuration management
