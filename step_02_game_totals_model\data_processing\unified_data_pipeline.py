"""
🔧 Unified Data Pipeline for Hierarchical WNBA Modeling

This module creates a unified data pipeline that ensures both Step 1 (Player Points) 
and Step 2 (Game Totals) models use the same underlying data source with consistent 
features, preventing feature dimension mismatches in hierarchical integration.

Key Features:
- Unified feature engineering for both player and team level models
- Consistent data splits and preprocessing
- Automated feature mapping between aggregation levels
- Robust hierarchical integration support
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import os
from datetime import datetime
from pathlib import Path


class UnifiedDataPipeline:
    """
    🔧 Unified Data Pipeline for Hierarchical WNBA Modeling
    
    This pipeline ensures both Step 1 and Step 2 models use the same features
    and data structure, preventing feature dimension mismatches.
    """
    
    def __init__(
        self,
        game_data_path: str,
        player_data_path: str,
        output_dir: str = "unified_data"
    ):
        self.game_data_path = game_data_path
        self.player_data_path = player_data_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Unified feature set (common features for both models)
        self.unified_features = [
            'points', 'rebounds', 'assists', 'steals', 'blocks',
            'field_goals_made', 'field_goals_attempted', 'field_goal_percentage',
            'three_pointers_made', 'three_pointers_attempted', 'three_point_percentage',
            'free_throws_made', 'free_throws_attempted', 'free_throw_percentage',
            'turnovers', 'minutes_played', 'games_played'
        ]
        
        # Metadata columns (preserved across both models)
        self.metadata_columns = [
            'date', 'season', 'team_abbreviation', 'player_name', 'game_id'
        ]
        
        # Data containers
        self.raw_game_data = None
        self.raw_player_data = None
        self.unified_game_data = None
        self.unified_player_data = None
    
    def load_raw_data(self):
        """Load raw data from both sources"""
        print("Loading raw data...")
        
        # Load game-level data
        print(f"Loading game data from: {self.game_data_path}")
        self.raw_game_data = pd.read_csv(self.game_data_path)
        
        # Load player-level data
        print(f"Loading player data from: {self.player_data_path}")
        self.raw_player_data = pd.read_csv(self.player_data_path)
        
        print(f"Raw game data shape: {self.raw_game_data.shape}")
        print(f"Raw player data shape: {self.raw_player_data.shape}")
    
    def create_unified_features(self):
        """Create unified features for both datasets"""
        print("Creating unified features...")
        
        # Process game data to match unified features
        self.unified_game_data = self._process_game_data()
        
        # Process player data to match unified features
        self.unified_player_data = self._process_player_data()
        
        # Validate feature consistency
        self._validate_feature_consistency()
    
    def _process_game_data(self) -> pd.DataFrame:
        """Process game data to match unified feature set"""
        print("Processing game data...")
        
        game_data = self.raw_game_data.copy()
        
        # Ensure date column is datetime
        if 'date' in game_data.columns:
            game_data['date'] = pd.to_datetime(game_data['date'])
        
        # Map game-level features to unified features
        feature_mapping = {
            'team_total_points': 'points',
            'rebounds': 'rebounds',
            'assists': 'assists',
            'steals': 'steals',
            'blocks': 'blocks',
            'field_goals_made': 'field_goals_made',
            'field_goals_attempted': 'field_goals_attempted',
            'field_goal_percentage': 'field_goal_percentage',
            'three_pointers_made': 'three_pointers_made',
            'three_pointers_attempted': 'three_pointers_attempted',
            'three_point_percentage': 'three_point_percentage',
            'free_throws_made': 'free_throws_made',
            'free_throws_attempted': 'free_throws_attempted',
            'free_throw_percentage': 'free_throw_percentage',
            'turnovers': 'turnovers'
        }
        
        # Apply feature mapping
        for old_col, new_col in feature_mapping.items():
            if old_col in game_data.columns:
                game_data[new_col] = game_data[old_col]
        
        # Create derived features if missing
        if 'minutes_played' not in game_data.columns:
            game_data['minutes_played'] = 240  # Assume 40 minutes per game * 6 players
        
        if 'games_played' not in game_data.columns:
            game_data['games_played'] = 1  # Each row is one game
        
        # Ensure all unified features exist
        for feature in self.unified_features:
            if feature not in game_data.columns:
                game_data[feature] = 0  # Default to 0 if missing
        
        # Add team and player identifiers for hierarchical mapping
        game_data['team_id'] = game_data.get('team_id', game_data.get('team_abbreviation', ''))
        game_data['player_name'] = 'TEAM_AGGREGATE'  # Team-level data
        
        return game_data
    
    def _process_player_data(self) -> pd.DataFrame:
        """Process player data to match unified feature set"""
        print("Processing player data...")
        
        player_data = self.raw_player_data.copy()
        
        # Create synthetic date column if missing (for consistent time splits)
        if 'date' not in player_data.columns:
            # Map season to approximate date
            season_dates = {
                2020: '2020-07-01',
                2021: '2021-05-01',
                2022: '2022-05-01',
                2023: '2023-05-01',
                2024: '2024-05-01',
                2025: '2025-05-01'
            }
            player_data['date'] = player_data['season'].map(season_dates)
            player_data['date'] = pd.to_datetime(player_data['date'])
        
        # Map player-level features to unified features
        feature_mapping = {
            'points': 'points',
            'rebounds': 'rebounds',
            'assists': 'assists',
            'steals': 'steals',
            'blocks': 'blocks',
            'threes': 'three_pointers_made',
            'games_played': 'games_played',
            'minutes_per_game': 'minutes_played'
        }
        
        # Apply feature mapping
        for old_col, new_col in feature_mapping.items():
            if old_col in player_data.columns:
                player_data[new_col] = player_data[old_col]
        
        # Create derived features from available data
        if 'field_goal_percentage' in player_data.columns:
            player_data['field_goal_percentage'] = player_data['field_goal_percentage']
        
        if 'free_throw_percentage' in player_data.columns:
            player_data['free_throw_percentage'] = player_data['free_throw_percentage']
        
        # Estimate missing features based on available data
        if 'field_goals_made' not in player_data.columns and 'points' in player_data.columns:
            # Estimate field goals made from points (rough approximation)
            player_data['field_goals_made'] = player_data['points'] * 0.4  # ~40% of points from FG
        
        if 'field_goals_attempted' not in player_data.columns and 'field_goals_made' in player_data.columns:
            fg_pct = player_data.get('field_goal_percentage', 0.45)  # Default 45% FG%
            player_data['field_goals_attempted'] = player_data['field_goals_made'] / np.maximum(fg_pct, 0.1)
        
        if 'three_pointers_attempted' not in player_data.columns and 'three_pointers_made' in player_data.columns:
            player_data['three_pointers_attempted'] = player_data['three_pointers_made'] / 0.35  # ~35% 3P%
        
        if 'three_point_percentage' not in player_data.columns:
            player_data['three_point_percentage'] = np.where(
                player_data['three_pointers_attempted'] > 0,
                player_data['three_pointers_made'] / player_data['three_pointers_attempted'],
                0.35
            )
        
        if 'free_throws_made' not in player_data.columns:
            player_data['free_throws_made'] = player_data['points'] * 0.15  # ~15% of points from FT
        
        if 'free_throws_attempted' not in player_data.columns:
            ft_pct = player_data.get('free_throw_percentage', 0.8)  # Default 80% FT%
            player_data['free_throws_attempted'] = player_data['free_throws_made'] / np.maximum(ft_pct, 0.1)
        
        # Ensure all unified features exist
        for feature in self.unified_features:
            if feature not in player_data.columns:
                player_data[feature] = 0  # Default to 0 if missing
        
        # Ensure consistent minutes_played (convert from per-game to total if needed)
        if 'minutes_played' in player_data.columns and 'games_played' in player_data.columns:
            # Check if it's per-game minutes and convert to total
            if player_data['minutes_played'].median() < 50:  # Likely per-game
                player_data['minutes_played'] = player_data['minutes_played'] * player_data['games_played']
        
        return player_data
    
    def _validate_feature_consistency(self):
        """Validate that both datasets have consistent features"""
        print("Validating feature consistency...")
        
        game_features = set(self.unified_game_data.columns)
        player_features = set(self.unified_player_data.columns)
        
        # Check if all unified features are present in both datasets
        missing_in_game = set(self.unified_features) - game_features
        missing_in_player = set(self.unified_features) - player_features
        
        if missing_in_game:
            print(f"⚠️  Missing features in game data: {missing_in_game}")
        
        if missing_in_player:
            print(f"⚠️  Missing features in player data: {missing_in_player}")
        
        # Show feature overlap
        common_features = game_features.intersection(player_features)
        print(f"✅ Common features: {len(common_features)}")
        print(f"✅ Unified features available: {len(set(self.unified_features).intersection(common_features))}")
    
    def save_unified_data(self):
        """Save unified datasets for use by both models"""
        print("Saving unified data...")
        
        # Save game-level data for Step 2
        game_output_path = self.output_dir / "unified_game_data.csv"
        self.unified_game_data.to_csv(game_output_path, index=False)
        print(f"✅ Game data saved to: {game_output_path}")
        
        # Save player-level data for Step 1
        player_output_path = self.output_dir / "unified_player_data.csv"
        self.unified_player_data.to_csv(player_output_path, index=False)
        print(f"✅ Player data saved to: {player_output_path}")
        
        # Save feature list for reference
        feature_list_path = self.output_dir / "unified_features.txt"
        with open(feature_list_path, 'w') as f:
            f.write("Unified Feature Set for Hierarchical WNBA Modeling\n")
            f.write("=" * 50 + "\n\n")
            for i, feature in enumerate(self.unified_features, 1):
                f.write(f"{i:2d}. {feature}\n")
        
        print(f"✅ Feature list saved to: {feature_list_path}")
    
    def create_player_to_game_mapping(self) -> pd.DataFrame:
        """Create mapping between players and games for hierarchical integration"""
        print("Creating player-to-game mapping...")
        
        # Create a mapping dataset that links players to games
        game_dates = self.unified_game_data[['date', 'team_abbreviation', 'game_id']].copy()
        player_info = self.unified_player_data[['player_name', 'team_abbreviation', 'season']].copy()
        
        # Create mapping based on team and season
        mapping_data = []
        
        for _, game in game_dates.iterrows():
            game_year = game['date'].year
            team = game['team_abbreviation']
            
            # Find players for this team in this season
            team_players = player_info[
                (player_info['team_abbreviation'] == team) & 
                (player_info['season'] == game_year)
            ]
            
            for _, player in team_players.iterrows():
                mapping_data.append({
                    'game_id': game['game_id'],
                    'date': game['date'],
                    'team_abbreviation': team,
                    'player_name': player['player_name'],
                    'season': game_year
                })
        
        mapping_df = pd.DataFrame(mapping_data)
        
        # Save mapping
        mapping_path = self.output_dir / "player_game_mapping.csv"
        mapping_df.to_csv(mapping_path, index=False)
        print(f"✅ Player-game mapping saved to: {mapping_path}")
        
        return mapping_df
    
    def run_pipeline(self):
        """Run the complete unified data pipeline"""
        print("🚀 Running Unified Data Pipeline...")
        print("=" * 50)
        
        # Load raw data
        self.load_raw_data()
        
        # Create unified features
        self.create_unified_features()
        
        # Save unified data
        self.save_unified_data()
        
        # Create player-to-game mapping
        self.create_player_to_game_mapping()
        
        print("\n✅ Unified Data Pipeline Complete!")
        print("=" * 50)
        print(f"Game data shape: {self.unified_game_data.shape}")
        print(f"Player data shape: {self.unified_player_data.shape}")
        print(f"Unified features: {len(self.unified_features)}")
        print(f"Output directory: {self.output_dir}")


def main():
    """Main function to run the unified data pipeline"""
    
    # Define data paths
    game_data_path = r"c:\Users\<USER>\Documents\HMNV_WNBA\step_02_game_totals_model\data\comprehensive_wnba_game_totals.csv"
    player_data_path = r"c:\Users\<USER>\Documents\HMNV_WNBA\consolidated_wnba\01_player_data\basic_stats\complete_real_wnba_features_with_metadata.csv"
    output_dir = r"c:\Users\<USER>\Documents\HMNV_WNBA\unified_data"
    
    # Create and run pipeline
    pipeline = UnifiedDataPipeline(
        game_data_path=game_data_path,
        player_data_path=player_data_path,
        output_dir=output_dir
    )
    
    pipeline.run_pipeline()


if __name__ == "__main__":
    main()
