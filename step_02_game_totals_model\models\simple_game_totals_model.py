"""
Simple Game Totals Model for testing
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl


class GameTotalsModel(pl.LightningModule):
    """Simple Game Totals Model"""
    
    def __init__(
        self,
        input_dim: int = 10,
        hidden_dims: list = [128, 64],
        dropout: float = 0.2,
        learning_rate: float = 1e-3
    ):
        super().__init__()
        self.save_hyperparameters()
        
        # Simple neural network
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, 1))
        
        self.net = nn.Sequential(*layers)
        self.loss_fn = nn.MSELoss()
    
    def forward(self, x):
        return self.net(x).squeeze(-1)
    
    def training_step(self, batch, batch_idx):
        features, targets, _, _, _ = batch
        predictions = self(features)
        loss = self.loss_fn(predictions, targets)
        self.log('train_loss', loss, prog_bar=True)
        return loss
    
    def validation_step(self, batch, batch_idx):
        features, targets, _, _, _ = batch
        predictions = self(features)
        loss = self.loss_fn(predictions, targets)
        self.log('val_loss', loss, prog_bar=True)
        return loss
    
    def test_step(self, batch, batch_idx):
        features, targets, _, _, _ = batch
        predictions = self(features)
        loss = self.loss_fn(predictions, targets)
        self.log('test_loss', loss)
        return loss
    
    def configure_optimizers(self):
        return torch.optim.AdamW(self.parameters(), lr=self.hparams.learning_rate)
