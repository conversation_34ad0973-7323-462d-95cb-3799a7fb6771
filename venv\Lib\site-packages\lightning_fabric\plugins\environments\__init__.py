# Copyright The Lightning AI team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from lightning_fabric.plugins.environments.cluster_environment import ClusterEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.kubeflow import KubeflowEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.lightning import LightningEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.lsf import LSFEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.mpi import MPIEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.slurm import SLURMEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.torchelastic import TorchElasticEnvironment  # noqa: F401
from lightning_fabric.plugins.environments.xla import XLAEnvironment  # noqa: F401
