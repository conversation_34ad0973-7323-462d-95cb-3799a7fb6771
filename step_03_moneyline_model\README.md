# WNBA Moneyline Model (Step 3)

This package implements the hierarchical moneyline prediction model that builds upon the Player Points Model (Step 1) and Game Totals Model (Step 2) to predict game winners with market-calibrated probabilities.

## 🏗️ Architecture

The moneyline model uses a sophisticated hierarchical approach:

1. **Hierarchical Integration**: Leverages predictions from the Game Totals Model
2. **Feature Extraction**: Separate offensive/defensive neural networks
3. **Market Calibration**: Platt scaling for production-ready probabilities
4. **Dynamic Weighting**: Learnable ensemble parameters

## 📊 Features

### Core Metrics (32 features)
- **Offensive**: `ortg_last5`, `efg_pct_last10`, `transition_eff`, etc.
- **Defensive**: `drtg_last5`, `rim_protection_eff`, `steal_rate_ema10`, etc.
- **Contextual**: `home_win_pct`, `rest_advantage`, `coaching_win_pct_diff`, etc.

### Advanced Components
- **Momentum Analysis**: Recent form weighting
- **Clutch Performance**: Late-game performance evaluation
- **Home Court Advantage**: Dynamic team-specific calculations

## 🎯 Performance Benchmarks

| Metric | Target | Alert Threshold |
|--------|--------|-----------------|
| Log Loss | < 0.62 | > 0.65 |
| AUC | > 0.72 | < 0.68 |
| Calibration Error | < 2.5% | > 4.0% |
| Accuracy | > 65% | < 60% |

## 🚀 Quick Start

### Demo Training
```bash
cd step_03_moneyline_model
python demo_training.py
```

### Production Training
```bash
python training/train_moneyline_fixed.py \
  --game_data_path ../unified_data/unified_game_data.csv \
  --totals_model_path ../step_02_game_totals_model/step_02_checkpoints/best_model.ckpt \
  --max_epochs 150 \
  --batch_size 64
```

## 📁 Directory Structure

```
step_03_moneyline_model/
├── models/
│   └── moneyline_model.py      # Core model architecture
├── data_processing/
│   └── moneyline_data_module.py # Data pipeline
├── training/
│   ├── train_moneyline_fixed.py # Production training
│   └── train_moneyline.py      # Original (with syntax issues)
├── demo_training.py            # Demo script
└── README.md                   # This file
```

## 🔧 Model Components

### MoneylineModel
- Hierarchical integration with totals model
- Market-calibrated probability outputs
- Separate offensive/defensive feature extraction

### MomentumEnsemble
- Combines full-season and recent form models
- Learnable ensemble weighting

### ClutchPerformanceModule
- Evaluates performance in close games
- Late-game situational analysis

## 📈 Integration Pipeline

1. **Player Points Model** → Individual player predictions
2. **Game Totals Model** → Team total points (uses player predictions)
3. **Moneyline Model** → Win probability (uses totals predictions)

## 🎲 Next Steps

1. **Market Integration**: Add real betting odds data
2. **Feature Engineering**: Implement advanced team metrics
3. **Ensemble Methods**: Combine multiple model approaches
4. **Real-time Updates**: Live game state integration
5. **Risk Management**: Kelly criterion position sizing

## 📊 Expected Integration

This model will integrate with:
- Steps 4-5: Player props and parlays
- Steps 6-7: Live betting and arbitrage
- Steps 8-9: Portfolio optimization and execution

## ⚡ Performance Notes

- Uses mixed precision training for speed
- Implements gradient clipping for stability
- Supports both CPU and GPU training
- Windows-compatible data loading (num_workers=0)
