#!/usr/bin/env python3
"""
Test script to verify Step 1 data module works with unified data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_processing.wnba_data_module import WNBADataModule

def test_unified_data_module():
    """Test that the data module properly uses unified data"""
    
    print("=" * 60)
    print("🧪 Testing Step 1 Data Module with Unified Data")
    print("=" * 60)
    
    # Create data module with unified data
    data_module = WNBADataModule(
        use_unified_data=True,
        batch_size=32
    )
    
    # Setup the data
    data_module.setup(stage="fit")
    
    # Check feature columns
    print(f"\n📊 Feature columns ({len(data_module.feature_columns)}):")
    for i, col in enumerate(data_module.feature_columns, 1):
        print(f"  {i:2d}. {col}")
    
    # Check expected unified features
    expected_features = [
        'points', 'rebounds', 'assists', 'steals', 'blocks',
        'field_goals_made', 'field_goals_attempted', 'field_goal_percentage',
        'three_pointers_made', 'three_pointers_attempted', 'three_point_percentage',
        'free_throws_made', 'free_throws_attempted', 'free_throw_percentage',
        'turnovers', 'minutes_played', 'games_played'
    ]
    
    print(f"\n🔍 Expected unified features ({len(expected_features)}):")
    for i, col in enumerate(expected_features, 1):
        print(f"  {i:2d}. {col}")
    
    # Check if all expected features are present
    missing_features = [f for f in expected_features if f not in data_module.feature_columns]
    extra_features = [f for f in data_module.feature_columns if f not in expected_features]
    
    if missing_features:
        print(f"\n❌ Missing features: {missing_features}")
    else:
        print(f"\n✅ All expected features present!")
    
    if extra_features:
        print(f"\n⚠️  Extra features: {extra_features}")
    else:
        print(f"\n✅ No extra features!")
    
    # Check dataset sizes
    print(f"\n📈 Dataset sizes:")
    print(f"  Training: {len(data_module.train_dataset)}")
    print(f"  Validation: {len(data_module.val_dataset)}")
    print(f"  Test: {len(data_module.test_dataset) if data_module.test_dataset else 0}")
    
    # Test a sample batch
    train_loader = data_module.train_dataloader()
    sample_batch = next(iter(train_loader))
    
    print(f"\n🎯 Sample batch:")
    print(f"  Features shape: {sample_batch[0].shape}")
    print(f"  Targets shape: {sample_batch[1].shape}")
    print(f"  Expected feature count: {len(expected_features)}")
    
    # Check if feature count matches
    if sample_batch[0].shape[1] == len(expected_features):
        print(f"\n✅ Feature count matches! ({sample_batch[0].shape[1]} features)")
    else:
        print(f"\n❌ Feature count mismatch! Got {sample_batch[0].shape[1]}, expected {len(expected_features)}")
    
    return len(missing_features) == 0 and len(extra_features) == 0 and sample_batch[0].shape[1] == len(expected_features)

if __name__ == "__main__":
    success = test_unified_data_module()
    
    if success:
        print(f"\n🎉 SUCCESS: Step 1 data module correctly uses unified data!")
        sys.exit(0)
    else:
        print(f"\n❌ FAILED: Step 1 data module has issues with unified data")
        sys.exit(1)
