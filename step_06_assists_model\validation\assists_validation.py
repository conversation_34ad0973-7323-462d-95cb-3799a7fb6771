"""
Assists Model Validation (Step 6 of 9) - Playmaking Validation
==============================================================

This module provides validation utilities for the assists prediction model:
- Assists prediction validation
- Usage ratio consistency checks
- Playmaking efficiency validation
- Teammate interaction validation

Key Components:
- AssistsValidator: Core validation for assists predictions
- PlaymakingValidator: Playmaking-specific validation
- UsageConsistencyValidator: Usage ratio consistency validation
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AssistsValidator:
    """
    Core validation for assists predictions.
    
    Validates assists predictions against expected ranges and patterns.
    """
    
    def __init__(self):
        self.validation_history = []
        
    def validate_predictions(self, predictions: np.ndarray, 
                           features: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, Any]:
        """Validate assists predictions"""
        
        validation_results = {}
        
        # Range validation
        range_validation = self._validate_prediction_range(predictions)
        validation_results['range_validation'] = range_validation
        
        # Distribution validation
        distribution_validation = self._validate_prediction_distribution(predictions)
        validation_results['distribution_validation'] = distribution_validation
        
        # Feature consistency validation
        if features:
            consistency_validation = self._validate_feature_consistency(predictions, features)
            validation_results['consistency_validation'] = consistency_validation
        
        # Overall validation score
        validation_results['overall_score'] = self._calculate_overall_score(validation_results)
        
        return validation_results
    
    def _validate_prediction_range(self, predictions: np.ndarray) -> Dict[str, Any]:
        """Validate prediction range"""
        
        # Expected range for assists: 0-15
        min_expected = 0.0
        max_expected = 15.0
        
        within_range = (predictions >= min_expected) & (predictions <= max_expected)
        range_compliance = np.mean(within_range)
        
        # Outlier detection
        outliers_low = predictions < min_expected
        outliers_high = predictions > max_expected
        
        return {
            'range_compliance': range_compliance,
            'outliers_low': np.sum(outliers_low),
            'outliers_high': np.sum(outliers_high),
            'min_prediction': np.min(predictions),
            'max_prediction': np.max(predictions)
        }
    
    def _validate_prediction_distribution(self, predictions: np.ndarray) -> Dict[str, Any]:
        """Validate prediction distribution"""
        
        # Statistical properties
        mean_pred = np.mean(predictions)
        std_pred = np.std(predictions)
        
        # Expected ranges for WNBA assists
        expected_mean_range = (2.0, 8.0)
        expected_std_range = (1.0, 4.0)
        
        mean_valid = expected_mean_range[0] <= mean_pred <= expected_mean_range[1]
        std_valid = expected_std_range[0] <= std_pred <= expected_std_range[1]
        
        # Zero predictions (should be reasonable)
        zero_predictions = np.sum(predictions < 0.5)
        zero_rate = zero_predictions / len(predictions)
        
        return {
            'mean_valid': mean_valid,
            'std_valid': std_valid,
            'mean_prediction': mean_pred,
            'std_prediction': std_pred,
            'zero_rate': zero_rate,
            'distribution_score': int(mean_valid) + int(std_valid)
        }
    
    def _validate_feature_consistency(self, predictions: np.ndarray, 
                                    features: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Validate predictions against feature consistency"""
        
        consistency_checks = {}
        
        # Usage rate consistency
        if 'usage_rate' in features:
            usage_consistency = self._check_usage_consistency(predictions, features['usage_rate'])
            consistency_checks['usage_consistency'] = usage_consistency
        
        # Minutes consistency
        if 'minutes_per_game' in features:
            minutes_consistency = self._check_minutes_consistency(predictions, features['minutes_per_game'])
            consistency_checks['minutes_consistency'] = minutes_consistency
        
        # Ball handling consistency
        if 'time_of_possession' in features:
            possession_consistency = self._check_possession_consistency(predictions, features['time_of_possession'])
            consistency_checks['possession_consistency'] = possession_consistency
        
        return consistency_checks
    
    def _check_usage_consistency(self, predictions: np.ndarray, usage_rate: np.ndarray) -> Dict[str, float]:
        """Check consistency with usage rate"""
        
        # Assist-to-usage ratio should be reasonable
        assist_to_usage = predictions / (usage_rate + 1e-6)
        
        # Expected range: 0.1 to 2.0
        reasonable_ratio = (assist_to_usage >= 0.1) & (assist_to_usage <= 2.0)
        consistency_rate = np.mean(reasonable_ratio)
        
        # Correlation should be positive
        correlation = np.corrcoef(predictions, usage_rate)[0, 1]
        correlation = correlation if not np.isnan(correlation) else 0.0
        
        return {
            'consistency_rate': consistency_rate,
            'correlation': correlation,
            'mean_ratio': np.mean(assist_to_usage)
        }
    
    def _check_minutes_consistency(self, predictions: np.ndarray, minutes: np.ndarray) -> Dict[str, float]:
        """Check consistency with minutes played"""
        
        # Assists per minute should be reasonable
        assists_per_minute = predictions / (minutes + 1e-6)
        
        # Expected range: 0.0 to 0.5 assists per minute
        reasonable_rate = assists_per_minute <= 0.5
        consistency_rate = np.mean(reasonable_rate)
        
        # Correlation should be positive
        correlation = np.corrcoef(predictions, minutes)[0, 1]
        correlation = correlation if not np.isnan(correlation) else 0.0
        
        return {
            'consistency_rate': consistency_rate,
            'correlation': correlation,
            'mean_assists_per_minute': np.mean(assists_per_minute)
        }
    
    def _check_possession_consistency(self, predictions: np.ndarray, possession_time: np.ndarray) -> Dict[str, float]:
        """Check consistency with possession time"""
        
        # Assists per possession time should be reasonable
        assists_per_possession = predictions / (possession_time + 1e-6)
        
        # Expected range: 0.0 to 2.0 assists per possession time unit
        reasonable_rate = assists_per_possession <= 2.0
        consistency_rate = np.mean(reasonable_rate)
        
        # Correlation should be positive
        correlation = np.corrcoef(predictions, possession_time)[0, 1]
        correlation = correlation if not np.isnan(correlation) else 0.0
        
        return {
            'consistency_rate': consistency_rate,
            'correlation': correlation,
            'mean_assists_per_possession': np.mean(assists_per_possession)
        }
    
    def _calculate_overall_score(self, validation_results: Dict[str, Any]) -> float:
        """Calculate overall validation score"""
        
        score = 0.0
        weight_sum = 0.0
        
        # Range validation (weight: 0.3)
        if 'range_validation' in validation_results:
            range_score = validation_results['range_validation']['range_compliance']
            score += range_score * 0.3
            weight_sum += 0.3
        
        # Distribution validation (weight: 0.3)
        if 'distribution_validation' in validation_results:
            dist_score = validation_results['distribution_validation']['distribution_score'] / 2.0
            score += dist_score * 0.3
            weight_sum += 0.3
        
        # Consistency validation (weight: 0.4)
        if 'consistency_validation' in validation_results:
            consistency_scores = []
            for key, consistency in validation_results['consistency_validation'].items():
                if isinstance(consistency, dict) and 'consistency_rate' in consistency:
                    consistency_scores.append(consistency['consistency_rate'])
            
            if consistency_scores:
                consistency_score = np.mean(consistency_scores)
                score += consistency_score * 0.4
                weight_sum += 0.4
        
        return score / weight_sum if weight_sum > 0 else 0.0


class PlaymakingValidator:
    """
    Playmaking-specific validation utilities.
    
    Validates playmaking efficiency and effectiveness metrics.
    """
    
    def __init__(self):
        self.validation_history = []
        
    def validate_playmaking_efficiency(self, predictions: np.ndarray, 
                                     features: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Validate playmaking efficiency metrics"""
        
        validation_results = {}
        
        # Assist rate validation
        if 'assist_rate_ema10' in features:
            assist_rate_validation = self._validate_assist_rate(predictions, features['assist_rate_ema10'])
            validation_results['assist_rate_validation'] = assist_rate_validation
        
        # Assist-to-usage ratio validation
        if 'usage_rate' in features:
            ratio_validation = self._validate_assist_usage_ratio(predictions, features['usage_rate'])
            validation_results['ratio_validation'] = ratio_validation
        
        # Playmaking context validation
        context_validation = self._validate_playmaking_context(predictions, features)
        validation_results['context_validation'] = context_validation
        
        return validation_results
    
    def _validate_assist_rate(self, predictions: np.ndarray, assist_rate: np.ndarray) -> Dict[str, float]:
        """Validate assist rate consistency"""
        
        # Correlation between predictions and assist rate
        correlation = np.corrcoef(predictions, assist_rate)[0, 1]
        correlation = correlation if not np.isnan(correlation) else 0.0
        
        # Expected strong positive correlation
        strong_correlation = correlation > 0.5
        
        return {
            'correlation': correlation,
            'strong_correlation': strong_correlation,
            'validation_score': correlation if correlation > 0 else 0.0
        }
    
    def _validate_assist_usage_ratio(self, predictions: np.ndarray, usage_rate: np.ndarray) -> Dict[str, float]:
        """Validate assist-to-usage ratio"""
        
        ratio = predictions / (usage_rate + 1e-6)
        
        # Expected ratio ranges for different player types
        guard_ratio_range = (0.4, 1.5)  # Guards typically higher
        forward_ratio_range = (0.1, 0.8)  # Forwards typically lower
        
        # Assume mixed population, so broader range
        reasonable_ratio = (ratio >= 0.1) & (ratio <= 1.5)
        ratio_validity = np.mean(reasonable_ratio)
        
        return {
            'ratio_validity': ratio_validity,
            'mean_ratio': np.mean(ratio),
            'std_ratio': np.std(ratio)
        }
    
    def _validate_playmaking_context(self, predictions: np.ndarray, 
                                   features: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Validate playmaking context"""
        
        context_checks = {}
        
        # Ball handling features
        ball_handling_features = ['time_of_possession', 'dribbles_per_possession', 'passes_per_possession']
        for feature in ball_handling_features:
            if feature in features:
                correlation = np.corrcoef(predictions, features[feature])[0, 1]
                correlation = correlation if not np.isnan(correlation) else 0.0
                context_checks[f'{feature}_correlation'] = correlation
        
        # Teammate features
        teammate_features = ['teammate_fg_pct', 'teammate_3p_pct']
        for feature in teammate_features:
            if feature in features:
                correlation = np.corrcoef(predictions, features[feature])[0, 1]
                correlation = correlation if not np.isnan(correlation) else 0.0
                context_checks[f'{feature}_correlation'] = correlation
        
        return context_checks


class UsageConsistencyValidator:
    """
    Usage ratio consistency validation.
    
    Validates consistency between usage patterns and assist predictions.
    """
    
    def __init__(self):
        self.validation_history = []
        
    def validate_usage_consistency(self, predictions: np.ndarray, 
                                 usage_features: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Validate usage consistency across multiple features"""
        
        consistency_results = {}
        
        # Core usage validation
        if 'usage_rate' in usage_features:
            core_validation = self._validate_core_usage(predictions, usage_features['usage_rate'])
            consistency_results['core_validation'] = core_validation
        
        # Time-based validation
        time_features = ['minutes_per_game', 'time_of_possession']
        time_validation = self._validate_time_consistency(predictions, usage_features, time_features)
        consistency_results['time_validation'] = time_validation
        
        # Possession-based validation
        possession_features = ['touches_per_possession', 'passes_per_possession']
        possession_validation = self._validate_possession_consistency(predictions, usage_features, possession_features)
        consistency_results['possession_validation'] = possession_validation
        
        return consistency_results
    
    def _validate_core_usage(self, predictions: np.ndarray, usage_rate: np.ndarray) -> Dict[str, float]:
        """Validate core usage rate consistency"""
        
        # Binned analysis
        usage_bins = np.percentile(usage_rate, [25, 50, 75])
        
        low_usage = usage_rate <= usage_bins[0]
        med_usage = (usage_rate > usage_bins[0]) & (usage_rate <= usage_bins[1])
        high_usage = usage_rate > usage_bins[1]
        
        # Expected assist patterns
        low_assists = np.mean(predictions[low_usage])
        med_assists = np.mean(predictions[med_usage])
        high_assists = np.mean(predictions[high_usage])
        
        # Monotonic relationship expected
        monotonic = (low_assists <= med_assists) and (med_assists <= high_assists)
        
        return {
            'monotonic_relationship': monotonic,
            'low_usage_assists': low_assists,
            'med_usage_assists': med_assists,
            'high_usage_assists': high_assists
        }
    
    def _validate_time_consistency(self, predictions: np.ndarray, features: Dict[str, np.ndarray], 
                                 time_features: List[str]) -> Dict[str, float]:
        """Validate time-based consistency"""
        
        time_correlations = {}
        
        for feature in time_features:
            if feature in features:
                correlation = np.corrcoef(predictions, features[feature])[0, 1]
                correlation = correlation if not np.isnan(correlation) else 0.0
                time_correlations[f'{feature}_correlation'] = correlation
        
        # Overall time consistency
        correlations = list(time_correlations.values())
        overall_consistency = np.mean(correlations) if correlations else 0.0
        
        time_correlations['overall_consistency'] = overall_consistency
        
        return time_correlations
    
    def _validate_possession_consistency(self, predictions: np.ndarray, features: Dict[str, np.ndarray], 
                                       possession_features: List[str]) -> Dict[str, float]:
        """Validate possession-based consistency"""
        
        possession_correlations = {}
        
        for feature in possession_features:
            if feature in features:
                correlation = np.corrcoef(predictions, features[feature])[0, 1]
                correlation = correlation if not np.isnan(correlation) else 0.0
                possession_correlations[f'{feature}_correlation'] = correlation
        
        # Overall possession consistency
        correlations = list(possession_correlations.values())
        overall_consistency = np.mean(correlations) if correlations else 0.0
        
        possession_correlations['overall_consistency'] = overall_consistency
        
        return possession_correlations


def validate_assists_model(predictions: np.ndarray, features: Dict[str, np.ndarray]) -> Dict[str, Any]:
    """
    Comprehensive assists model validation.
    
    Args:
        predictions: Predicted assist values
        features: Feature dictionary for validation
        
    Returns:
        Dictionary containing all validation results
    """
    
    # Initialize validators
    assists_validator = AssistsValidator()
    playmaking_validator = PlaymakingValidator()
    usage_validator = UsageConsistencyValidator()
    
    # Core validation
    core_validation = assists_validator.validate_predictions(predictions, features)
    
    # Playmaking validation
    playmaking_validation = playmaking_validator.validate_playmaking_efficiency(predictions, features)
    
    # Usage consistency validation
    usage_validation = usage_validator.validate_usage_consistency(predictions, features)
    
    # Combine results
    validation_results = {
        'core_validation': core_validation,
        'playmaking_validation': playmaking_validation,
        'usage_validation': usage_validation,
        'timestamp': pd.Timestamp.now()
    }
    
    return validation_results
