"""
🧪 Critical Validation Checks for Player Points Model

This module implements post-training validation to ensure player predictions
sum to realistic team totals and maintain basketball logic consistency.
"""

import torch
import numpy as np
import pandas as pd
from collections import defaultdict
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt
import seaborn as sns


def validate_player_consistency(model, dataloader, device='cpu'):
    """
    Post-training validation: Ensure player sums ≈ team totals
    
    Args:
        model: Trained PlayerPointsModel
        dataloader: DataLoader with validation data
        device: Device to run inference on
    
    Returns:
        Dict with validation metrics and analysis
    """
    model.eval()
    
    # Ensure device is a torch.device
    if isinstance(device, str):
        device = torch.device(device)
    elif not isinstance(device, torch.device):
        device = torch.device('cpu')
    
    model = model.to(device)
    
    # Collect predictions and actuals by game/team
    team_actual = defaultdict(list)
    team_pred = defaultdict(list)
    player_data = []
    
    with torch.no_grad():
        for batch in dataloader:
            if len(batch) == 4:
                features, points, game_ids, team_ids = batch
            else:
                features, points = batch
                # Generate dummy IDs if not provided
                game_ids = torch.arange(len(points))
                team_ids = torch.zeros_like(game_ids)
            
            features = features.to(device)
            pred = model(features)
            
            # Convert to numpy for processing
            points_np = points.cpu().numpy()
            pred_np = pred.cpu().numpy()
            game_ids_np = game_ids.cpu().numpy()
            team_ids_np = team_ids.cpu().numpy()
            
            # Group by game and team
            for i in range(len(points_np)):
                game_team_key = f"{game_ids_np[i]}_{team_ids_np[i]}"
                team_actual[game_team_key].append(points_np[i])
                team_pred[game_team_key].append(pred_np[i])
                
                player_data.append({
                    'game_id': game_ids_np[i],
                    'team_id': team_ids_np[i],
                    'actual_points': points_np[i],
                    'pred_points': pred_np[i],
                    'error': abs(pred_np[i] - points_np[i])
                })
    
    # Calculate team-level metrics
    team_metrics = calculate_team_level_metrics(team_actual, team_pred)
    
    # Calculate player-level metrics
    player_metrics = calculate_player_level_metrics(player_data)
    
    # Generate validation report
    validation_report = generate_validation_report(team_metrics, player_metrics, player_data)
    
    return {
        'team_metrics': team_metrics,
        'player_metrics': player_metrics,
        'validation_report': validation_report,
        'player_data': player_data
    }


def calculate_team_level_metrics(team_actual: Dict, team_pred: Dict) -> Dict:
    """Calculate team-level validation metrics"""
    team_maes = {}
    team_totals_actual = {}
    team_totals_pred = {}
    
    for game_team_key in team_actual:
        actual_sum = sum(team_actual[game_team_key])
        pred_sum = sum(team_pred[game_team_key])
        
        team_totals_actual[game_team_key] = actual_sum
        team_totals_pred[game_team_key] = pred_sum
        team_maes[game_team_key] = abs(actual_sum - pred_sum)
    
    # Overall team metrics
    overall_team_mae = np.mean(list(team_maes.values()))
    team_mae_std = np.std(list(team_maes.values()))
    
    # Team total correlation
    actual_totals = list(team_totals_actual.values())
    pred_totals = list(team_totals_pred.values())
    team_correlation = np.corrcoef(actual_totals, pred_totals)[0, 1]
    
    return {
        'team_mae_mean': overall_team_mae,
        'team_mae_std': team_mae_std,
        'team_correlation': team_correlation,
        'team_maes': team_maes,
        'team_totals_actual': team_totals_actual,
        'team_totals_pred': team_totals_pred
    }


def calculate_player_level_metrics(player_data: List[Dict]) -> Dict:
    """Calculate player-level validation metrics"""
    df = pd.DataFrame(player_data)
    
    # Overall player metrics
    player_mae = df['error'].mean()
    player_mae_std = df['error'].std()
    player_rmse = np.sqrt(np.mean(df['error'] ** 2))
    
    # Correlation
    player_correlation = df['actual_points'].corr(df['pred_points'])
    
    # Performance by point ranges
    df['point_range'] = pd.cut(df['actual_points'], 
                              bins=[0, 5, 10, 15, 20, 30, 100], 
                              labels=['0-5', '6-10', '11-15', '16-20', '21-30', '30+'])
    
    range_metrics = df.groupby('point_range')['error'].agg(['mean', 'std', 'count']).to_dict()
    
    return {
        'player_mae': player_mae,
        'player_mae_std': player_mae_std,
        'player_rmse': player_rmse,
        'player_correlation': player_correlation,
        'range_metrics': range_metrics
    }


def generate_validation_report(team_metrics: Dict, player_metrics: Dict, player_data: List[Dict]) -> str:
    """Generate a comprehensive validation report"""
    
    report = f"""
🏀 WNBA Player Points Model Validation Report
============================================

📊 TEAM-LEVEL VALIDATION
------------------------
Team Total MAE: {team_metrics['team_mae_mean']:.2f} ± {team_metrics['team_mae_std']:.2f} points
Team Correlation: {team_metrics['team_correlation']:.3f}

Target: Team Total MAE < 8.0 points
Status: {'✅ PASS' if team_metrics['team_mae_mean'] < 8.0 else '❌ FAIL'}

📈 PLAYER-LEVEL VALIDATION  
--------------------------
Player MAE: {player_metrics['player_mae']:.2f} ± {player_metrics['player_mae_std']:.2f} points
Player RMSE: {player_metrics['player_rmse']:.2f} points
Player Correlation: {player_metrics['player_correlation']:.3f}

Target: Player MAE < 2.5 points for rotation players
Status: {'✅ PASS' if player_metrics['player_mae'] < 2.5 else '❌ FAIL'}

🎯 PERFORMANCE BY POINT RANGE
-----------------------------"""
    
    # Add range-specific metrics
    for point_range, metrics in player_metrics['range_metrics']['mean'].items():
        count = player_metrics['range_metrics']['count'][point_range]
        std = player_metrics['range_metrics']['std'][point_range]
        report += f"\n{point_range} points: MAE {metrics:.2f} ± {std:.2f} (n={count})"
    
    report += f"""

🔍 BASKETBALL LOGIC CHECKS
--------------------------
Total Games Analyzed: {len(set([d['game_id'] for d in player_data]))}
Total Players Analyzed: {len(player_data)}

📋 RECOMMENDATIONS
------------------"""
    
    if team_metrics['team_mae_mean'] > 8.0:
        report += "\n• Team totals too high - consider ensemble calibration"
    
    if player_metrics['player_mae'] > 2.5:
        report += "\n• Player accuracy needs improvement - check feature engineering"
    
    if player_metrics['player_correlation'] < 0.7:
        report += "\n• Low correlation - model may be underfitting"
    
    if team_metrics['team_mae_mean'] < 8.0 and player_metrics['player_mae'] < 2.5:
        report += "\n✅ Model meets all validation benchmarks!"
    
    return report


def plot_validation_results(validation_results: Dict, save_path: str = None):
    """Create visualization plots for validation results"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot 1: Team totals actual vs predicted
    team_actual = list(validation_results['team_metrics']['team_totals_actual'].values())
    team_pred = list(validation_results['team_metrics']['team_totals_pred'].values())
    
    axes[0, 0].scatter(team_actual, team_pred, alpha=0.6)
    axes[0, 0].plot([min(team_actual), max(team_actual)], 
                    [min(team_actual), max(team_actual)], 'r--', lw=2)
    axes[0, 0].set_xlabel('Actual Team Points')
    axes[0, 0].set_ylabel('Predicted Team Points')
    axes[0, 0].set_title('Team Totals: Actual vs Predicted')
    
    # Plot 2: Player points actual vs predicted
    df = pd.DataFrame(validation_results['player_data'])
    axes[0, 1].scatter(df['actual_points'], df['pred_points'], alpha=0.6)
    axes[0, 1].plot([df['actual_points'].min(), df['actual_points'].max()], 
                    [df['actual_points'].min(), df['actual_points'].max()], 'r--', lw=2)
    axes[0, 1].set_xlabel('Actual Player Points')
    axes[0, 1].set_ylabel('Predicted Player Points')
    axes[0, 1].set_title('Player Points: Actual vs Predicted')
    
    # Plot 3: Error distribution
    axes[1, 0].hist(df['error'], bins=30, alpha=0.7, edgecolor='black')
    axes[1, 0].set_xlabel('Prediction Error (|pred - actual|)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('Distribution of Prediction Errors')
    
    # Plot 4: Team MAE distribution
    team_maes = list(validation_results['team_metrics']['team_maes'].values())
    axes[1, 1].hist(team_maes, bins=20, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('Team Total MAE')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Distribution of Team Total Errors')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig


def check_basketball_logic(validation_results: Dict) -> Dict:
    """
    Perform basketball-specific logic checks
    
    Returns:
        Dict with logic check results
    """
    df = pd.DataFrame(validation_results['player_data'])
    
    logic_checks = {}
    
    # Check 1: No negative predictions
    negative_preds = (df['pred_points'] < 0).sum()
    logic_checks['negative_predictions'] = {
        'count': negative_preds,
        'pass': negative_preds == 0
    }
    
    # Check 2: Reasonable maximum predictions (< 50 points)
    high_preds = (df['pred_points'] > 50).sum()
    logic_checks['unrealistic_high_predictions'] = {
        'count': high_preds,
        'pass': high_preds == 0
    }
    
    # Check 3: Team totals in reasonable range (40-120 points)
    team_totals = list(validation_results['team_metrics']['team_totals_pred'].values())
    unrealistic_team_totals = sum(1 for total in team_totals if total < 40 or total > 120)
    logic_checks['unrealistic_team_totals'] = {
        'count': unrealistic_team_totals,
        'pass': unrealistic_team_totals == 0
    }
    
    return logic_checks
