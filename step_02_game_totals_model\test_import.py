#!/usr/bin/env python3
"""
Test script to debug the import issue
"""

import sys
import traceback

print("=== Testing GameTotalsModel Import ===")

try:
    print("1. Testing basic imports...")
    import torch
    import torch.nn as nn
    import pytorch_lightning as pl
    print("   ✅ Basic imports successful")
    
    print("2. Testing module import...")
    import models.game_totals_model as gtm_module
    print("   ✅ Module imported")
    
    print("3. Checking module contents...")
    module_contents = [x for x in dir(gtm_module) if not x.startswith('_')]
    print(f"   Module contents: {module_contents}")
    
    print("4. Testing class import...")
    if 'GameTotalsModel' in module_contents:
        from models.game_totals_model import GameTotalsModel
        print("   ✅ GameTotalsModel imported successfully")
        
        print("5. Testing instantiation...")
        model = GameTotalsModel(input_dim=10)
        print("   ✅ Model created successfully")
        
    else:
        print("   ❌ GameTotalsModel not found in module")
        print("   Available classes:")
        for attr in module_contents:
            obj = getattr(gtm_module, attr)
            if isinstance(obj, type):
                print(f"     - {attr}: {obj}")

except Exception as e:
    print(f"❌ Error: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
