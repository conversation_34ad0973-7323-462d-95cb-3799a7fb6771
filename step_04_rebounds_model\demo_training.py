"""
Demo Training Script for Player Rebounds Model (Step 4 of 9)
============================================================

This script demonstrates the rebounds model training with a simplified setup
for testing and validation purposes.

Features:
- Quick model validation
- Sample data generation
- Basic training loop
- Performance verification
"""

import os
import sys
from pathlib import Path
import logging
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import numpy as np
import pandas as pd
from typing import Dict, Any

# Import model and data modules
from models.player_rebounds_model import PlayerReboundsModel
from data_processing.rebound_data_module import ReboundDataModule
from utils.rebound_metrics import ReboundMetrics, ReboundBenchmarks

logger = logging.getLogger(__name__)


def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def create_demo_data():
    """Create demonstration data for testing."""
    logger.info("Creating demo data...")
    
    # Create a temporary data directory
    data_dir = Path("demo_data")
    data_dir.mkdir(exist_ok=True)
    
    # Generate sample data
    np.random.seed(42)
    n_samples = 500
    
    # Position mapping
    positions = ['PG', 'SG', 'SF', 'PF', 'C']
    position_weights = [0.15, 0.15, 0.2, 0.25, 0.25]
    
    data = {
        'PLAYER_ID': np.random.randint(1, 50, n_samples),
        'TEAM_ID': np.random.randint(1, 13, n_samples),
        'GAME_DATE': pd.date_range('2023-01-01', periods=n_samples, freq='D')[:n_samples],
        'POSITION': np.random.choice(positions, n_samples, p=position_weights),
        'MIN': np.random.uniform(10, 40, n_samples),
        'FGA': np.random.poisson(8, n_samples),
        'FG3A': np.random.poisson(3, n_samples),
        'FTA': np.random.poisson(2, n_samples),
        'DREB': np.random.poisson(4, n_samples),
        'OREB': np.random.poisson(1, n_samples),
        'STL': np.random.poisson(1, n_samples),
        'BLK': np.random.poisson(0.5, n_samples),
        'HEIGHT': np.random.normal(72, 4, n_samples),
        'PACE': np.random.uniform(95, 105, n_samples),
        # Opponent features
        'OPP_DREB': np.random.poisson(35, n_samples),
        'OPP_OREB': np.random.poisson(10, n_samples),
        'OPP_FGA': np.random.poisson(85, n_samples),
        'OPP_HEIGHT_AVG': np.random.normal(72, 2, n_samples),
        'OPP_PACE': np.random.uniform(95, 105, n_samples),
        'OPP_STL': np.random.poisson(7, n_samples),
        'OPP_BLK': np.random.poisson(4, n_samples),
        'OPP_MIN': np.random.uniform(200, 250, n_samples)
    }
    
    df = pd.DataFrame(data)
    df['REB'] = df['DREB'] + df['OREB']
    
    # Adjust rebounds based on position (Centers get more rebounds)
    position_multipliers = {'PG': 0.6, 'SG': 0.7, 'SF': 0.9, 'PF': 1.3, 'C': 1.6}
    for pos, mult in position_multipliers.items():
        mask = df['POSITION'] == pos
        df.loc[mask, 'REB'] = (df.loc[mask, 'REB'] * mult).round().astype(int)
        df.loc[mask, 'DREB'] = (df.loc[mask, 'DREB'] * mult).round().astype(int)
        df.loc[mask, 'OREB'] = (df.loc[mask, 'OREB'] * mult * 0.6).round().astype(int)
    
    # Save demo data
    demo_file = data_dir / 'demo_rebounds_data.csv'
    df.to_csv(demo_file, index=False)
    
    logger.info(f"Demo data created: {demo_file}")
    logger.info(f"Data shape: {df.shape}")
    logger.info(f"Rebound distribution: {df['REB'].describe()}")
    
    return str(data_dir)


def demo_training():
    """Run a demo training session."""
    logger.info("Starting demo training...")
    
    # Set random seeds
    pl.seed_everything(42)
    
    # Create demo data
    data_path = create_demo_data()
    
    # Setup data module
    data_module = ReboundDataModule(
        data_path=data_path,
        batch_size=16,
        num_workers=0,  # Disable multiprocessing for demo
        val_split=0.2,
        test_split=0.1,
        target_column='REB',
        position_column='POSITION',
        min_minutes=10.0
    )
    
    # Setup data module
    data_module.setup()
    
    # Get feature dimensions
    feature_info = data_module.get_feature_info()
    logger.info(f"Feature dimensions: {feature_info['n_features']}")
    logger.info(f"Opponent feature dimensions: {feature_info['n_opponent_features']}")
    
    # Setup model
    model = PlayerReboundsModel(
        input_dim=feature_info['n_features'],
        position_embedding_dim=32,
        opponent_dim=feature_info['n_opponent_features'],
        hidden_dim=64,  # Smaller for demo
        learning_rate=0.001,
        weight_decay=0.01,
        dropout_rate=0.2
    )
    
    logger.info("Model initialized successfully")
    
    # Setup trainer
    checkpoint_callback = ModelCheckpoint(
        dirpath='demo_checkpoints',
        filename='demo-rebounds-{epoch:02d}-{val_loss:.2f}',
        save_top_k=1,
        monitor='val_loss',
        mode='min'
    )
    
    early_stop_callback = EarlyStopping(
        monitor='val_loss',
        patience=5,
        min_delta=0.001,
        mode='min'
    )
    
    trainer = pl.Trainer(
        max_epochs=10,  # Short demo training
        callbacks=[checkpoint_callback, early_stop_callback],
        enable_progress_bar=True,
        log_every_n_steps=5,
        deterministic=True
    )
    
    # Train model
    logger.info("Starting training...")
    trainer.fit(model, data_module)
    
    # Test model
    logger.info("Testing model...")
    test_results = trainer.test(model, data_module)
    
    # Evaluation
    logger.info("Running evaluation...")
    model.eval()
    test_dataloader = data_module.test_dataloader()
    
    all_predictions = []
    all_targets = []
    all_positions = []
    all_gate_probs = []
    all_poisson_rates = []
    
    with torch.no_grad():
        for batch in test_dataloader:
            features = batch['features']
            positions = batch['position_indices']
            opponent_features = batch['opponent_features']
            targets = batch['rebounds']
            
            # Get predictions
            gate_probs, poisson_rates = model(features, positions, opponent_features)
            predictions = (1 - gate_probs) * poisson_rates
            
            all_predictions.append(predictions.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
            all_positions.append(positions.cpu().numpy())
            all_gate_probs.append(gate_probs.cpu().numpy())
            all_poisson_rates.append(poisson_rates.cpu().numpy())
    
    # Concatenate results
    all_predictions = np.concatenate(all_predictions)
    all_targets = np.concatenate(all_targets)
    all_positions = np.concatenate(all_positions)
    all_gate_probs = np.concatenate(all_gate_probs)
    all_poisson_rates = np.concatenate(all_poisson_rates)
    
    # Comprehensive evaluation
    metrics = ReboundMetrics()
    evaluation_results = metrics.comprehensive_evaluation(
        y_true=all_targets.flatten(),
        y_pred=all_predictions.flatten(),
        positions=all_positions.flatten(),
        gate_probs=all_gate_probs.flatten(),
        poisson_rates=all_poisson_rates.flatten()
    )
    
    # Benchmark comparison
    benchmarks = ReboundBenchmarks()
    benchmark_results = benchmarks.benchmark_comparison(
        y_true=all_targets.flatten(),
        y_pred=all_predictions.flatten(),
        positions=all_positions.flatten()
    )
    
    # Performance summary
    summary = benchmarks.performance_summary(evaluation_results)
    
    logger.info("Demo Training Complete!")
    logger.info("="*60)
    logger.info(summary)
    logger.info("="*60)
    
    # Print some key metrics
    std_metrics = evaluation_results['standard']
    zip_metrics = evaluation_results['zero_inflated']
    
    print("\n" + "="*50)
    print("DEMO TRAINING RESULTS")
    print("="*50)
    print(f"Test Loss: {test_results[0]['test_loss']:.4f}")
    print(f"MAE: {std_metrics['mae']:.3f}")
    print(f"RMSE: {std_metrics['rmse']:.3f}")
    print(f"R²: {std_metrics['r2']:.3f}")
    print(f"Zero Rate (True): {zip_metrics['observed_zero_rate']:.3f}")
    print(f"Zero Rate (Pred): {zip_metrics['predicted_zero_rate']:.3f}")
    print(f"Zero Accuracy: {zip_metrics['zero_accuracy']:.3f}")
    print("="*50)
    
    # Test prediction functionality
    logger.info("Testing prediction functionality...")
    
    # Get a sample batch
    sample_batch = next(iter(data_module.test_dataloader()))
    sample_features = sample_batch['features'][:5]  # First 5 samples
    sample_positions = sample_batch['position_indices'][:5]
    sample_opponent = sample_batch['opponent_features'][:5]
    sample_targets = sample_batch['rebounds'][:5]
    
    # Generate predictions with uncertainty
    pred_results = model.predict_rebounds(sample_features, sample_positions, sample_opponent)
    
    print("\nSample Predictions:")
    print("-" * 40)
    for i in range(5):
        print(f"Sample {i+1}:")
        print(f"  True: {sample_targets[i].item():.1f}")
        print(f"  Predicted: {pred_results['expected_rebounds'][i].item():.2f}")
        print(f"  Std: {pred_results['std'][i].item():.2f}")
        print(f"  Gate Prob: {pred_results['gate_prob'][i].item():.3f}")
        print(f"  Poisson Rate: {pred_results['rate'][i].item():.2f}")
        print()
    
    logger.info("Demo training completed successfully!")
    
    # Cleanup
    import shutil
    if Path("demo_data").exists():
        shutil.rmtree("demo_data")
    
    return evaluation_results, benchmark_results


def main():
    """Main demo function."""
    setup_logging()
    
    logger.info("Starting Player Rebounds Model Demo...")
    
    try:
        evaluation_results, benchmark_results = demo_training()
        
        logger.info("Demo completed successfully!")
        print("\n✅ Player Rebounds Model demo training completed successfully!")
        print("🎯 Model is ready for full training with real data.")
        
    except Exception as e:
        logger.error(f"Demo training failed: {e}")
        print(f"\n❌ Demo training failed: {e}")
        raise


if __name__ == "__main__":
    main()
