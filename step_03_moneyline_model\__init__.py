"""
🏀 WNBA Moneyline Model (Step 3 of 9)

This package implements the hierarchical moneyline prediction model that builds upon
the Player Points Model (Step 1) and Game Totals Model (Step 2) to predict game winners.

Key Features:
- Hierarchical integration with Steps 1 & 2
- Market-calibrated probability predictions
- Dynamic home court advantage
- Momentum-weighted ensemble learning
- Clutch performance analysis
- Real-time feature drift detection
"""

__version__ = "1.0.0"
__author__ = "HMNV_WNBA Project"
