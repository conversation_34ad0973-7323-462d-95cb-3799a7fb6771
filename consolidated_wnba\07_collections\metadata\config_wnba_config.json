{"_meta": {"description": "WNBA model configuration for Medusa Neural Vault. Defines model ensemble, features, data sources, and integration points for WNBA analytics and prediction workflows.", "version": "1.0.0", "last_reviewed": "2025-06-26", "maintainer": "Medusa Engineering Team"}, "ensemble": {"models": [{"name": "wnba_xgboost_v1", "type": "xgboost", "description": "Primary XGBoost model for WNBA game outcome prediction.", "features": ["team_stats", "player_stats", "recent_performance", "injury_report", "odds_data"], "data_source": "wnba_api", "monitoring": {"drift_detection": true, "alert_threshold": 0.05}}, {"name": "wnba_nn_v2", "type": "neural_network", "description": "Deep learning model for advanced WNBA analytics.", "features": ["player_embeddings", "game_context", "historical_trends"], "data_source": "custom_pipeline", "monitoring": {"drift_detection": true, "alert_threshold": 0.03}}], "voting_strategy": "weighted", "integration_points": ["backend.services.wnba_predictor", "api.wnba_endpoints"], "business_value": "Delivers actionable WNBA predictions and analytics for betting, fan engagement, and business intelligence."}, "data_sources": [{"name": "wnba_api", "type": "REST", "description": "Official WNBA stats API for real-time and historical data."}, {"name": "custom_pipeline", "type": "internal", "description": "Medusa proprietary data pipeline for enriched analytics."}], "monitoring": {"enabled": true, "alerting": true, "metrics": ["model_accuracy", "prediction_latency", "data_drift"]}, "review_date": "2025-12-01"}