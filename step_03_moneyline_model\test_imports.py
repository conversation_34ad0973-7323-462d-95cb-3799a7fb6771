"""
🔍 Step 3 Import Test
Test script to verify all imports work correctly for Step 3 Moneyline Model
"""

import sys
import os
from pathlib import Path

def setup_imports():
    """Setup correct import paths for Step 3"""
    # Get current directory and parent directories
    current_dir = Path(__file__).parent
    step2_dir = current_dir.parent / "step_02_game_totals_model"
    step1_dir = current_dir.parent / "step_01_player_points_model"
    
    # Add paths to sys.path
    sys.path.insert(0, str(step2_dir))
    sys.path.insert(0, str(step1_dir))
    sys.path.insert(0, str(current_dir))
    
    # Add models directory specifically
    sys.path.insert(0, str(step2_dir / "models"))
    
    print(f"✅ Added Step 2 path: {step2_dir}")
    print(f"✅ Added Step 1 path: {step1_dir}")
    print(f"✅ Added current path: {current_dir}")
    
    return step2_dir, step1_dir, current_dir

def test_imports():
    """Test all required imports for Step 3"""
    print("🔍 STEP 3 IMPORT TEST")
    print("=" * 50)
    
    # Setup paths
    step2_dir, step1_dir, current_dir = setup_imports()
    
    try:
        # Test Step 2 Data Module - use importlib for better path handling
        import importlib.util

        # Import GameTotalsDataModule
        spec = importlib.util.spec_from_file_location(
            "game_totals_data_module",
            step2_dir / "data_processing" / "game_totals_data_module.py"
        )
        data_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(data_module)
        GameTotalsDataModule = data_module.GameTotalsDataModule
        print("✅ GameTotalsDataModule imported successfully")

        # Import GameTotalsModel
        spec = importlib.util.spec_from_file_location(
            "game_totals_model",
            step2_dir / "models" / "game_totals_model.py"
        )
        model_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(model_module)
        GameTotalsModel = model_module.GameTotalsModel
        print("✅ GameTotalsModel imported successfully")
        
        # Import MoneylineModel
        spec = importlib.util.spec_from_file_location(
            "moneyline_model",
            current_dir / "models" / "moneyline_model.py"
        )
        ml_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(ml_module)
        MoneylineModel = ml_module.MoneylineModel
        print("✅ MoneylineModel imported successfully")
        
        # Test instantiation
        dummy_totals_model = GameTotalsModel(player_points_model=None, input_dim=28)
        moneyline_model = MoneylineModel(totals_model=dummy_totals_model)
        print("✅ Models instantiated successfully")
        
        print()
        print("🎯 ALL IMPORTS WORKING!")
        print("Step 3 is ready for training.")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    
    if success:
        print()
        print("📋 COPY THIS ROBUST IMPORT PATTERN FOR YOUR STEP 3 SCRIPTS:")
        print()
        print("# Method 1: Simple sys.path approach (if it works)")
        print("import sys")
        print("from pathlib import Path")
        print("current_dir = Path(__file__).parent")
        print("step2_dir = current_dir.parent / 'step_02_game_totals_model'")
        print("sys.path.insert(0, str(step2_dir))")
        print("from data_processing.game_totals_data_module import GameTotalsDataModule")
        print()
        print("# Method 2: Robust importlib approach (recommended)")
        print("import importlib.util")
        print("from pathlib import Path")
        print()
        print("current_dir = Path(__file__).parent")
        print("step2_dir = current_dir.parent / 'step_02_game_totals_model'")
        print()
        print("# Import GameTotalsDataModule")
        print("spec = importlib.util.spec_from_file_location(")
        print("    'game_totals_data_module',")
        print("    step2_dir / 'data_processing' / 'game_totals_data_module.py'")
        print(")")
        print("data_module = importlib.util.module_from_spec(spec)")
        print("spec.loader.exec_module(data_module)")
        print("GameTotalsDataModule = data_module.GameTotalsDataModule")
        print()
        print("# Import GameTotalsModel")
        print("spec = importlib.util.spec_from_file_location(")
        print("    'game_totals_model',")
        print("    step2_dir / 'models' / 'game_totals_model.py'")
        print(")")
        print("model_module = importlib.util.module_from_spec(spec)")
        print("spec.loader.exec_module(model_module)")
        print("GameTotalsModel = model_module.GameTotalsModel")
    else:
        print()
        print("❌ Import issues detected. Check paths and file structure.")
