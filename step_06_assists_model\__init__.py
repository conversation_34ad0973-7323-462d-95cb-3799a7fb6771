"""
Assists Model (Step 6 of 9) - Playmaking-Focused Basketball Analytics
=====================================================================

This package implements a comprehensive assists prediction model focused on playmaking analysis.

Key Components:
- AssistsModel: Main prediction model with teammate interaction and usage modeling
- AssistsDataModule: Data processing with playmaking feature engineering
- AssistsMetrics: Specialized metrics for assists evaluation
- AssistsValidator: Validation utilities for assists predictions

Features:
- Playmaking-focused architecture
- Teammate interaction modeling
- Usage rate and assist opportunity analysis
- Comprehensive validation and metrics

Usage:
    from step_06_assists_model import AssistsModel, AssistsDataModule
    from step_06_assists_model.training import train_assists_model
    from step_06_assists_model.utils import evaluate_assists_model
"""

# Import main components
from .models.assists_model import AssistsModel
from .data_processing.assists_data_module import AssistsDataModule

# Import utilities
from .utils.assists_metrics import evaluate_assists_model
from .validation.assists_validation import validate_assists_model

# Import training
from .training.train_assists_model import train_assists_model, AssistsTrainingConfig

__version__ = "1.0.0"
__author__ = "HMNV Analytics Team"

__all__ = [
    "AssistsModel",
    "AssistsDataModule",
    "evaluate_assists_model",
    "validate_assists_model",
    "train_assists_model",
    "AssistsTrainingConfig"
]
