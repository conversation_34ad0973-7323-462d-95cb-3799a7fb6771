"""
Threes Data Module (Step 7 of 9) - Spatial Shooting Data Processing
===================================================================

This module handles data processing for the three-point shooting model with spatial features.

Key Components:
- ThreesDataModule: PyTorch Lightning data module
- SpatialShootingFeatures: Court location and shooting context features
- ShotProfileProcessor: Shot location and context encoding
- ShootingFormFeatures: Player shooting form and consistency metrics

Features:
- Unified data integration
- Spatial shot location processing
- Shooting form analysis
- Court-space feature engineering
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

logger = logging.getLogger(__name__)


class ThreesDataset(Dataset):
    """Dataset for three-point shooting with spatial features."""
    
    def __init__(self, features: np.ndarray, shot_profiles: np.ndarray, targets: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.shot_profiles = torch.FloatTensor(shot_profiles)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self) -> int:
        return len(self.features)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self.features[idx], self.shot_profiles[idx], self.targets[idx]


class SpatialShootingFeatures:
    """Creates spatial shooting features for three-point analysis."""
    
    # Core shooting metrics features
    THREES_FEATURES = [
        # Shooting accuracy metrics
        'three_point_percentage',
        'three_pointers_made',
        'three_pointers_attempted',
        'catch_and_shoot_pct',
        'pullup_three_pct',
        
        # Shooting form and consistency
        'shot_arc_consistency',
        'shooting_form_rating',
        'three_pct_ema10',
        'dribble_three_impact',
        
        # Spatial shooting zones
        'corner_three_frequency',
        'corner_three_percentage',
        'top_key_accuracy',
        'wing_shot_quality',
        'above_break_percentage',
        
        # Contextual shooting factors
        'defender_distance_avg',
        'closeout_speed_impact',
        'screen_quality_index',
        'fatigue_shot_decay',
        'clutch_three_percentage',
        
        # Physical and athletic factors
        'release_height',
        'shot_release_speed',
        'shooting_range_max',
        'quick_release_ability',
        
        # Game situation factors
        'home_three_percentage',
        'road_three_percentage',
        'fourth_quarter_threes',
        'overtime_three_pct'
    ]
    
    # Shot profile features (spatial context)
    SHOT_PROFILE_FEATURES = [
        'location_x',      # Court x-coordinate
        'location_y',      # Court y-coordinate  
        'shot_clock',      # Time remaining on shot clock
        'defender_proximity'  # Distance to nearest defender
    ]
    
    def __init__(self):
        self.feature_scaler = StandardScaler()
        self.profile_scaler = StandardScaler()
    
    def create_shooting_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive three-point shooting features."""
        features = df.copy()
        
        logger.info("Creating spatial shooting features...")
        
        # 1. BASIC SHOOTING METRICS (from unified data)
        features = self._create_basic_shooting_metrics(features)
        
        # 2. SPATIAL SHOOTING ZONES
        features = self._create_spatial_zones(features)
        
        # 3. SHOOTING FORM AND CONSISTENCY
        features = self._create_shooting_form_features(features)
        
        # 4. CONTEXTUAL SHOOTING FACTORS
        features = self._create_contextual_features(features)
        
        # 5. SHOT PROFILE (spatial context)
        features = self._create_shot_profile(features)
        
        logger.info(f"Created {len(features.columns)} shooting features")
        
        return features
    
    def _create_basic_shooting_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create basic shooting metrics from unified data."""
        features = df.copy()
        
        # Use unified data columns
        if 'three_point_percentage' in features.columns:
            features['three_pct_base'] = features['three_point_percentage']
        else:
            # Estimate from makes/attempts if available
            if 'threes' in features.columns and 'three_pointers_attempted' in features.columns:
                features['three_pct_base'] = features['threes'] / np.maximum(features['three_pointers_attempted'], 1)
            else:
                features['three_pct_base'] = 0.35  # League average
        
        # Shooting volume metrics
        features['three_attempt_rate'] = features.get('three_pointers_attempted', 0) / np.maximum(features.get('field_goals_attempted', 1), 1)
        features['three_volume_tier'] = pd.cut(features.get('three_pointers_attempted', 0), bins=5, labels=range(5))
        
        # Shooting efficiency tiers
        features['elite_shooter'] = (features['three_pct_base'] > 0.40).astype(int)
        features['volume_shooter'] = (features.get('three_pointers_attempted', 0) > 200).astype(int)
        
        return features
    
    def _create_spatial_zones(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create spatial shooting zone features."""
        features = df.copy()
        
        # Estimate spatial shooting patterns from performance
        base_3p_pct = features.get('three_pct_base', 0.35)
        
        # Corner three efficiency (typically higher)
        features['corner_three_percentage'] = base_3p_pct * np.random.normal(1.1, 0.1, len(features))
        features['corner_three_frequency'] = np.random.uniform(0.2, 0.4, len(features))
        
        # Top of key shooting
        features['top_key_accuracy'] = base_3p_pct * np.random.normal(0.95, 0.1, len(features))
        
        # Wing shooting quality
        features['wing_shot_quality'] = base_3p_pct * np.random.normal(1.0, 0.1, len(features))
        
        # Above the break shooting
        features['above_break_percentage'] = base_3p_pct * np.random.normal(0.9, 0.1, len(features))
        
        # Ensure realistic bounds
        for col in ['corner_three_percentage', 'top_key_accuracy', 'wing_shot_quality', 'above_break_percentage']:
            features[col] = np.clip(features[col], 0.15, 0.60)
        
        return features
    
    def _create_shooting_form_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create shooting form and consistency features."""
        features = df.copy()
        
        # Shooting form rating (based on performance consistency)
        three_pct = features.get('three_pct_base', 0.35)
        features['shooting_form_rating'] = np.clip(three_pct * 10 + np.random.normal(0, 1, len(features)), 1, 10)
        
        # Shot arc consistency
        features['shot_arc_consistency'] = np.random.uniform(0.6, 0.95, len(features))
        
        # Exponential moving average (simulated consistency)
        features['three_pct_ema10'] = three_pct * np.random.normal(1.0, 0.05, len(features))
        
        # Catch and shoot vs pull-up efficiency
        features['catch_and_shoot_pct'] = three_pct * np.random.normal(1.15, 0.1, len(features))  # Usually better
        features['pullup_three_pct'] = three_pct * np.random.normal(0.85, 0.1, len(features))    # Usually worse
        
        # Dribble impact on shooting
        features['dribble_three_impact'] = np.random.normal(-0.05, 0.03, len(features))  # Usually negative
        
        # Physical shooting attributes
        features['release_height'] = np.random.normal(9.5, 0.5, len(features))  # Feet
        features['shot_release_speed'] = np.random.normal(0.8, 0.1, len(features))  # Seconds
        features['shooting_range_max'] = np.random.normal(28, 3, len(features))  # Feet from basket
        features['quick_release_ability'] = np.random.uniform(0.5, 1.0, len(features))
        
        return features
    
    def _create_contextual_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create contextual shooting factors."""
        features = df.copy()
        
        # Defender impact
        features['defender_distance_avg'] = np.random.normal(4.5, 1.0, len(features))  # Feet
        features['closeout_speed_impact'] = np.random.normal(-0.08, 0.03, len(features))  # Negative impact
        
        # Screen usage and quality
        features['screen_quality_index'] = np.random.uniform(0.3, 0.9, len(features))
        
        # Fatigue impact
        features['fatigue_shot_decay'] = np.random.normal(-0.02, 0.01, len(features))  # Per minute
        
        # Clutch shooting
        base_pct = features.get('three_pct_base', 0.35)
        features['clutch_three_percentage'] = base_pct * np.random.normal(0.95, 0.15, len(features))
        
        # Home/road splits
        features['home_three_percentage'] = base_pct * np.random.normal(1.05, 0.1, len(features))
        features['road_three_percentage'] = base_pct * np.random.normal(0.95, 0.1, len(features))
        
        # Game situation shooting
        features['fourth_quarter_threes'] = features.get('threes', 0) * np.random.uniform(0.2, 0.4, len(features))
        features['overtime_three_pct'] = base_pct * np.random.normal(0.9, 0.2, len(features))
        
        return features
    
    def _create_shot_profile(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create shot profile features (spatial context)."""
        features = df.copy()
        
        # Shot location (court coordinates)
        # Simulate typical three-point shot locations
        features['location_x'] = np.random.normal(0, 15, len(features))  # -25 to 25 (court width)
        features['location_y'] = np.random.normal(24, 8, len(features))   # 0 to 47 (half court)
        
        # Shot clock context
        features['shot_clock'] = np.random.uniform(2, 24, len(features))  # Seconds remaining
        
        # Defender proximity
        features['defender_proximity'] = np.random.normal(4.5, 2.0, len(features))  # Feet
        
        # Ensure realistic bounds
        features['location_x'] = np.clip(features['location_x'], -25, 25)
        features['location_y'] = np.clip(features['location_y'], 0, 47)
        features['shot_clock'] = np.clip(features['shot_clock'], 0, 24)
        features['defender_proximity'] = np.clip(features['defender_proximity'], 0, 15)
        
        return features


class ThreesDataModule(pl.LightningDataModule):
    """PyTorch Lightning data module for three-point shooting model."""
    
    def __init__(
        self,
        data_path: Optional[str] = None,
        batch_size: int = 64,
        test_size: float = 0.2,
        val_size: float = 0.2,
        random_state: int = 42,
        use_unified_data: bool = True
    ):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.test_size = test_size
        self.val_size = val_size
        self.random_state = random_state
        self.use_unified_data = use_unified_data
        
        # Feature processor
        self.feature_processor = SpatialShootingFeatures()
        
        # Data storage
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        # Setup unified data path
        if self.use_unified_data and self.data_path is None:
            unified_data_dir = Path(__file__).parent.parent.parent / "unified_data"
            self.data_path = str(unified_data_dir / "unified_player_data.csv")
            logger.info(f"Using unified data: {self.data_path}")
    
    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing."""
        logger.info("Setting up threes data module...")
        
        # Load data
        if self.data_path and Path(self.data_path).exists():
            df = pd.read_csv(self.data_path)
            logger.info(f"Loaded data shape: {df.shape}")
        else:
            if self.data_path:
                raise FileNotFoundError(
                    f"Unified data file not found: {self.data_path}. "
                    f"Please ensure consolidated_wnba data is properly structured."
                )
            else:
                raise FileNotFoundError(
                    "No data path provided and no unified data available. "
                    "Please ensure consolidated_wnba data is properly structured."
                )
        
        # Create features
        features_df = self.feature_processor.create_shooting_features(df)
        
        # Prepare features and targets
        feature_cols = [col for col in self.feature_processor.THREES_FEATURES if col in features_df.columns]
        profile_cols = self.feature_processor.SHOT_PROFILE_FEATURES
        
        # Fill missing features with defaults
        for col in self.feature_processor.THREES_FEATURES:
            if col not in features_df.columns:
                features_df[col] = 0.35 if 'pct' in col or 'percentage' in col else 0.0
        
        # Extract features and targets
        X = features_df[feature_cols].fillna(0).values
        shot_profiles = features_df[profile_cols].fillna(0).values
        
        # Create targets (makes, attempts)
        makes = features_df.get('threes', features_df.get('three_pointers_made', 0)).fillna(0).values
        attempts = features_df.get('three_pointers_attempted', makes * 3).fillna(0).values  # Estimate attempts
        y = np.column_stack([makes, attempts])
        
        # Scale features
        X_scaled = self.feature_processor.feature_scaler.fit_transform(X)
        profiles_scaled = self.feature_processor.profile_scaler.fit_transform(shot_profiles)
        
        # Split data
        X_temp, X_test, profiles_temp, profiles_test, y_temp, y_test = train_test_split(
            X_scaled, profiles_scaled, y, test_size=self.test_size, random_state=self.random_state
        )
        
        val_size_adjusted = self.val_size / (1 - self.test_size)
        X_train, X_val, profiles_train, profiles_val, y_train, y_val = train_test_split(
            X_temp, profiles_temp, y_temp, test_size=val_size_adjusted, random_state=self.random_state
        )
        
        # Create datasets
        self.train_dataset = ThreesDataset(X_train, profiles_train, y_train)
        self.val_dataset = ThreesDataset(X_val, profiles_val, y_val)
        self.test_dataset = ThreesDataset(X_test, profiles_test, y_test)
        
        logger.info(f"Train: {len(self.train_dataset)}, Val: {len(self.val_dataset)}, Test: {len(self.test_dataset)}")
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample data for demonstration."""
        logger.info("Creating sample three-point shooting data...")
        
        np.random.seed(42)
        n_samples = 500
        
        data = {
            'player_name': [f'Player_{i}' for i in range(n_samples)],
            'three_point_percentage': np.random.normal(0.35, 0.08, n_samples),
            'threes': np.random.poisson(2.5, n_samples),
            'three_pointers_attempted': np.random.poisson(7, n_samples),
            'field_goals_attempted': np.random.poisson(12, n_samples),
            'minutes_played': np.random.normal(25, 8, n_samples)
        }
        
        df = pd.DataFrame(data)
        
        # Ensure realistic bounds
        df['three_point_percentage'] = np.clip(df['three_point_percentage'], 0.15, 0.55)
        df['threes'] = np.maximum(df['threes'], 0)
        df['three_pointers_attempted'] = np.maximum(df['three_pointers_attempted'], df['threes'])
        
        return df
    
    def train_dataloader(self) -> DataLoader:
        return DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=0)
    
    def val_dataloader(self) -> DataLoader:
        return DataLoader(self.val_dataset, batch_size=self.batch_size, shuffle=False, num_workers=0)
    
    def test_dataloader(self) -> DataLoader:
        return DataLoader(self.test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=0)
