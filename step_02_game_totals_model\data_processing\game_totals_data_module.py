"""
⚙️ Game Totals Data Pipeline for WNBA Team Scoring Model

This module implements the PyTorch Lightning DataModule for loading and preprocessing
game-level data that aggregates player performances into team totals.
Uses the same time-based splits as the Player Points model for hierarchical consistency.
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from sklearn.preprocessing import QuantileTransformer, StandardScaler
from typing import Optional, List, Tuple, Dict
import os
import sys
from pathlib import Path

# Add step_01 to path to reuse the player model
step_01_path = str(Path(__file__).parent.parent.parent / "step_01_player_points_model")
if step_01_path not in sys.path:
    sys.path.insert(0, step_01_path)

# Import Step 1 components for hierarchical integration
HIERARCHICAL_AVAILABLE = False
PlayerPointsModel = None
WNBADataModule = None

try:
    # Try direct imports first
    from models.player_points_model import PlayerPointsModel
    from data_processing.wnba_data_module import WNBADataModule
    HIERARCHICAL_AVAILABLE = True
    print("✅ Hierarchical integration available - Step 1 components loaded")
except ImportError as e1:
    try:
        # Try package-style imports
        import step_01_player_points_model
        from step_01_player_points_model import PlayerPointsModel, WNBADataModule
        HIERARCHICAL_AVAILABLE = True
        print("✅ Hierarchical integration available - Step 1 package imports loaded")
    except ImportError as e2:
        try:
            # Try absolute imports
            import sys
            sys.path.insert(0, str(Path(__file__).parent.parent.parent))
            from step_01_player_points_model.models.player_points_model import PlayerPointsModel
            from step_01_player_points_model.data_processing.wnba_data_module import WNBADataModule
            HIERARCHICAL_AVAILABLE = True
            print("✅ Hierarchical integration available - Step 1 absolute imports loaded")
        except ImportError as e3:
            HIERARCHICAL_AVAILABLE = False
            print(f"⚠️  Hierarchical integration unavailable - all import attempts failed:")
            print(f"   Direct import error: {e1}")
            print(f"   Package import error: {e2}")
            print(f"   Absolute import error: {e3}")
            print(f"   Step 1 path: {step_01_path}")
            print(f"   Path exists: {os.path.exists(step_01_path)}")
            print("   Continuing with standard (non-hierarchical) mode only")


class GameTotalsDataModule(pl.LightningDataModule):
    """
    🔧 Game Totals Data Pipeline for Team Scoring Predictions
    
    Features:
    - Consistent time-based splits with Player Points model (2020-2023 train, 2024 val, 2025 test)
    - Game-level aggregation from player performances
    - Team context features and opponent analysis
    - Rolling team performance metrics
    """
    
    def __init__(
        self,
        data_path: str,
        batch_size: int = 32,
        num_workers: int = 0,  # Set to 0 for Windows compatibility
        seq_len: int = 10,  # Game sequence length for team rolling features
        val_split_date: str = '2024-01-01',
        test_split_date: str = '2025-01-01',
        player_model_path: Optional[str] = None,
        enable_hierarchical: bool = True,  # Enable integration with Step 1
        player_data_path: Optional[str] = None  # Path to player-level data
    ):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.seq_len = seq_len
        self.val_split_date = val_split_date
        self.test_split_date = test_split_date
        self.player_model_path = player_model_path
        self.enable_hierarchical = enable_hierarchical and HIERARCHICAL_AVAILABLE
        
        # Set default player data path to unified data if not provided
        if player_data_path is None:
            unified_data_dir = Path(__file__).parent.parent.parent / "unified_data"
            self.player_data_path = str(unified_data_dir / "unified_player_data.csv")
        else:
            self.player_data_path = player_data_path
        
        # Hierarchical integration components
        self.player_model = None
        self.player_data_module = None
        
        # Feature columns (will be determined from data)
        self.feature_columns = None
        self.target_column = 'team_total_points'
        
        # Scalers
        self.feature_scaler = None
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        # Load player model if hierarchical mode is enabled
        if self.enable_hierarchical and self.player_model_path:
            self._load_player_model()
        
        print(f"🔗 Hierarchical integration: {'✅ Enabled' if self.enable_hierarchical else '❌ Disabled'}")

    def _load_player_model(self):
        """Load the trained player points model for hierarchical integration"""
        try:
            if os.path.exists(self.player_model_path):
                print(f"Loading player model from: {self.player_model_path}")
                self.player_model = PlayerPointsModel.load_from_checkpoint(self.player_model_path)
                self.player_model.eval()  # Set to evaluation mode
                self.player_model.freeze()  # Freeze the model parameters
                print("✅ Player model loaded successfully")
            else:
                print(f"⚠️  Player model checkpoint not found: {self.player_model_path}")
                self.player_model = None
        except Exception as e:
            print(f"❌ Error loading player model: {e}")
            self.player_model = None

    def _setup_player_data_module(self):
        """Setup the player data module for hierarchical feature generation"""
        if self.player_data_path and os.path.exists(self.player_data_path):
            try:
                print(f"Setting up player data module from: {self.player_data_path}")
                self.player_data_module = WNBADataModule(
                    data_path=self.player_data_path,
                    batch_size=64,
                    val_split_date=self.val_split_date,
                    test_split_date=self.test_split_date,
                    use_unified_data=True  # Use unified data for consistency
                )
                # Setup the player data module
                self.player_data_module.setup()
                print("✅ Player data module setup complete")
            except Exception as e:
                print(f"❌ Error setting up player data module: {e}")
                self.player_data_module = None
        else:
            print("⚠️  Player data path not provided or doesn't exist")
            self.player_data_module = None

    def _generate_hierarchical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate hierarchical features from player-level predictions"""
        if not self.enable_hierarchical:
            print("⚠️  Hierarchical features disabled - using standard features only")
            return data
        
        print("🔄 Generating hierarchical features from player predictions...")
        
        # Create a copy to avoid modifying the original
        data_with_hierarchical = data.copy()
        
        # Initialize hierarchical feature columns
        hierarchical_features = []
        
        # Only generate hierarchical features if we have the player model
        if self.player_model is not None:
            # If we have player data module, use it to generate features
            if self.player_data_module is not None:
                # Get player predictions for each game
                player_predictions = self._get_player_predictions_for_games(data)
                
                # Aggregate player predictions by team and game
                team_aggregated_features = self._aggregate_player_predictions(player_predictions, data)
                
                # Merge with game data
                for feature_name, feature_values in team_aggregated_features.items():
                    data_with_hierarchical[feature_name] = feature_values
                    hierarchical_features.append(feature_name)
                    
                print(f"✅ Added {len(hierarchical_features)} hierarchical features")
            else:
                print("⚠️  Player data module not available - skipping hierarchical features")
        else:
            print("⚠️  Player model not available - skipping hierarchical features")
        
        # Add hierarchical features to the feature columns list
        for feature in hierarchical_features:
            if feature not in self.feature_columns:
                self.feature_columns.append(feature)
        
        return data_with_hierarchical

    def _get_player_predictions_for_games(self, game_data: pd.DataFrame) -> pd.DataFrame:
        """Get player predictions for each game in the dataset"""
        if self.player_data_module is None or self.player_model is None:
            print("⚠️  Player data module or model not available - cannot generate player predictions")
            return pd.DataFrame()
        
        print("🔄 Generating real player predictions from Step 1 model...")
        
        # Get the player data from the data module
        player_data = self.player_data_module.data
        
        # Get model expected dimensions
        expected_dim = self.player_model.hparams.input_dim
        available_features = len(self.player_data_module.feature_columns)
        
        print(f"🔍 Model expects {expected_dim} features, player data has {available_features}")
        
        # Create a simplified prediction approach if dimensions don't match
        if available_features != expected_dim:
            print(f"⚠️  Feature dimension mismatch detected, using simplified prediction approach")
            return self._get_simplified_player_predictions(game_data, player_data)
        
        # Create a mapping of teams to get consistent team IDs
        team_mapping = self._create_team_mapping(game_data, player_data)
        
        # Generate predictions for each game (limit to avoid memory issues)
        all_predictions = []
        max_games_to_process = min(50, len(game_data))  # Process first 50 games as test
        
        for i, (_, game) in enumerate(game_data.head(max_games_to_process).iterrows()):
            if i % 10 == 0:
                print(f"🔄 Processing game {i+1}/{max_games_to_process}")
            
            game_id = game['game_id']
            team_abbr = game['team_abbreviation']
            game_date = game['date']
            
            # Get players for this team
            team_players = self._get_team_players(player_data, team_abbr, game_date)
            
            if len(team_players) == 0:
                print(f"⚠️  No players found for team {team_abbr} on {game_date}")
                continue
            
            # Generate predictions for each player
            for _, player in team_players.iterrows():
                try:
                    # Prepare player features for prediction
                    player_features = self._prepare_player_features_for_prediction(player)
                    
                    # Generate prediction using Step 1 model
                    with torch.no_grad():
                        prediction = self.player_model(player_features)
                        predicted_points_per_game = prediction.item()

                        # Step 1 model outputs points per game, so use directly
                        predicted_points = predicted_points_per_game
                    
                    # Store prediction
                    all_predictions.append({
                        'game_id': game_id,
                        'team_id': game['team_id'],
                        'team_abbreviation': team_abbr,
                        'player_name': player['player_name'],
                        'predicted_points': predicted_points,
                        'actual_points': player.get('points', 0),  # For validation if available
                        'minutes_per_game': player.get('minutes_per_game', 20),
                        'games_played': player.get('games_played', 1)
                    })
                    
                except Exception as e:
                    print(f"⚠️  Error predicting for player {player.get('player_name', 'Unknown')}: {e}")
                    # Use simplified prediction as fallback
                    try:
                        simplified_pred = self._get_simplified_prediction(player)
                        all_predictions.append({
                            'game_id': game_id,
                            'team_id': game['team_id'],
                            'team_abbreviation': team_abbr,
                            'player_name': player['player_name'],
                            'predicted_points': simplified_pred,
                            'actual_points': player.get('points', 0),
                            'minutes_per_game': player.get('minutes_per_game', 20),
                            'games_played': player.get('games_played', 1)
                        })
                    except Exception as e2:
                        print(f"⚠️  Error with simplified prediction: {e2}")
                        continue
        
        predictions_df = pd.DataFrame(all_predictions)
        
        if len(predictions_df) > 0:
            print(f"✅ Generated {len(predictions_df)} player predictions for {min(max_games_to_process, len(game_data))} games")
            print(f"   Average predicted points per player: {predictions_df['predicted_points'].mean():.2f}")
            print(f"   Players per game: {len(predictions_df) / min(max_games_to_process, len(game_data)):.1f}")
        else:
            print("⚠️  No player predictions generated")
        
        return predictions_df

    def _get_simplified_player_predictions(self, game_data: pd.DataFrame, player_data: pd.DataFrame) -> pd.DataFrame:
        """Generate simplified player predictions based on historical averages when model dimensions don't match"""
        
        print("🔄 Generating simplified player predictions based on historical stats...")
        
        # Create team mapping
        team_mapping = self._create_team_mapping(game_data, player_data)
        
        all_predictions = []
        max_games_to_process = min(50, len(game_data))  # Process first 50 games as test
        
        for i, (_, game) in enumerate(game_data.head(max_games_to_process).iterrows()):
            if i % 10 == 0:
                print(f"🔄 Processing game {i+1}/{max_games_to_process}")
            
            game_id = game['game_id']
            team_abbr = game['team_abbreviation']
            game_date = game['date']
            
            # Get players for this team
            team_players = self._get_team_players(player_data, team_abbr, game_date)
            
            if len(team_players) == 0:
                continue
            
            # Generate predictions based on historical averages
            for _, player in team_players.iterrows():
                # Use historical points as baseline (convert season total to per-game)
                season_total_points = player.get('points', 0)
                games_played = player.get('games_played', 30)  # Default ~30 games per season
                historical_points = season_total_points / max(games_played, 1)  # Convert to per-game
                
                # Apply some variation based on minutes played
                minutes_factor = player.get('minutes_per_game', 20) / 25.0  # Normalize to 25 minutes
                
                # Simple prediction: historical points * minutes factor + some noise
                predicted_points = historical_points * minutes_factor
                
                # Add some realistic variation
                if predicted_points > 0:
                    # Add variation based on player type
                    if predicted_points > 15:  # Star player
                        predicted_points *= np.random.normal(1.0, 0.15)
                    elif predicted_points > 8:  # Role player
                        predicted_points *= np.random.normal(1.0, 0.25)
                    else:  # Bench player
                        predicted_points *= np.random.normal(1.0, 0.35)
                
                predicted_points = max(0, predicted_points)  # Ensure non-negative
                
                all_predictions.append({
                    'game_id': game_id,
                    'team_id': game['team_id'],
                    'team_abbreviation': team_abbr,
                    'player_name': player['player_name'],
                    'predicted_points': predicted_points,
                    'actual_points': historical_points,
                    'minutes_per_game': player.get('minutes_per_game', 20),
                    'games_played': player.get('games_played', 1)
                })
        
        predictions_df = pd.DataFrame(all_predictions)
        
        if len(predictions_df) > 0:
            print(f"✅ Generated {len(predictions_df)} simplified player predictions")
            print(f"   Average predicted points per player: {predictions_df['predicted_points'].mean():.2f}")
        
        return predictions_df

    def _create_team_mapping(self, game_data: pd.DataFrame, player_data: pd.DataFrame) -> Dict[str, str]:
        """Create a mapping between team abbreviations in game data and player data"""
        game_teams = set(game_data['team_abbreviation'].unique())
        player_teams = set(player_data['team_abbreviation'].unique())
        
        # Create mapping for teams that might have different abbreviations
        team_mapping = {}
        
        # Standard team mappings (handling common abbreviation differences)
        standard_mappings = {
            'SAN': 'LAS',  # San Antonio -> Las Vegas
            'PHX': 'PHO',  # Phoenix -> Phoenix
            'GSV': 'GSW',  # Golden State (if different)
            'CON': 'CONN', # Connecticut variations
            'NYL': 'NY',   # New York variations
            'LVA': 'LAS',  # Las Vegas variations
        }
        
        # Apply standard mappings
        for game_team in game_teams:
            if game_team in player_teams:
                team_mapping[game_team] = game_team
            elif game_team in standard_mappings and standard_mappings[game_team] in player_teams:
                team_mapping[game_team] = standard_mappings[game_team]
            else:
                # Try to find the closest match
                for player_team in player_teams:
                    if game_team.lower() in player_team.lower() or player_team.lower() in game_team.lower():
                        team_mapping[game_team] = player_team
                        break
                else:
                    # No match found, use the first available player team as fallback
                    if player_teams:
                        team_mapping[game_team] = list(player_teams)[0]
                        print(f"⚠️  No direct mapping for team {game_team}, using {list(player_teams)[0]} as fallback")
        
        print(f"✅ Created team mapping: {team_mapping}")
        return team_mapping

    def _get_team_players(self, player_data: pd.DataFrame, team_abbr: str, game_date: pd.Timestamp) -> pd.DataFrame:
        """Get players for a specific team and time period"""
        
        # Create team mapping
        team_mapping = self._create_team_mapping(pd.DataFrame({'team_abbreviation': [team_abbr]}), player_data)
        mapped_team = team_mapping.get(team_abbr, team_abbr)
        
        # Filter players for this team
        team_players = player_data[player_data['team_abbreviation'] == mapped_team].copy()
        
        # If we have season information, try to match the appropriate season
        if 'season' in player_data.columns:
            # Map game date to season (simplified logic)
            game_year = game_date.year
            
            # WNBA seasons typically run from May to September
            if game_date.month >= 5:  # May onwards is current year season
                target_season = game_year
            else:  # January-April is previous year season
                target_season = game_year - 1
            
            # Try to find players for the target season
            season_players = team_players[team_players['season'] == target_season]
            
            if len(season_players) > 0:
                team_players = season_players
            else:
                # Fallback to most recent season for this team
                if len(team_players) > 0:
                    latest_season = team_players['season'].max()
                    team_players = team_players[team_players['season'] == latest_season]
        
        # Ensure we have a reasonable roster size (10-15 players)
        if len(team_players) > 15:
            # Take top players by minutes or games played
            if 'minutes_per_game' in team_players.columns:
                team_players = team_players.nlargest(15, 'minutes_per_game')
            elif 'games_played' in team_players.columns:
                team_players = team_players.nlargest(15, 'games_played')
            else:
                team_players = team_players.head(15)
        elif len(team_players) < 8:
            # If we have very few players, try to get more from other seasons
            if 'season' in player_data.columns:
                all_team_players = player_data[player_data['team_abbreviation'] == mapped_team]
                if len(all_team_players) > len(team_players):
                    # Take players from the most recent seasons
                    recent_players = all_team_players.nlargest(12, 'season')
                    team_players = recent_players.head(12)
        
        return team_players

    def _prepare_player_features_for_prediction(self, player: pd.Series) -> torch.Tensor:
        """Prepare player features for Step 1 model prediction with fallback handling"""
        
        # Get the expected input dimension from the model
        expected_dim = self.player_model.hparams.input_dim
        print(f"🔍 Model expects: {expected_dim} features")
        
        # Check if we can use the trained model or need to fall back to simplified prediction
        if hasattr(self.player_data_module, 'feature_columns') and self.player_data_module.feature_columns:
            expected_features = self.player_data_module.feature_columns
            print(f"🔍 Step 1 model expects {len(expected_features)} features")
            
            # Check how many features we can actually provide
            available_features = []
            for feature in expected_features:
                if feature in player.index:
                    available_features.append(feature)
            
            print(f"🔍 Available features: {len(available_features)}/{len(expected_features)}")
            
            # If we have too few features, use simplified prediction
            if len(available_features) < expected_dim * 0.3:  # Less than 30% of features available
                print("⚠️  Too few features available, using simplified prediction")
                return self._create_simplified_player_features(player, expected_dim)
            
            # Try to prepare features for the trained model
            try:
                feature_values = []
                for feature in expected_features:
                    if feature in player.index:
                        value = player[feature]
                        # Handle missing values
                        if pd.isna(value):
                            value = self._get_default_value(feature)
                        feature_values.append(float(value))
                    else:
                        # Feature not available, use default
                        feature_values.append(self._get_default_value(feature))
                
                # Adjust dimensions if needed
                if len(feature_values) != expected_dim:
                    print(f"⚠️  Feature dimension mismatch: got {len(feature_values)}, expected {expected_dim}")
                    feature_values = self._adjust_feature_dimensions(feature_values, expected_dim)
                
                # Convert to tensor
                feature_tensor = torch.FloatTensor(feature_values).unsqueeze(0)
                
                # Apply scaling if available
                if hasattr(self.player_data_module, 'feature_scaler') and self.player_data_module.feature_scaler is not None:
                    try:
                        feature_array = feature_tensor.numpy()
                        scaled_features = self.player_data_module.feature_scaler.transform(feature_array)
                        feature_tensor = torch.FloatTensor(scaled_features)
                    except Exception as e:
                        print(f"⚠️  Error applying feature scaling: {e}")
                        # Continue without scaling
                
                return feature_tensor  # Keep batch dimension for model
                
            except Exception as e:
                print(f"⚠️  Error preparing features for trained model: {e}")
                return self._create_simplified_player_features(player, expected_dim)
        
        else:
            # No feature columns available, use simplified approach
            print("⚠️  No feature columns available, using simplified approach")
            return self._create_simplified_player_features(player, expected_dim)
    
    def _get_default_value(self, feature: str) -> float:
        """Get default value for a missing feature"""
        if feature in ['age', 'games_played']:
            return 25.0 if feature == 'age' else 20.0
        elif 'percentage' in feature:
            return 0.45  # Default shooting percentage
        elif 'per_minute' in feature:
            return 0.1   # Default per-minute stat
        elif 'ewm' in feature or 'ema' in feature:
            return 0.0   # Default for rolling averages
        elif 'days_' in feature:
            return 2.0   # Default rest days
        elif 'b2b_flag' in feature:
            return 0.0   # Default not back-to-back
        else:
            return 0.0
    
    def _adjust_feature_dimensions(self, feature_values: List[float], expected_dim: int) -> List[float]:
        """Adjust feature dimensions to match expected model input"""
        if len(feature_values) < expected_dim:
            # Pad with zeros
            feature_values.extend([0.0] * (expected_dim - len(feature_values)))
            print(f"✅ Padded features to {len(feature_values)}")
        elif len(feature_values) > expected_dim:
            # Trim features - keep the most important ones
            feature_values = feature_values[:expected_dim]
            print(f"✅ Trimmed features to {len(feature_values)}")
        
        return feature_values
    
    def _create_simplified_player_features(self, player: pd.Series, expected_dim: int) -> torch.Tensor:
        """Create simplified features when the trained model can't be used"""
        
        # Use basic stats that are available in our player data
        basic_features = []
        
        # Core stats
        for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
            if stat in player.index:
                basic_features.append(float(player[stat]) if not pd.isna(player[stat]) else 0.0)
            else:
                basic_features.append(0.0)
        
        # Advanced stats
        for stat in ['minutes_per_game', 'field_goal_percentage', 'free_throw_percentage']:
            if stat in player.index:
                value = player[stat] if not pd.isna(player[stat]) else (25.0 if stat == 'minutes_per_game' else 0.45)
                basic_features.append(float(value))
            else:
                basic_features.append(25.0 if stat == 'minutes_per_game' else 0.45)
        
        # Per-minute stats
        for stat in ['points_per_minute', 'rebounds_per_minute', 'assists_per_minute']:
            if stat in player.index:
                basic_features.append(float(player[stat]) if not pd.isna(player[stat]) else 0.0)
            else:
                basic_features.append(0.0)
        
        # Other available stats
        for stat in ['age', 'games_played', 'total_stats']:
            if stat in player.index:
                value = player[stat] if not pd.isna(player[stat]) else (25.0 if stat == 'age' else 20.0)
                basic_features.append(float(value))
            else:
                basic_features.append(25.0 if stat == 'age' else 20.0)
        
        # Pad or trim to expected dimensions
        if len(basic_features) < expected_dim:
            # Pad with zeros
            basic_features.extend([0.0] * (expected_dim - len(basic_features)))
        elif len(basic_features) > expected_dim:
            # Trim to expected size
            basic_features = basic_features[:expected_dim]
        
        print(f"✅ Created {len(basic_features)} simplified features for player {player.get('player_name', 'Unknown')}")
        
        return torch.FloatTensor(basic_features)

    def _aggregate_player_predictions(self, player_predictions: pd.DataFrame, game_data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Aggregate player predictions into team-level features"""
        if len(player_predictions) == 0:
            print("⚠️  No player predictions to aggregate")
            return {}
        
        print(f"🔄 Aggregating {len(player_predictions)} player predictions into team-level features...")
        
        # Group by game_id and team_id to get team-level aggregations
        team_aggregations = player_predictions.groupby(['game_id', 'team_id'])['predicted_points'].agg([
            'sum',    # Total team points predicted
            'mean',   # Average player points
            'std',    # Standard deviation (scoring distribution)
            'max',    # Top scorer prediction
            'min',    # Lowest scorer prediction
            'count'   # Number of players
        ]).reset_index()
        
        # Handle NaN values in std (when only one player)
        team_aggregations['std'] = team_aggregations['std'].fillna(0)
        
        # Calculate additional derived features
        team_aggregations['scoring_concentration'] = team_aggregations['max'] / team_aggregations['sum'].clip(lower=1.0)
        team_aggregations['scoring_balance'] = 1.0 - team_aggregations['scoring_concentration']  # More balanced = higher value
        team_aggregations['depth_score'] = team_aggregations['count'] / 15.0  # Normalize by typical roster size
        
        # Calculate percentile-based features
        team_aggregations['top_3_avg'] = player_predictions.groupby(['game_id', 'team_id'])['predicted_points'].apply(
            lambda x: x.nlargest(3).mean()
        ).reset_index(drop=True)
        
        team_aggregations['bench_contribution'] = player_predictions.groupby(['game_id', 'team_id'])['predicted_points'].apply(
            lambda x: x.nsmallest(max(1, len(x) - 5)).mean() if len(x) > 5 else 0
        ).reset_index(drop=True)
        
        # Map back to the original game order
        game_order = game_data[['game_id', 'team_id']].copy()
        
        # Create features arrays in the same order as game_data
        features = {}
        feature_names = [
            'player_pred_total',      # Total predicted team points
            'player_pred_mean',       # Average predicted points per player
            'player_pred_std',        # Standard deviation of predictions
            'player_pred_max',        # Highest predicted individual score
            'player_pred_min',        # Lowest predicted individual score
            'player_pred_count',      # Number of players with predictions
            'player_scoring_concentration',  # How concentrated the scoring is
            'player_scoring_balance',        # How balanced the scoring is
            'player_depth_score',            # Team depth indicator
            'player_top3_avg',              # Average of top 3 predicted scorers
            'player_bench_contribution'      # Contribution from bench players
        ]
        
        for feature_name in feature_names:
            features[feature_name] = []
        
        # Map aggregated features back to game order
        for _, game in game_order.iterrows():
            game_id = game['game_id']
            team_id = game['team_id']
            
            # Find the aggregated stats for this game-team combination
            mask = (team_aggregations['game_id'] == game_id) & (team_aggregations['team_id'] == team_id)
            team_stats = team_aggregations[mask]
            
            if len(team_stats) > 0:
                stats = team_stats.iloc[0]
                features['player_pred_total'].append(stats['sum'])
                features['player_pred_mean'].append(stats['mean'])
                features['player_pred_std'].append(stats['std'])
                features['player_pred_max'].append(stats['max'])
                features['player_pred_min'].append(stats['min'])
                features['player_pred_count'].append(stats['count'])
                features['player_scoring_concentration'].append(stats['scoring_concentration'])
                features['player_scoring_balance'].append(stats['scoring_balance'])
                features['player_depth_score'].append(stats['depth_score'])
                features['player_top3_avg'].append(stats['top_3_avg'])
                features['player_bench_contribution'].append(stats['bench_contribution'])
            else:
                # No predictions for this game-team combination, use defaults
                print(f"⚠️  No predictions found for game {game_id}, team {team_id}")
                features['player_pred_total'].append(85.0)      # WNBA team average
                features['player_pred_mean'].append(7.1)        # Average player points
                features['player_pred_std'].append(4.2)         # Typical std
                features['player_pred_max'].append(15.0)        # Typical max
                features['player_pred_min'].append(2.0)         # Typical min
                features['player_pred_count'].append(12)        # Typical roster
                features['player_scoring_concentration'].append(0.18)  # Typical concentration
                features['player_scoring_balance'].append(0.82)        # Typical balance
                features['player_depth_score'].append(0.8)             # Typical depth
                features['player_top3_avg'].append(12.0)               # Typical top 3
                features['player_bench_contribution'].append(4.0)      # Typical bench
        
        # Convert to numpy arrays
        final_features = {k: np.array(v) for k, v in features.items()}
        
        # Print summary statistics
        print(f"✅ Generated {len(final_features)} hierarchical features:")
        for feature_name, feature_values in final_features.items():
            print(f"   {feature_name}: mean={np.mean(feature_values):.2f}, std={np.std(feature_values):.2f}")
        
        return final_features

    def prepare_data(self):
        """Download/prepare data if needed"""
        # Check if data file exists
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing"""
        
        # Load the data
        print(f"Loading game data from: {self.data_path}")
        self.data = self._load_and_preprocess_data()
        
        # Setup player data module if hierarchical mode is enabled
        if self.enable_hierarchical:
            self._setup_player_data_module()
        
        # Determine initial feature columns (will be updated after feature engineering)
        self._setup_feature_columns()
        
        # Create time-based splits (consistent with Player Points model)
        train_data, val_data, test_data = self._create_time_splits()
        
        # Apply feature engineering first (before scaling)
        train_data = self._add_rolling_features(train_data)
        val_data = self._add_rolling_features(val_data)
        test_data = self._add_rolling_features(test_data)
        
        # Generate hierarchical features from player model predictions
        if self.enable_hierarchical:
            train_data = self._generate_hierarchical_features(train_data)
            val_data = self._generate_hierarchical_features(val_data)
            test_data = self._generate_hierarchical_features(test_data)
        
        # Update feature columns after all feature engineering is complete
        self._setup_feature_columns_from_data(train_data)
        
        # Fit scalers on training data (after all feature engineering)
        self._fit_scalers(train_data)

        # Apply scaling
        train_data = self._apply_preprocessing(train_data)
        val_data = self._apply_preprocessing(val_data)
        test_data = self._apply_preprocessing(test_data)
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = GameTotalsDataset(
                train_data, self.feature_columns, self.target_column
            )
            if len(val_data) > 0:
                self.val_dataset = GameTotalsDataset(
                    val_data, self.feature_columns, self.target_column
                )
            else:
                # Use a small subset of training data for validation if no val data
                val_subset = train_data.sample(min(2, len(train_data)), random_state=42)
                self.val_dataset = GameTotalsDataset(
                    val_subset, self.feature_columns, self.target_column
                )
        
        if stage == "test" or stage is None:
            if len(test_data) > 0:
                self.test_dataset = GameTotalsDataset(
                    test_data, self.feature_columns, self.target_column
                )
            else:
                # Use a small subset of training data for testing if no test data
                test_subset = train_data.sample(min(2, len(train_data)), random_state=123)
                self.test_dataset = GameTotalsDataset(
                    test_subset, self.feature_columns, self.target_column
                )

    def _load_and_preprocess_data(self) -> pd.DataFrame:
        """Load and perform initial preprocessing of game-level data"""
        
        # Load the main game data file
        if self.data_path.endswith('.csv'):
            data = pd.read_csv(self.data_path)
        elif self.data_path.endswith('.parquet'):
            data = pd.read_parquet(self.data_path)
        else:
            raise ValueError("Data file must be CSV or Parquet format")
        
        print(f"Loaded {len(data)} game records")
        print(f"Columns: {list(data.columns)}")
        
        # Create a date column if it doesn't exist
        if 'date' not in data.columns and 'game_date' in data.columns:
            data['date'] = pd.to_datetime(data['game_date'])
        elif 'date' not in data.columns:
            raise ValueError("Date column is required (either 'date' or 'game_date')")
        else:
            data['date'] = pd.to_datetime(data['date'])
        
        # Ensure we have required columns for team totals
        if self.target_column not in data.columns:
            # Try to create team_total_points from available columns
            if 'team_points' in data.columns:
                print("Using team_points as team_total_points")
                data['team_total_points'] = data['team_points']
            elif 'points' in data.columns:
                print("Using points as team_total_points")
                data['team_total_points'] = data['points']
            else:
                raise ValueError(f"Target column '{self.target_column}' not found in data and cannot be created from available columns")
        
        # Remove any rows with missing target values
        data = data.dropna(subset=[self.target_column])
        
        # Add essential IDs if not present
        if 'team_id' not in data.columns:
            if 'team_abbreviation' in data.columns:
                data['team_id'] = pd.Categorical(data['team_abbreviation']).codes
            else:
                raise ValueError("team_id or team_abbreviation column is required")
        
        if 'game_id' not in data.columns:
            raise ValueError("game_id column is required")
        
        if 'opponent_id' not in data.columns:
            if 'opponent_abbreviation' in data.columns:
                data['opponent_id'] = pd.Categorical(data['opponent_abbreviation']).codes
            elif 'opponent' in data.columns:
                data['opponent_id'] = pd.Categorical(data['opponent']).codes
            else:
                raise ValueError("opponent_id, opponent_abbreviation, or opponent column is required")
        
        return data.sort_values(['date', 'team_id']).reset_index(drop=True)

    def _setup_feature_columns(self):
        """Determine which columns to use as features"""
        
        # Exclude non-feature columns
        exclude_columns = [
            'date', 'team_id', 'game_id', 'opponent_id',
            'team_abbreviation', 'opponent_abbreviation', 'opponent',
            'season', 'game_date',
            self.target_column
        ]
        
        # Select numeric columns as features
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        self.feature_columns = [col for col in numeric_columns if col not in exclude_columns]
        
        print(f"Using {len(self.feature_columns)} feature columns:")
        print(self.feature_columns)

    def _setup_feature_columns_from_data(self, data: pd.DataFrame):
        """Determine which columns to use as features from the actual data after feature engineering"""
        
        # Exclude non-feature columns
        exclude_columns = [
            'date', 'team_id', 'game_id', 'opponent_id',
            'team_abbreviation', 'opponent_abbreviation', 'opponent',
            'season', 'game_date',
            self.target_column
        ]
        
        # Select numeric columns as features
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        self.feature_columns = [col for col in numeric_columns if col not in exclude_columns]
        
        print(f"Using {len(self.feature_columns)} feature columns:")
        print(self.feature_columns)

    def _create_time_splits(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Create time-based train/validation/test splits
        
        Data Split Strategy (Consistent with Player Points Model):
        - Training: 2020-2023 (Multi-year historical data for pattern learning)
        - Validation: 2024 (Recent data for hyperparameter tuning)  
        - Test: 2025 (Most current data for performance evaluation)
        
        This ensures hierarchical consistency where game totals predictions
        can properly integrate with player-level predictions from Step 1.
        """
        
        # Sort by date for proper temporal ordering
        data_sorted = self.data.sort_values('date')
        
        # Create splits based on dates (same as Player Points model)
        train_mask = data_sorted['date'] < self.val_split_date
        val_mask = (data_sorted['date'] >= self.val_split_date) & (data_sorted['date'] < self.test_split_date)
        test_mask = data_sorted['date'] >= self.test_split_date
        
        train_data = data_sorted[train_mask].copy()
        val_data = data_sorted[val_mask].copy()
        test_data = data_sorted[test_mask].copy()
        
        print(f"Train set: {len(train_data)} games ({train_data['date'].min()} to {train_data['date'].max()})")
        print(f"Val set: {len(val_data)} games ({val_data['date'].min()} to {val_data['date'].max()})")
        print(f"Test set: {len(test_data)} games ({test_data['date'].min()} to {test_data['date'].max()})")
        
        # Show team distribution (consistent splits for hierarchical modeling)
        if 'team_abbreviation' in data_sorted.columns:
            print(f"Train teams: {train_data['team_abbreviation'].nunique()}")
            print(f"Val teams: {val_data['team_abbreviation'].nunique()}")
            print(f"Test teams: {test_data['team_abbreviation'].nunique()}")
        elif 'team_id' in data_sorted.columns:
            print(f"Train teams: {train_data['team_id'].nunique()}")
            print(f"Val teams: {val_data['team_id'].nunique()}")
            print(f"Test teams: {test_data['team_id'].nunique()}")
        
        return train_data, val_data, test_data

    def _fit_scalers(self, train_data: pd.DataFrame):
        """Fit scalers on training data"""
        
        # Robust scaling for features (handles outliers better)
        self.feature_scaler = QuantileTransformer(output_distribution='normal', random_state=42)
        self.feature_scaler.fit(train_data[self.feature_columns])
        
        print("Fitted QuantileTransformer on training features")

    def _apply_preprocessing(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply preprocessing (scaling only, rolling features added earlier)"""

        data = data.copy()

        # Apply feature scaling only if we have data
        if self.feature_scaler is not None and len(data) > 0:
            data[self.feature_columns] = self.feature_scaler.transform(data[self.feature_columns])

        return data

    def _add_rolling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add rolling sequence features for each team"""
        
        data = data.copy()
        
        # Sort by team and date
        data = data.sort_values(['team_id', 'date'])
        
        # Core rolling features for team performance
        rolling_cols = []
        
        # Basic team stats that might be available
        basic_stats = ['field_goals_made', 'field_goals_attempted', 'three_pointers_made',
                      'three_pointers_attempted', 'free_throws_made', 'free_throws_attempted',
                      'rebounds', 'assists', 'steals', 'blocks', 'turnovers']
        
        for col in basic_stats:
            if col in data.columns:
                rolling_cols.append(col)
        
        # Add percentage-based stats if available
        percentage_stats = ['field_goal_percentage', 'three_point_percentage', 'free_throw_percentage']
        for col in percentage_stats:
            if col in data.columns:
                rolling_cols.append(col)
        
        if rolling_cols:
            # Calculate exponentially weighted moving averages (EWMA) for team trends
            for col in rolling_cols:
                ewm_col = f'{col}_ewm_{self.seq_len}'
                data[ewm_col] = data.groupby('team_id')[col].transform(
                    lambda x: x.ewm(span=self.seq_len, min_periods=1).mean()
                )
                
                # Add to feature columns
                if ewm_col not in self.feature_columns:
                    self.feature_columns.append(ewm_col)
        
        # Add team context features
        if 'team_total_points' in data.columns:
            # Rolling average team scoring
            data['team_scoring_avg'] = data.groupby('team_id')[self.target_column].transform(
                lambda x: x.ewm(span=5, min_periods=1).mean().shift(1)  # Shift to avoid leakage
            )
            if 'team_scoring_avg' not in self.feature_columns:
                self.feature_columns.append('team_scoring_avg')
        
        # Add opponent context if available
        if 'opponent_id' in data.columns:
            # Calculate opponent defensive strength (points allowed)
            # This would be more sophisticated in a real implementation
            data['opponent_defense_rating'] = data.groupby('opponent_id')[self.target_column].transform(
                lambda x: x.ewm(span=5, min_periods=1).mean().shift(1)
            )
            if 'opponent_defense_rating' not in self.feature_columns:
                self.feature_columns.append('opponent_defense_rating')
        
        # Add game sequence features
        data['team_game_number'] = data.groupby('team_id').cumcount() + 1
        if 'team_game_number' not in self.feature_columns:
            self.feature_columns.append('team_game_number')
        
        # Add days rest if we have game dates
        if 'date' in data.columns:
            data_sorted = data.sort_values(['team_id', 'date'])
            
            # Calculate days_rest (days since last game for each team)
            data_sorted['days_rest'] = data_sorted.groupby('team_id')['date'].diff().dt.days
            data_sorted['days_rest'] = data_sorted['days_rest'].fillna(2)  # Default rest for first game
            
            # Back-to-back flag (b2b_flag)
            data_sorted['b2b_flag'] = (data_sorted['days_rest'] <= 1).astype(int)
            
            # Add these temporal features to feature columns
            for temp_col in ['days_rest', 'b2b_flag']:
                if temp_col not in self.feature_columns:
                    self.feature_columns.append(temp_col)
            
            data = data_sorted
        
        return data

    def train_dataloader(self):
        """Return training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def val_dataloader(self):
        """Return validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def test_dataloader(self):
        """Return test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False  # Disabled for Windows compatibility
        )


class GameTotalsDataset(Dataset):
    """
    Dataset for team game total records
    """
    
    def __init__(self, data: pd.DataFrame, feature_columns: List[str], target_column: str):
        self.data = data.reset_index(drop=True)
        self.feature_columns = feature_columns
        self.target_column = target_column
        
        # Convert to tensors
        self.features = torch.FloatTensor(self.data[feature_columns].values)
        self.targets = torch.FloatTensor(self.data[target_column].values)
        
        # Store metadata
        self.game_ids = torch.LongTensor(self.data['game_id'].values)
        self.team_ids = torch.LongTensor(self.data['team_id'].values)
        if 'opponent_id' in self.data.columns:
            self.opponent_ids = torch.LongTensor(self.data['opponent_id'].values)
        else:
            self.opponent_ids = torch.zeros(len(self.data), dtype=torch.long)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        """Return features and targets for standard game totals model"""
        return (
            self.features[idx],
            self.targets[idx]
        )
    
    def _get_simplified_prediction(self, player: pd.Series) -> float:
        """Get simplified prediction based on player stats when model fails"""
        
        # Use basic stats to estimate points per game
        points = player.get('points', 0)
        games_played = player.get('games_played', 1)
        minutes_per_game = player.get('minutes_per_game', 20)
        
        # If we have points and games, use that
        if points > 0 and games_played > 0:
            return float(points / games_played)
        
        # Otherwise, estimate based on minutes and basic stats
        base_points = 0.0
        
        # Minutes-based baseline
        if minutes_per_game > 0:
            base_points = minutes_per_game * 0.3  # Rough estimate: 0.3 points per minute
        
        # Adjust based on other stats
        if 'field_goal_percentage' in player.index and not pd.isna(player['field_goal_percentage']):
            fg_pct = player['field_goal_percentage']
            if fg_pct > 0.5:
                base_points *= 1.2  # Good shooter bonus
            elif fg_pct < 0.4:
                base_points *= 0.8  # Poor shooter penalty
        
        # Add bonus for other stats
        for stat in ['rebounds', 'assists', 'steals', 'blocks', 'threes']:
            if stat in player.index and not pd.isna(player[stat]):
                base_points += float(player[stat]) * 0.1
        
        # Ensure reasonable range
        return max(0.0, min(base_points, 30.0))  # Cap at 30 points per game
