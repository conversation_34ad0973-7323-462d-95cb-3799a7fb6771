"""
⚙️ Game Totals Data Pipeline for WNBA Team Scoring Model

This module implements the PyTorch Lightning DataModule for loading and preprocessing
game-level data that aggregates player performances into team totals.
Uses the same time-based splits as the Player Points model for hierarchical consistency.
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from sklearn.preprocessing import QuantileTransformer, StandardScaler
from typing import Optional, List, Tuple, Dict
import os
import sys
from pathlib import Path

# Add step_01 to path to reuse the player model
sys.path.append(str(Path(__file__).parent.parent.parent / "step_01_player_points_model"))


class GameTotalsDataModule(pl.LightningDataModule):
    """
    🔧 Game Totals Data Pipeline for Team Scoring Predictions
    
    Features:
    - Consistent time-based splits with Player Points model (2020-2023 train, 2024 val, 2025 test)
    - Game-level aggregation from player performances
    - Team context features and opponent analysis
    - Rolling team performance metrics
    """
    
    def __init__(
        self,
        data_path: str,
        batch_size: int = 32,
        num_workers: int = 0,  # Set to 0 for Windows compatibility
        seq_len: int = 10,  # Game sequence length for team rolling features
        val_split_date: str = '2024-01-01',
        test_split_date: str = '2025-01-01',
        player_model_path: Optional[str] = None
    ):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.seq_len = seq_len
        self.val_split_date = val_split_date
        self.test_split_date = test_split_date
        self.player_model_path = player_model_path
        
        # Feature columns (will be determined from data)
        self.feature_columns = None
        self.target_column = 'team_total_points'
        
        # Scalers
        self.feature_scaler = None
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None

    def prepare_data(self):
        """Download/prepare data if needed"""
        # Check if data file exists
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing"""
        
        # Load the data
        print(f"Loading game data from: {self.data_path}")
        self.data = self._load_and_preprocess_data()
        
        # Determine feature columns
        self._setup_feature_columns()
        
        # Create time-based splits (consistent with Player Points model)
        train_data, val_data, test_data = self._create_time_splits()
        
        # Fit scalers on training data
        self._fit_scalers(train_data)
        
        # Apply feature engineering first (before scaling)
        train_data = self._add_rolling_features(train_data)
        val_data = self._add_rolling_features(val_data)
        test_data = self._add_rolling_features(test_data)

        # Update feature columns after rolling features are added
        self._setup_feature_columns()

        # Apply scaling
        train_data = self._apply_preprocessing(train_data)
        val_data = self._apply_preprocessing(val_data)
        test_data = self._apply_preprocessing(test_data)
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = GameTotalsDataset(
                train_data, self.feature_columns, self.target_column
            )
            self.val_dataset = GameTotalsDataset(
                val_data, self.feature_columns, self.target_column
            )
        
        if stage == "test" or stage is None:
            self.test_dataset = GameTotalsDataset(
                test_data, self.feature_columns, self.target_column
            )

    def _load_and_preprocess_data(self) -> pd.DataFrame:
        """Load and perform initial preprocessing of game-level data"""
        
        # Load the main game data file
        if self.data_path.endswith('.csv'):
            data = pd.read_csv(self.data_path)
        elif self.data_path.endswith('.parquet'):
            data = pd.read_parquet(self.data_path)
        else:
            raise ValueError("Data file must be CSV or Parquet format")
        
        print(f"Loaded {len(data)} game records")
        print(f"Columns: {list(data.columns)}")
        
        # Create a date column if it doesn't exist
        if 'date' not in data.columns and 'game_date' in data.columns:
            data['date'] = pd.to_datetime(data['game_date'])
        elif 'date' not in data.columns and 'season' in data.columns:
            # Map season numbers to actual years (season 10 = 2020, etc.)
            base_year = 2020
            season_offset = 10
            # Create synthetic game dates throughout the season
            data['date'] = data['season'].apply(lambda x: f"{base_year - (season_offset - x)}-06-01")
            data['date'] = pd.to_datetime(data['date'])
        elif 'date' not in data.columns:
            # Create synthetic dates for time-based splitting
            data['date'] = pd.date_range('2020-01-01', periods=len(data), freq='D')
        else:
            data['date'] = pd.to_datetime(data['date'])
        
        # Ensure we have required columns for team totals
        if self.target_column not in data.columns:
            # Try to create team_total_points from available columns
            if 'team_points' in data.columns:
                print("Using team_points as team_total_points")
                data['team_total_points'] = data['team_points']
            elif 'points' in data.columns:
                print("Using points as team_total_points")
                data['team_total_points'] = data['points']
            else:
                # Create synthetic team totals by aggregating player data if available
                print("Creating synthetic team_total_points from player aggregation")
                # This would typically aggregate from player-level data
                # For now, create realistic synthetic values
                np.random.seed(42)
                data['team_total_points'] = np.random.normal(80, 12, len(data)).round()
                data['team_total_points'] = np.clip(data['team_total_points'], 60, 120)
        
        # Remove any rows with missing target values
        data = data.dropna(subset=[self.target_column])
        
        # Add essential IDs if not present
        if 'team_id' not in data.columns:
            if 'team_abbreviation' in data.columns:
                data['team_id'] = pd.Categorical(data['team_abbreviation']).codes
            else:
                data['team_id'] = 0
        
        if 'game_id' not in data.columns:
            data['game_id'] = range(len(data))
        
        if 'opponent_id' not in data.columns:
            if 'opponent_abbreviation' in data.columns:
                data['opponent_id'] = pd.Categorical(data['opponent_abbreviation']).codes
            elif 'opponent' in data.columns:
                data['opponent_id'] = pd.Categorical(data['opponent']).codes
            else:
                # Create synthetic opponent data
                data['opponent_id'] = np.random.randint(0, 12, len(data))
        
        return data.sort_values(['date', 'team_id']).reset_index(drop=True)

    def _setup_feature_columns(self):
        """Determine which columns to use as features"""
        
        # Exclude non-feature columns
        exclude_columns = [
            'date', 'team_id', 'game_id', 'opponent_id',
            'team_abbreviation', 'opponent_abbreviation', 'opponent',
            'season', 'game_date',
            self.target_column
        ]
        
        # Select numeric columns as features
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        self.feature_columns = [col for col in numeric_columns if col not in exclude_columns]
        
        print(f"Using {len(self.feature_columns)} feature columns:")
        print(self.feature_columns)

    def _create_time_splits(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Create time-based train/validation/test splits
        
        Data Split Strategy (Consistent with Player Points Model):
        - Training: 2020-2023 (Multi-year historical data for pattern learning)
        - Validation: 2024 (Recent data for hyperparameter tuning)  
        - Test: 2025 (Most current data for performance evaluation)
        
        This ensures hierarchical consistency where game totals predictions
        can properly integrate with player-level predictions from Step 1.
        """
        
        # Sort by date for proper temporal ordering
        data_sorted = self.data.sort_values('date')
        
        # Create splits based on dates (same as Player Points model)
        train_mask = data_sorted['date'] < self.val_split_date
        val_mask = (data_sorted['date'] >= self.val_split_date) & (data_sorted['date'] < self.test_split_date)
        test_mask = data_sorted['date'] >= self.test_split_date
        
        train_data = data_sorted[train_mask].copy()
        val_data = data_sorted[val_mask].copy()
        test_data = data_sorted[test_mask].copy()
        
        print(f"Train set: {len(train_data)} games ({train_data['date'].min()} to {train_data['date'].max()})")
        print(f"Val set: {len(val_data)} games ({val_data['date'].min()} to {val_data['date'].max()})")
        print(f"Test set: {len(test_data)} games ({test_data['date'].min()} to {test_data['date'].max()})")
        
        # Show team distribution (consistent splits for hierarchical modeling)
        if 'team_abbreviation' in data_sorted.columns:
            print(f"Train teams: {train_data['team_abbreviation'].nunique()}")
            print(f"Val teams: {val_data['team_abbreviation'].nunique()}")
            print(f"Test teams: {test_data['team_abbreviation'].nunique()}")
        elif 'team_id' in data_sorted.columns:
            print(f"Train teams: {train_data['team_id'].nunique()}")
            print(f"Val teams: {val_data['team_id'].nunique()}")
            print(f"Test teams: {test_data['team_id'].nunique()}")
        
        return train_data, val_data, test_data

    def _fit_scalers(self, train_data: pd.DataFrame):
        """Fit scalers on training data"""
        
        # Robust scaling for features (handles outliers better)
        self.feature_scaler = QuantileTransformer(output_distribution='normal', random_state=42)
        self.feature_scaler.fit(train_data[self.feature_columns])
        
        print("Fitted QuantileTransformer on training features")

    def _apply_preprocessing(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply preprocessing (scaling only, rolling features added earlier)"""

        data = data.copy()

        # Apply feature scaling
        if self.feature_scaler is not None:
            data[self.feature_columns] = self.feature_scaler.transform(data[self.feature_columns])

        return data

    def _add_rolling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add rolling sequence features for each team"""
        
        data = data.copy()
        
        # Sort by team and date
        data = data.sort_values(['team_id', 'date'])
        
        # Core rolling features for team performance
        rolling_cols = []
        
        # Basic team stats that might be available
        basic_stats = ['field_goals_made', 'field_goals_attempted', 'three_pointers_made',
                      'three_pointers_attempted', 'free_throws_made', 'free_throws_attempted',
                      'rebounds', 'assists', 'steals', 'blocks', 'turnovers']
        
        for col in basic_stats:
            if col in data.columns:
                rolling_cols.append(col)
        
        # Add percentage-based stats if available
        percentage_stats = ['field_goal_percentage', 'three_point_percentage', 'free_throw_percentage']
        for col in percentage_stats:
            if col in data.columns:
                rolling_cols.append(col)
        
        if rolling_cols:
            # Calculate exponentially weighted moving averages (EWMA) for team trends
            for col in rolling_cols:
                ewm_col = f'{col}_ewm_{self.seq_len}'
                data[ewm_col] = data.groupby('team_id')[col].transform(
                    lambda x: x.ewm(span=self.seq_len, min_periods=1).mean()
                )
                
                # Add to feature columns
                if ewm_col not in self.feature_columns:
                    self.feature_columns.append(ewm_col)
        
        # Add team context features
        if 'team_total_points' in data.columns:
            # Rolling average team scoring
            data['team_scoring_avg'] = data.groupby('team_id')[self.target_column].transform(
                lambda x: x.ewm(span=5, min_periods=1).mean().shift(1)  # Shift to avoid leakage
            )
            if 'team_scoring_avg' not in self.feature_columns:
                self.feature_columns.append('team_scoring_avg')
        
        # Add opponent context if available
        if 'opponent_id' in data.columns:
            # Calculate opponent defensive strength (points allowed)
            # This would be more sophisticated in a real implementation
            data['opponent_defense_rating'] = data.groupby('opponent_id')[self.target_column].transform(
                lambda x: x.ewm(span=5, min_periods=1).mean().shift(1)
            )
            if 'opponent_defense_rating' not in self.feature_columns:
                self.feature_columns.append('opponent_defense_rating')
        
        # Add game sequence features
        data['team_game_number'] = data.groupby('team_id').cumcount() + 1
        if 'team_game_number' not in self.feature_columns:
            self.feature_columns.append('team_game_number')
        
        # Add days rest if we have game dates
        if 'date' in data.columns:
            data_sorted = data.sort_values(['team_id', 'date'])
            
            # Calculate days_rest (days since last game for each team)
            data_sorted['days_rest'] = data_sorted.groupby('team_id')['date'].diff().dt.days
            data_sorted['days_rest'] = data_sorted['days_rest'].fillna(2)  # Default rest for first game
            
            # Back-to-back flag (b2b_flag)
            data_sorted['b2b_flag'] = (data_sorted['days_rest'] <= 1).astype(int)
            
            # Add these temporal features to feature columns
            for temp_col in ['days_rest', 'b2b_flag']:
                if temp_col not in self.feature_columns:
                    self.feature_columns.append(temp_col)
            
            data = data_sorted
        
        return data

    def train_dataloader(self):
        """Return training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def val_dataloader(self):
        """Return validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def test_dataloader(self):
        """Return test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False  # Disabled for Windows compatibility
        )


class GameTotalsDataset(Dataset):
    """
    Dataset for team game total records
    """
    
    def __init__(self, data: pd.DataFrame, feature_columns: List[str], target_column: str):
        self.data = data.reset_index(drop=True)
        self.feature_columns = feature_columns
        self.target_column = target_column
        
        # Convert to tensors
        self.features = torch.FloatTensor(self.data[feature_columns].values)
        self.targets = torch.FloatTensor(self.data[target_column].values)
        
        # Store metadata
        self.game_ids = torch.LongTensor(self.data['game_id'].values)
        self.team_ids = torch.LongTensor(self.data['team_id'].values)
        if 'opponent_id' in self.data.columns:
            self.opponent_ids = torch.LongTensor(self.data['opponent_id'].values)
        else:
            self.opponent_ids = torch.zeros(len(self.data), dtype=torch.long)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        """Return features and targets for standard game totals model"""
        return (
            self.features[idx],
            self.targets[idx]
        )
