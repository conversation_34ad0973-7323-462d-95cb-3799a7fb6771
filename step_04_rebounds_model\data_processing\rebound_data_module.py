"""
Rebound Data Module (Step 4 of 9) - Rebound-Specific Feature Engineering
========================================================================

This module implements the data processing pipeline for the Player Rebounds Model:
- Rebound-specific feature engineering
- Position encoding and standardization
- Opponent interaction features
- Advanced rebounding metrics
- Integration with hierarchical features from previous models

Key Components:
- ReboundDataModule: Main PyTorch Lightning data module
- ReboundFeatureEngineering: Rebound-specific feature creation
- OpponentReboundFeatures: Opponent interaction modeling
- PositionStandardization: Position-specific normalization
"""

import torch
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class ReboundFeatureEngineering:
    """
    Rebound-specific feature engineering class.
    
    Creates features specifically relevant to rebounding performance:
    - Height and wingspan advantages
    - Pace and shot volume indicators
    - Defensive positioning metrics
    - Historical rebounding patterns
    """
    
    def __init__(self):
        self.position_encoder = LabelEncoder()
        self.scalers = {}
        
    def create_physical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced physical attribute features using performance-based estimation from unified data."""
        features = df.copy()

        # 🏀 PERFORMANCE-BASED PHYSICAL ESTIMATION (UNIFIED DATA)

        # 1. HEIGHT ESTIMATION from rebounding performance + defensive ability
        wnba_avg_height = 71.0  # WNBA average height in inches

        # Base height estimation from performance indicators
        rebounding_indicator = features.get('rebounds_per_minute', 0) * 15  # Strong rebounders tend to be taller
        blocking_indicator = features.get('blocks_per_minute', 0) * 25     # Shot blockers are typically tall
        defensive_indicator = features.get('defensive_tier', 0) * 2        # Defensive tier correlates with size

        features['estimated_height'] = (
            wnba_avg_height +                    # Base height
            rebounding_indicator +               # Rebounding ability
            blocking_indicator +                 # Shot blocking ability
            defensive_indicator +                # Overall defensive impact
            np.random.normal(0, 1.5, len(features))  # Natural variation
        )

        # Ensure realistic height bounds (64-80 inches for WNBA)
        features['estimated_height'] = np.clip(features['estimated_height'], 64, 80)

        # Height analysis features
        features['height_z'] = (features['estimated_height'] - wnba_avg_height) / 3.5
        features['height_advantage'] = features['estimated_height'] - wnba_avg_height

        # Position-specific height expectations (estimated from performance patterns)
        features['height_vs_position'] = 0.0

        # Estimate position from performance if not available
        if 'POSITION' not in features.columns:
            # Estimate position from performance patterns
            features['estimated_position'] = 'SF'  # Default

            # Guards: High assists, lower rebounds/blocks
            guard_pattern = (
                (features.get('assists_per_minute', 0) > 0.15) &
                (features.get('rebounds_per_minute', 0) < 0.25)
            )
            features.loc[guard_pattern, 'estimated_position'] = 'PG'

            # Centers: High rebounds/blocks, lower assists
            center_pattern = (
                (features.get('rebounds_per_minute', 0) > 0.35) |
                (features.get('blocks_per_minute', 0) > 0.05)
            )
            features.loc[center_pattern, 'estimated_position'] = 'C'

            position_col = 'estimated_position'
        else:
            position_col = 'POSITION'

        # Position-specific height adjustments
        guard_mask = features[position_col].str.contains('PG|SG', na=False)
        forward_mask = features[position_col].str.contains('SF|PF', na=False)
        center_mask = features[position_col].str.contains('C', na=False)

        features.loc[guard_mask, 'height_vs_position'] = features.loc[guard_mask, 'estimated_height'] - 68.0
        features.loc[forward_mask, 'height_vs_position'] = features.loc[forward_mask, 'estimated_height'] - 72.0
        features.loc[center_mask, 'height_vs_position'] = features.loc[center_mask, 'estimated_height'] - 75.0

        # 2. WINGSPAN ESTIMATION from steals + defensive reach indicators
        # Steals indicate wingspan/reach ability, defensive stats show positioning
        steal_indicator = features.get('steals_per_minute', 0) * 20      # Steals require reach
        defensive_indicator = features.get('defensive_stats', 0) * 0.05  # Overall defensive ability
        base_wingspan_ratio = 1.05  # Typical wingspan-to-height ratio

        # Performance-based wingspan estimation
        features['estimated_wingspan'] = (
            features['estimated_height'] * base_wingspan_ratio +  # Base wingspan
            steal_indicator +                                     # Steal ability indicates reach
            defensive_indicator +                                 # Defensive positioning
            np.random.normal(0, 2.0, len(features))              # Natural variation
        )

        # Position-specific wingspan adjustments based on performance patterns
        features['wingspan_bonus'] = 0.0

        # Guards with high steals get wingspan bonus
        high_steal_guards = guard_mask & (features.get('steals_per_minute', 0) > 0.08)
        features.loc[high_steal_guards, 'wingspan_bonus'] = 2.0

        # Centers with high defensive stats get wingspan bonus
        high_def_centers = center_mask & (features.get('defensive_tier', 0) > 1)
        features.loc[high_def_centers, 'wingspan_bonus'] = 3.0

        # Forwards get moderate bonus
        features.loc[forward_mask, 'wingspan_bonus'] = 1.5

        features['estimated_wingspan'] += features['wingspan_bonus']

        # Ensure realistic wingspan bounds
        features['estimated_wingspan'] = np.clip(features['estimated_wingspan'], 66, 85)

        # Wingspan analysis features
        features['wingspan_to_height_ratio'] = features['estimated_wingspan'] / features['estimated_height']
        features['wingspan_advantage'] = features['estimated_wingspan'] - features['estimated_height']

        # 3. VERTICAL LEAP ESTIMATION from blocks + athletic performance indicators
        base_vertical = 24.0  # WNBA average vertical leap

        # Performance indicators for vertical leap
        blocking_indicator = features.get('blocks_per_minute', 0) * 40    # Shot blocking requires jumping
        steal_indicator = features.get('steals_per_minute', 0) * 15       # Steals indicate quickness/athleticism
        athletic_indicator = features.get('defensive_stats', 0) * 0.03    # Overall athleticism

        # Performance-based vertical leap estimation
        features['estimated_vertical'] = (
            base_vertical +                                      # Base vertical
            blocking_indicator +                                 # Shot blocking ability
            steal_indicator +                                    # Athletic quickness
            athletic_indicator +                                 # Overall athleticism
            np.random.normal(0, 2.5, len(features))            # Natural variation
        )

        # Position-specific adjustments based on performance patterns
        # Guards: Higher vertical (more athletic, less size-dependent)
        features.loc[guard_mask, 'estimated_vertical'] += 2.0

        # Centers: Lower vertical (more size-dependent, less jumping)
        features.loc[center_mask, 'estimated_vertical'] -= 2.0

        # High-block players get vertical bonus regardless of position
        high_block_players = features.get('blocks_per_minute', 0) > 0.05
        features.loc[high_block_players, 'estimated_vertical'] += 3.0

        # Ensure realistic vertical leap bounds (18-35 inches for WNBA)
        features['estimated_vertical'] = np.clip(features['estimated_vertical'], 18, 35)

        # 4. REACH CALCULATION (Height + Wingspan + Vertical)
        features['standing_reach'] = features['estimated_height'] * 1.33 + features['estimated_wingspan'] * 0.1
        features['max_reach'] = features['standing_reach'] + features['estimated_vertical']
        features['reach_advantage'] = features['max_reach'] - 108.0  # Average max reach

        # 5. REBOUND STRENGTH INDEX (Performance-based composite metric)
        # Combine physical estimates with actual rebounding performance
        physical_component = (
            features['height_advantage'] * 0.25 +
            features['wingspan_advantage'] * 0.25 +
            features['estimated_vertical'] * 0.15
        )

        performance_component = (
            features.get('rebounds_per_minute', 0) * 20 +      # Actual rebounding ability
            features.get('defensive_tier', 0) * 3 +            # Defensive impact
            features.get('blocks_per_minute', 0) * 15          # Shot blocking (size indicator)
        )

        features['rebound_strength_index'] = physical_component + performance_component

        # 6. POSITION-SPECIFIC ADVANTAGES (Performance-based)
        # Use estimated position or actual position
        pos_col = position_col if 'POSITION' in features.columns else 'estimated_position'

        features['is_big'] = (features.get(pos_col, '').str.contains('C|PF', na=False)).astype(int)
        features['is_guard'] = (features.get(pos_col, '').str.contains('PG|SG', na=False)).astype(int)
        features['is_forward'] = (features.get(pos_col, '').str.contains('SF|PF', na=False)).astype(int)

        # Performance-based mismatch indicators
        features['size_mismatch_potential'] = (
            (features['height_advantage'] > 3.0) |                    # Height advantage
            (features.get('rebounds_per_minute', 0) > 0.4)           # Exceptional rebounding
        ).astype(int)

        features['athletic_advantage'] = (
            (features['estimated_vertical'] > 26.0) |                # High vertical
            (features.get('steals_per_minute', 0) > 0.1) |          # High steals (athleticism)
            (features.get('blocks_per_minute', 0) > 0.05)           # High blocks (athleticism)
        ).astype(int)

        # 7. REBOUNDING RADIUS (Performance + physical space control)
        physical_radius = (
            features['wingspan_advantage'] * 0.4 +
            features['height_advantage'] * 0.3
        )

        performance_radius = (
            features.get('rebounds_per_minute', 0) * 10 +           # Actual rebounding reach
            features.get('defensive_tier', 0) * 1.5                 # Defensive positioning
        )

        features['rebounding_radius'] = physical_radius + performance_radius

        return features
    
    def create_pace_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create pace and shot volume features."""
        features = df.copy()
        
        # Team pace indicators
        if 'PACE' in features.columns:
            features['pace_z'] = (features['PACE'] - features['PACE'].mean()) / features['PACE'].std()
            features['high_pace'] = (features['PACE'] > features['PACE'].quantile(0.75)).astype(int)
        
        # Shot volume indicators
        shot_cols = ['FGA', 'FG3A', 'FTA']
        for col in shot_cols:
            if col in features.columns:
                features[f'{col}_per_min'] = features[col] / (features.get('MIN', 1) + 1e-8)
        
        # Offensive rebound opportunities
        if 'FGA' in features.columns:
            features['shot_volume_opp'] = features['FGA'] * 0.7  # Approximate miss rate
        
        return features
    
    def create_defensive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create defensive positioning and rebounding features."""
        features = df.copy()
        
        # Defensive metrics
        if 'DREB' in features.columns:
            features['dreb_rate'] = features['DREB'] / (features.get('MIN', 1) + 1e-8)
            features['total_reb_rate'] = (features['DREB'] + features.get('OREB', 0)) / (features.get('MIN', 1) + 1e-8)
        
        # Defensive positioning
        if 'STL' in features.columns and 'BLK' in features.columns:
            features['defensive_activity'] = features['STL'] + features['BLK']
            features['def_activity_rate'] = features['defensive_activity'] / (features.get('MIN', 1) + 1e-8)
        
        # Rebounding efficiency
        if 'DREB' in features.columns and 'FGA' in features.columns:
            features['dreb_efficiency'] = features['DREB'] / (features['FGA'] + 1e-8)
        
        return features
    
    def create_historical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create historical rebounding pattern features."""
        features = df.copy()
        
        # Sort by player and date for rolling calculations
        if 'PLAYER_ID' in features.columns and 'GAME_DATE' in features.columns:
            features = features.sort_values(['PLAYER_ID', 'GAME_DATE'])
            
            # Rolling averages for key rebounding metrics
            rebound_cols = ['DREB', 'OREB', 'REB']
            for col in rebound_cols:
                if col in features.columns:
                    features[f'{col}_rolling_3'] = features.groupby('PLAYER_ID')[col].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
                    features[f'{col}_rolling_10'] = features.groupby('PLAYER_ID')[col].rolling(10, min_periods=1).mean().reset_index(0, drop=True)
            
            # Recent form indicators
            if 'REB' in features.columns:
                features['recent_form'] = features.groupby('PLAYER_ID')['REB'].rolling(5, min_periods=1).mean().reset_index(0, drop=True)
                features['form_trend'] = features.groupby('PLAYER_ID')['recent_form'].pct_change().reset_index(0, drop=True)
        
        return features
    
    def create_opportunity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced rebound opportunity features with expert logic."""
        features = df.copy()

        # 🏀 EXPERT REBOUND OPPORTUNITY ANALYSIS

        # 1. SHOT QUALITY AND MISS PROBABILITY (Unified Data Columns)
        if 'field_goals_attempted' in features.columns:
            features['shot_volume'] = features['field_goals_attempted']
            fg_pct = features.get('field_goal_percentage', 0.45)
            features['estimated_misses'] = features['field_goals_attempted'] * (1 - fg_pct)

            # Shot distance impact on rebound probability
            if 'three_pointers_attempted' in features.columns:
                three_pct = features.get('three_point_percentage', 0.35)
                two_pt_attempts = features['field_goals_attempted'] - features['three_pointers_attempted']
                two_pt_pct = 0.52  # Estimate 2-point percentage

                # Long rebounds from 3-pointers (guards benefit)
                features['long_rebound_opportunities'] = features['three_pointers_attempted'] * (1 - three_pct) * 0.7

                # Short rebounds from 2-pointers (bigs benefit)
                features['short_rebound_opportunities'] = two_pt_attempts * (1 - two_pt_pct) * 0.8
            else:
                # Estimate if 3PA not available - use 'threes' column from unified data
                if 'threes' in features.columns:
                    # Estimate attempts from makes (assuming ~35% 3P%)
                    estimated_3pa = features['threes'] / 0.35
                    features['long_rebound_opportunities'] = estimated_3pa * 0.65 * 0.7
                    features['short_rebound_opportunities'] = (features['field_goals_attempted'] - estimated_3pa) * 0.48 * 0.8
                else:
                    # Default estimation
                    estimated_3pa = features['field_goals_attempted'] * 0.35
                    features['long_rebound_opportunities'] = estimated_3pa * 0.65 * 0.7
                    features['short_rebound_opportunities'] = (features['field_goals_attempted'] - estimated_3pa) * 0.48 * 0.8

        # 2. FREE THROW REBOUND OPPORTUNITIES (Unified Data)
        if 'free_throws_attempted' in features.columns:
            ft_pct = features.get('free_throw_percentage', 0.80)
            features['ft_rebound_opportunities'] = features['free_throws_attempted'] * (1 - ft_pct)

            # Technical free throws (no rebounding)
            features['technical_ft_rate'] = 0.05  # Estimate
            features['reboundable_ft_misses'] = features['ft_rebound_opportunities'] * (1 - features['technical_ft_rate'])
        else:
            # Estimate from free_throws_made if available
            if 'free_throws_made' in features.columns:
                ft_pct = features.get('free_throw_percentage', 0.80)
                estimated_fta = features['free_throws_made'] / ft_pct
                features['ft_rebound_opportunities'] = estimated_fta * (1 - ft_pct)
                features['technical_ft_rate'] = 0.05
                features['reboundable_ft_misses'] = features['ft_rebound_opportunities'] * (1 - features['technical_ft_rate'])
            else:
                features['ft_rebound_opportunities'] = 0
                features['reboundable_ft_misses'] = 0

        # 3. MINUTES-BASED OPPORTUNITIES (Unified Data)
        minutes_col = 'minutes_played' if 'minutes_played' in features.columns else 'minutes_per_game'
        if minutes_col in features.columns:
            features['min_z'] = (features[minutes_col] - features[minutes_col].mean()) / (features[minutes_col].std() + 1e-8)
            features['high_minutes'] = (features[minutes_col] > features[minutes_col].quantile(0.75)).astype(int)

            # Opportunities per minute
            features['opportunities_per_minute'] = features.get('estimated_misses', 0) / (features[minutes_col] + 1e-6)
            features['high_opportunity_games'] = (features['opportunities_per_minute'] > 1.5).astype(int)

        # 4. REBOUND OPPORTUNITY INDEX (Composite metric)
        features['rebound_opportunity_index'] = (
            features.get('short_rebound_opportunities', 0) * 1.2 +  # Favor close rebounds
            features.get('long_rebound_opportunities', 0) * 0.8 +   # Long rebounds harder
            features.get('reboundable_ft_misses', 0) * 1.5          # FT rebounds valuable
        )

        # 5. SHOT ANGLE AND DISTANCE IMPACT
        # Estimate shot distribution impact on rebound location
        features['paint_shot_rate'] = 0.35  # Estimate paint shots
        features['mid_range_rate'] = 0.30   # Mid-range shots
        features['three_point_rate'] = 0.35 # Three-point shots

        # Rebound angle predictability
        features['predictable_rebound_rate'] = (
            features['paint_shot_rate'] * 0.8 +      # Paint shots = predictable rebounds
            features['mid_range_rate'] * 0.6 +       # Mid-range = somewhat predictable
            features['three_point_rate'] * 0.3       # 3-pointers = unpredictable
        )

        # 6. TEAM REBOUNDING CONTEXT
        if 'TEAM_ID' in features.columns:
            team_stats = features.groupby('TEAM_ID').agg({
                'REB': ['mean', 'std'],
                'DREB': ['mean', 'std'],
                'OREB': ['mean', 'std'],
                'FGA': ['mean'],
                'FG_PCT': ['mean']
            }).reset_index()

            team_stats.columns = ['TEAM_ID', 'team_reb_mean', 'team_reb_std',
                                 'team_dreb_mean', 'team_dreb_std',
                                 'team_oreb_mean', 'team_oreb_std',
                                 'team_fga_mean', 'team_fg_pct_mean']

            features = features.merge(team_stats, on='TEAM_ID', how='left')

            # Team rebounding rates
            features['team_oreb_rate'] = features['team_oreb_mean'] / (features['team_oreb_mean'] + 30)  # Estimate opp dreb
            features['team_dreb_rate'] = features['team_dreb_mean'] / (features['team_dreb_mean'] + 10)  # Estimate opp oreb

        return features

    def create_positioning_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced positioning and boxout features with expert rebounding logic."""
        features = df.copy()

        # 🏀 EXPERT POSITIONING AND BOXOUT ANALYSIS

        # Define position masks at the beginning
        position_col = 'POSITION' if 'POSITION' in features.columns else 'estimated_position'
        guard_mask = features.get(position_col, '').str.contains('PG|SG', na=False)
        forward_mask = features.get(position_col, '').str.contains('PF|SF', na=False)
        center_mask = features.get(position_col, '').str.contains('C', na=False)

        # 1. BOXOUT STRENGTH ESTIMATION (Performance-based from unified data)
        if 'steals' in features.columns and 'blocks' in features.columns:
            # Use unified data columns
            minutes_col = 'minutes_played' if 'minutes_played' in features.columns else 'minutes_per_game'

            # Defensive activity indicates positioning awareness
            defensive_iq = (features['steals'] + features['blocks']) / (features.get(minutes_col, 1) + 1e-8)

            # Use turnovers as positioning aggression indicator
            positioning_aggression = features.get('turnovers', 2) / (features.get(minutes_col, 1) + 1e-8)

            # Combine with physical attributes for boxout strength
            physical_strength = features.get('rebound_strength_index', 0) / 20.0  # Normalize

            features['boxout_strength'] = (
                defensive_iq * 0.4 +                    # Defensive awareness
                physical_strength * 0.4 +               # Physical capability
                positioning_aggression * 0.2            # Positioning aggression
            )
        else:
            # Estimate based on position and performance if detailed stats unavailable
            features['boxout_strength'] = 0.5  # Default

            # Position-based adjustments
            features.loc[center_mask, 'boxout_strength'] = 0.8
            features.loc[forward_mask, 'boxout_strength'] = 0.6
            features.loc[guard_mask, 'boxout_strength'] = 0.3

            # Performance-based adjustments
            high_rebounders = features.get('rebounds_per_minute', 0) > 0.3
            features.loc[high_rebounders, 'boxout_strength'] += 0.2

        # 2. DEFENSIVE REBOUNDING POSITIONING (Unified data)
        minutes_col = 'minutes_played' if 'minutes_played' in features.columns else 'minutes_per_game'

        # Use total rebounds and estimate defensive portion
        if 'rebounds' in features.columns:
            # Estimate defensive rebounds as ~75% of total rebounds
            estimated_dreb = features['rebounds'] * 0.75
            features['dreb_rate'] = estimated_dreb / (features.get(minutes_col, 1) + 1e-8)

            # Position-specific defensive rebounding expectations
            features['dreb_vs_position_expectation'] = 0.0

            # Expected DREB rates by position (per minute) - adjusted for unified data
            features.loc[center_mask, 'dreb_vs_position_expectation'] = features.loc[center_mask, 'dreb_rate'] - 0.25
            features.loc[forward_mask, 'dreb_vs_position_expectation'] = features.loc[forward_mask, 'dreb_rate'] - 0.18
            features.loc[guard_mask, 'dreb_vs_position_expectation'] = features.loc[guard_mask, 'dreb_rate'] - 0.12

            # Defensive rebounding radius (area of influence)
            features['def_reb_radius'] = (
                features['boxout_strength'] * 0.6 +
                features.get('wingspan_advantage', 0) * 0.4 / 10.0  # Normalize wingspan
            )
        else:
            # Use rebounds_per_minute if available
            features['dreb_rate'] = features.get('rebounds_per_minute', 0) * 0.75
            features['dreb_vs_position_expectation'] = 0.0
            features['def_reb_radius'] = features['boxout_strength'] * 0.6

        # 3. OFFENSIVE REBOUNDING POSITIONING (Unified data)
        # Estimate offensive rebounds as ~25% of total rebounds
        if 'rebounds' in features.columns:
            estimated_oreb = features['rebounds'] * 0.25
            features['oreb_rate'] = estimated_oreb / (features.get(minutes_col, 1) + 1e-8)

            # Offensive rebounding requires different skills than defensive
            features['off_reb_positioning'] = (
                features.get('estimated_vertical', 24) / 30.0 * 0.5 +    # Athleticism for tip-ins
                features.get('boxout_strength', 0.5) * 0.3 +              # Strength to fight for position
                (1 - features.get('is_guard', 0)) * 0.2                   # Size advantage
            )

            # Putback opportunity estimation using unified data
            if 'field_goals_attempted' in features.columns:
                team_miss_rate = 1 - features.get('field_goal_percentage', 0.45)
                features['putback_opportunities'] = features['field_goals_attempted'] * team_miss_rate * features['off_reb_positioning']
            else:
                # Estimate from other stats
                features['putback_opportunities'] = features['oreb_rate'] * 2.0  # Rough estimate
        else:
            features['oreb_rate'] = features.get('rebounds_per_minute', 0) * 0.25
            features['off_reb_positioning'] = 0.5  # Default
            features['putback_opportunities'] = features['oreb_rate'] * 2.0

        # 4. REBOUNDING MOTOR AND EFFORT (Unified data)
        # High-effort rebounders often have more rebounds than expected from physical traits
        if 'rebounds' in features.columns:
            expected_rebounds = (
                features.get('rebound_strength_index', 0) * 0.05 +  # Adjusted scale
                features.get(minutes_col, 20) * 0.15
            )
            features['rebounding_motor'] = features['rebounds'] / (expected_rebounds + 1e-8)
            features['high_motor_rebounder'] = (features['rebounding_motor'] > 1.2).astype(int)
        else:
            features['rebounding_motor'] = 1.0  # Default
            features['high_motor_rebounder'] = 0

        # 5. CONTESTED REBOUND ABILITY
        # Estimate ability to secure rebounds in traffic
        features['contested_reb_ability'] = (
            features.get('boxout_strength', 0.5) * 0.4 +
            features.get('estimated_vertical', 24) / 30.0 * 0.3 +
            features.get('wingspan_to_height_ratio', 1.05) * 0.3
        )

        # 6. REBOUND CHASE SPEED (Guards vs Bigs)
        # Guards are faster to long rebounds, bigs dominate close rebounds
        features['rebound_chase_speed'] = 0.5  # Default
        features.loc[guard_mask, 'rebound_chase_speed'] = 0.8      # Guards: fast
        features.loc[forward_mask, 'rebound_chase_speed'] = 0.6    # Forwards: medium
        features.loc[center_mask, 'rebound_chase_speed'] = 0.4     # Centers: slow but strong

        # 7. TIP-OUT RATE (Offensive rebounding skill) - Unified data
        if 'assists' in features.columns:
            # Players who tip out vs secure offensive rebounds
            estimated_oreb = features.get('rebounds', 0) * 0.25
            total_offensive_plays = estimated_oreb + features.get('assists', 0) * 0.1  # Estimate
            features['tip_out_rate'] = features.get('assists', 0) * 0.1 / (total_offensive_plays + 1e-8)
        else:
            features['tip_out_rate'] = 0.2  # Default

        # 8. BOXOUT FORCE (Ability to create space)
        features['boxout_force'] = (
            features.get('rebound_strength_index', 0) / 20.0 * 0.5 +  # Normalize
            features.get('boxout_strength', 0.5) * 0.5
        )

        # 9. OUTLET PASS ACCURACY (Defensive rebounding value-add) - Unified data
        if 'assists' in features.columns:
            # Good rebounders who can outlet increase team value
            estimated_dreb = features.get('rebounds', 0) * 0.75
            features['outlet_pass_accuracy'] = features['assists'] / (estimated_dreb + 1e-8)
            features['outlet_pass_accuracy'] = np.clip(features['outlet_pass_accuracy'], 0, 1)
        else:
            features['outlet_pass_accuracy'] = 0.5  # Default

        return features

    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Main feature engineering pipeline."""
        logger.info("Starting rebound feature engineering...")
        
        # Apply all feature engineering steps
        df = self.create_physical_features(df)
        df = self.create_pace_features(df)
        df = self.create_defensive_features(df)
        df = self.create_historical_features(df)
        df = self.create_opportunity_features(df)
        df = self.create_positioning_features(df)
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        
        logger.info(f"Feature engineering complete. Shape: {df.shape}")
        return df


class OpponentReboundFeatures:
    """
    Opponent interaction feature engineering for rebounding.
    
    Creates features that capture how opponent characteristics
    affect rebounding opportunities and success rates.
    """
    
    def __init__(self):
        self.opponent_scalers = {}
        
    def create_opponent_strength_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create opponent rebounding strength features."""
        features = df.copy()
        
        # Opponent rebounding metrics
        if 'OPP_DREB' in features.columns:
            features['opp_dreb_rate'] = features['OPP_DREB'] / (features.get('OPP_MIN', 1) + 1e-8)
            features['opp_total_reb_rate'] = (features['OPP_DREB'] + features.get('OPP_OREB', 0)) / (features.get('OPP_MIN', 1) + 1e-8)
        
        # Opponent size metrics
        if 'OPP_HEIGHT_AVG' in features.columns:
            features['height_disadvantage'] = features.get('HEIGHT', 0) - features['OPP_HEIGHT_AVG']
            features['size_mismatch'] = (features['height_disadvantage'] < -2).astype(int)
        
        # Opponent pace impact
        if 'OPP_PACE' in features.columns:
            features['pace_differential'] = features.get('PACE', 0) - features['OPP_PACE']
            features['pace_advantage'] = (features['pace_differential'] > 0).astype(int)
        
        return features
    
    def create_matchup_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create specific matchup features."""
        features = df.copy()
        
        # Rebounding matchup strength
        if 'DREB' in features.columns and 'OPP_DREB' in features.columns:
            features['reb_matchup_strength'] = features['DREB'] / (features['OPP_DREB'] + 1e-8)
        
        # Defensive pressure
        if 'OPP_STL' in features.columns and 'OPP_BLK' in features.columns:
            features['opp_defensive_pressure'] = features['OPP_STL'] + features['OPP_BLK']
        
        # Shot volume impact
        if 'OPP_FGA' in features.columns:
            features['opp_shot_volume'] = features['OPP_FGA']
            features['reb_opportunity_index'] = features['opp_shot_volume'] * 0.7  # Miss rate approximation
        
        return features
    
    def engineer_opponent_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Main opponent feature engineering pipeline."""
        logger.info("Starting opponent feature engineering...")
        
        df = self.create_opponent_strength_features(df)
        df = self.create_matchup_features(df)
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        
        logger.info(f"Opponent feature engineering complete. Shape: {df.shape}")
        return df


class ReboundDataset(Dataset):
    """
    PyTorch Dataset for rebound prediction.
    
    Handles feature preparation, position encoding, and target preparation
    for the Zero-Inflated Poisson model.
    """
    
    def __init__(self, features: np.ndarray, position_indices: np.ndarray,
                 opponent_features: np.ndarray, targets: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.position_indices = torch.LongTensor(position_indices)
        self.opponent_features = torch.FloatTensor(opponent_features)
        self.targets = torch.FloatTensor(targets)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return {
            'features': self.features[idx],
            'position_indices': self.position_indices[idx],
            'opponent_features': self.opponent_features[idx],
            'rebounds': self.targets[idx]
        }


class ReboundDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning DataModule for rebound prediction.
    
    Handles data loading, preprocessing, feature engineering,
    and dataset preparation for training and validation.
    """
    
    def __init__(
        self,
        data_path: str,
        batch_size: int = 32,
        num_workers: int = 4,
        val_split: float = 0.2,
        test_split: float = 0.1,
        target_column: str = 'REB',
        position_column: str = 'POSITION',
        min_minutes: float = 5.0,
        **kwargs
    ):
        super().__init__()
        self.data_path = Path(data_path)
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.val_split = val_split
        self.test_split = test_split
        self.target_column = target_column
        self.position_column = position_column
        self.min_minutes = min_minutes
        
        # Feature engineering modules
        self.feature_engineer = ReboundFeatureEngineering()
        self.opponent_engineer = OpponentReboundFeatures()
        
        # Data storage
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        # Feature information
        self.feature_names = []
        self.opponent_feature_names = []
        self.scalers = {}
        
    def prepare_data(self):
        """Prepare data (download, tokenize, etc.)."""
        if not self.data_path.exists():
            raise FileNotFoundError(f"Data path {self.data_path} does not exist")
        
        logger.info(f"Data path verified: {self.data_path}")
    
    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing."""
        logger.info("Setting up rebound data module...")
        
        # Load data
        data = self._load_data()
        
        # Feature engineering
        data = self.feature_engineer.engineer_features(data)
        data = self.opponent_engineer.engineer_opponent_features(data)
        
        # Filter data
        data = self._filter_data(data)
        
        # Prepare features and targets
        features, position_indices, opponent_features, targets = self._prepare_features_targets(data)
        
        # Split data
        train_idx, temp_idx = train_test_split(
            range(len(features)), 
            test_size=self.val_split + self.test_split, 
            random_state=42,
            stratify=position_indices
        )
        
        val_idx, test_idx = train_test_split(
            temp_idx,
            test_size=self.test_split / (self.val_split + self.test_split),
            random_state=42,
            stratify=position_indices[temp_idx]
        )
        
        # Create datasets
        self.train_dataset = ReboundDataset(
            features[train_idx], position_indices[train_idx],
            opponent_features[train_idx], targets[train_idx]
        )
        
        self.val_dataset = ReboundDataset(
            features[val_idx], position_indices[val_idx],
            opponent_features[val_idx], targets[val_idx]
        )
        
        self.test_dataset = ReboundDataset(
            features[test_idx], position_indices[test_idx],
            opponent_features[test_idx], targets[test_idx]
        )
        
        logger.info(f"Dataset sizes - Train: {len(self.train_dataset)}, "
                   f"Val: {len(self.val_dataset)}, Test: {len(self.test_dataset)}")
    
    def _load_data(self) -> pd.DataFrame:
        """Load data from various sources."""
        logger.info("Loading rebound data...")
        
        # Try to load from different possible sources
        possible_paths = [
            self.data_path / "unified_data.csv",
            self.data_path / "player_stats.csv",
            self.data_path / "consolidated_data.csv"
        ]
        
        data = None
        for path in possible_paths:
            if path.exists():
                logger.info(f"Loading data from {path}")
                data = pd.read_csv(path)
                break
        
        if data is None:
            # Create sample data for testing
            logger.warning("No data file found, creating sample data...")
            data = self._create_sample_data()
        
        return data
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample data for testing purposes."""
        np.random.seed(42)
        n_samples = 1000
        
        # Position mapping
        positions = ['PG', 'SG', 'SF', 'PF', 'C']
        position_weights = [0.15, 0.15, 0.2, 0.25, 0.25]  # Centers and forwards get more rebounds
        
        data = {
            'PLAYER_ID': np.random.randint(1, 100, n_samples),
            'TEAM_ID': np.random.randint(1, 13, n_samples),
            'GAME_DATE': pd.date_range('2023-01-01', periods=n_samples, freq='D')[:n_samples],
            'POSITION': np.random.choice(positions, n_samples, p=position_weights),
            'MIN': np.random.uniform(5, 40, n_samples),
            'FGA': np.random.poisson(8, n_samples),
            'FG3A': np.random.poisson(3, n_samples),
            'FTA': np.random.poisson(2, n_samples),
            'DREB': np.random.poisson(4, n_samples),
            'OREB': np.random.poisson(1, n_samples),
            'STL': np.random.poisson(1, n_samples),
            'BLK': np.random.poisson(0.5, n_samples),
            'HEIGHT': np.random.normal(72, 4, n_samples),
            'PACE': np.random.uniform(95, 105, n_samples),
            # Opponent features
            'OPP_DREB': np.random.poisson(35, n_samples),
            'OPP_OREB': np.random.poisson(10, n_samples),
            'OPP_FGA': np.random.poisson(85, n_samples),
            'OPP_HEIGHT_AVG': np.random.normal(72, 2, n_samples),
            'OPP_PACE': np.random.uniform(95, 105, n_samples),
            'OPP_STL': np.random.poisson(7, n_samples),
            'OPP_BLK': np.random.poisson(4, n_samples),
            'OPP_MIN': np.random.uniform(200, 250, n_samples)
        }
        
        df = pd.DataFrame(data)
        df['REB'] = df['DREB'] + df['OREB']
        
        # Adjust rebounds based on position
        position_multipliers = {'PG': 0.6, 'SG': 0.7, 'SF': 0.9, 'PF': 1.3, 'C': 1.5}
        for pos, mult in position_multipliers.items():
            mask = df['POSITION'] == pos
            df.loc[mask, 'REB'] = (df.loc[mask, 'REB'] * mult).round().astype(int)
            df.loc[mask, 'DREB'] = (df.loc[mask, 'DREB'] * mult).round().astype(int)
            df.loc[mask, 'OREB'] = (df.loc[mask, 'OREB'] * mult * 0.5).round().astype(int)
        
        return df
    
    def _filter_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Filter data based on minimum requirements."""
        logger.info("Filtering data...")
        
        # Remove players with insufficient minutes
        if 'MIN' in data.columns:
            data = data[data['MIN'] >= self.min_minutes]
        
        # Remove rows with missing essential data
        essential_cols = [self.target_column, self.position_column]
        data = data.dropna(subset=essential_cols)
        
        logger.info(f"Filtered data shape: {data.shape}")
        return data
    
    def _prepare_features_targets(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Prepare features and targets for model training."""
        logger.info("Preparing features and targets...")
        
        # Position encoding
        position_map = {'PG': 0, 'SG': 0, 'SF': 1, 'PF': 1, 'C': 2}  # Guard, Forward, Center
        position_indices = data[self.position_column].map(position_map).fillna(1).astype(int).values
        
        # Select feature columns
        feature_cols = [col for col in data.columns if col not in [
            self.target_column, self.position_column, 'PLAYER_ID', 'TEAM_ID', 'GAME_DATE'
        ] and not col.startswith('OPP_')]
        
        opponent_feature_cols = [col for col in data.columns if col.startswith('OPP_')]
        
        # Prepare features
        features = data[feature_cols].values
        opponent_features = data[opponent_feature_cols].values
        
        # Scale features
        from sklearn.preprocessing import StandardScaler
        self.scalers['features'] = StandardScaler()
        self.scalers['opponent'] = StandardScaler()
        
        features = self.scalers['features'].fit_transform(features)
        opponent_features = self.scalers['opponent'].fit_transform(opponent_features)
        
        # Prepare targets
        targets = data[self.target_column].values.reshape(-1, 1)
        
        self.feature_names = feature_cols
        self.opponent_feature_names = opponent_feature_cols
        
        logger.info(f"Features shape: {features.shape}")
        logger.info(f"Opponent features shape: {opponent_features.shape}")
        logger.info(f"Targets shape: {targets.shape}")
        
        return features, position_indices, opponent_features, targets
    
    def train_dataloader(self):
        """Return training dataloader."""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def val_dataloader(self):
        """Return validation dataloader."""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def test_dataloader(self):
        """Return test dataloader."""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get information about features."""
        return {
            'feature_names': self.feature_names,
            'opponent_feature_names': self.opponent_feature_names,
            'n_features': len(self.feature_names),
            'n_opponent_features': len(self.opponent_feature_names),
            'scalers': self.scalers
        }
