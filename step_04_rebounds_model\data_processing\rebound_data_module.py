"""
Rebound Data Module (Step 4 of 9) - Rebound-Specific Feature Engineering
========================================================================

This module implements the data processing pipeline for the Player Rebounds Model:
- Rebound-specific feature engineering
- Position encoding and standardization
- Opponent interaction features
- Advanced rebounding metrics
- Integration with hierarchical features from previous models

Key Components:
- ReboundDataModule: Main PyTorch Lightning data module
- ReboundFeatureEngineering: Rebound-specific feature creation
- OpponentReboundFeatures: Opponent interaction modeling
- PositionStandardization: Position-specific normalization
"""

import torch
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class ReboundFeatureEngineering:
    """
    Rebound-specific feature engineering class.
    
    Creates features specifically relevant to rebounding performance:
    - Height and wingspan advantages
    - Pace and shot volume indicators
    - Defensive positioning metrics
    - Historical rebounding patterns
    """
    
    def __init__(self):
        self.position_encoder = LabelEncoder()
        self.scalers = {}
        
    def create_physical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced physical attribute features with expert rebounding logic."""
        features = df.copy()

        # 🏀 EXPERT REBOUNDING PHYSICAL FEATURES

        # 1. HEIGHT ANALYSIS (Real WNBA data-driven)
        if 'HEIGHT' in features.columns:
            # Normalize height (WNBA average ~71 inches)
            wnba_avg_height = 71.0
            features['height_z'] = (features['HEIGHT'] - wnba_avg_height) / 3.5
            features['height_advantage'] = features['HEIGHT'] - wnba_avg_height

            # Position-specific height expectations
            features['height_vs_position'] = 0.0
            guard_mask = features.get('POSITION', '').str.contains('PG|SG', na=False)
            forward_mask = features.get('POSITION', '').str.contains('SF|PF', na=False)
            center_mask = features.get('POSITION', '').str.contains('C', na=False)

            features.loc[guard_mask, 'height_vs_position'] = features.loc[guard_mask, 'HEIGHT'] - 68.0  # Guard avg
            features.loc[forward_mask, 'height_vs_position'] = features.loc[forward_mask, 'HEIGHT'] - 72.0  # Forward avg
            features.loc[center_mask, 'height_vs_position'] = features.loc[center_mask, 'HEIGHT'] - 75.0  # Center avg
        else:
            # Estimate height from position if not available
            features['HEIGHT'] = 71.0  # Default
            features.loc[features.get('POSITION', '').str.contains('PG|SG', na=False), 'HEIGHT'] = 68.0
            features.loc[features.get('POSITION', '').str.contains('SF|PF', na=False), 'HEIGHT'] = 72.0
            features.loc[features.get('POSITION', '').str.contains('C', na=False), 'HEIGHT'] = 75.0
            features['height_z'] = (features['HEIGHT'] - 71.0) / 3.5
            features['height_advantage'] = features['HEIGHT'] - 71.0
            features['height_vs_position'] = 0.0

        # 2. WINGSPAN ESTIMATION (Expert formula based on height + position)
        if 'WINGSPAN' not in features.columns:
            # Expert wingspan estimation: typically 1.03-1.08x height for basketball players
            base_wingspan_ratio = 1.05  # Average

            # Position-specific wingspan advantages
            features['wingspan_bonus'] = 0.0
            features.loc[guard_mask, 'wingspan_bonus'] = np.random.normal(0.02, 0.01, guard_mask.sum())  # Guards: +2%
            features.loc[forward_mask, 'wingspan_bonus'] = np.random.normal(0.04, 0.015, forward_mask.sum())  # Forwards: +4%
            features.loc[center_mask, 'wingspan_bonus'] = np.random.normal(0.06, 0.02, center_mask.sum())  # Centers: +6%

            features['WINGSPAN'] = features['HEIGHT'] * (base_wingspan_ratio + features.get('wingspan_bonus', 0))

        # Wingspan-to-height ratio (key rebounding metric)
        features['wingspan_to_height_ratio'] = features['WINGSPAN'] / features['HEIGHT']
        features['wingspan_advantage'] = features['WINGSPAN'] - features['HEIGHT']

        # 3. VERTICAL LEAP ESTIMATION (Position + athleticism indicators)
        if 'VERTICAL_LEAP' not in features.columns:
            # Base vertical leap by position (inches)
            base_vertical = 24.0  # WNBA average

            # Position-specific vertical leap patterns
            features['VERTICAL_LEAP'] = base_vertical
            features.loc[guard_mask, 'VERTICAL_LEAP'] = np.random.normal(26.0, 3.0, guard_mask.sum())  # Guards: higher
            features.loc[forward_mask, 'VERTICAL_LEAP'] = np.random.normal(24.0, 2.5, forward_mask.sum())  # Forwards: average
            features.loc[center_mask, 'VERTICAL_LEAP'] = np.random.normal(22.0, 2.0, center_mask.sum())  # Centers: lower

            # Adjust based on athleticism indicators
            if 'STL' in features.columns and 'BLK' in features.columns:
                athleticism_score = (features['STL'] + features['BLK']) / 2
                features['VERTICAL_LEAP'] += athleticism_score * 0.5  # Athletic players jump higher

        # 4. REACH CALCULATION (Height + Wingspan + Vertical)
        features['standing_reach'] = features['HEIGHT'] * 1.33 + features['WINGSPAN'] * 0.1  # Expert formula
        features['max_reach'] = features['standing_reach'] + features['VERTICAL_LEAP']
        features['reach_advantage'] = features['max_reach'] - 108.0  # Average max reach

        # 5. REBOUND STRENGTH INDEX (Composite physical metric)
        features['rebound_strength_index'] = (
            features['height_advantage'] * 0.3 +
            features['wingspan_advantage'] * 0.4 +
            features['VERTICAL_LEAP'] * 0.2 +
            features.get('wingspan_to_height_ratio', 1.05) * 10.0  # Normalize ratio
        )

        # 6. POSITION-SPECIFIC PHYSICAL ADVANTAGES
        features['is_big'] = (features.get('POSITION', '').str.contains('C|PF', na=False)).astype(int)
        features['is_guard'] = (features.get('POSITION', '').str.contains('PG|SG', na=False)).astype(int)
        features['is_forward'] = (features.get('POSITION', '').str.contains('SF|PF', na=False)).astype(int)

        # Physical mismatch indicators
        features['size_mismatch_potential'] = (
            features['height_advantage'] > 3.0
        ).astype(int)

        features['athletic_advantage'] = (
            features['VERTICAL_LEAP'] > 26.0
        ).astype(int)

        # 7. REBOUNDING RADIUS (Physical space control)
        features['rebounding_radius'] = (
            features['wingspan_advantage'] * 0.6 +
            features['height_advantage'] * 0.4
        )

        return features
    
    def create_pace_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create pace and shot volume features."""
        features = df.copy()
        
        # Team pace indicators
        if 'PACE' in features.columns:
            features['pace_z'] = (features['PACE'] - features['PACE'].mean()) / features['PACE'].std()
            features['high_pace'] = (features['PACE'] > features['PACE'].quantile(0.75)).astype(int)
        
        # Shot volume indicators
        shot_cols = ['FGA', 'FG3A', 'FTA']
        for col in shot_cols:
            if col in features.columns:
                features[f'{col}_per_min'] = features[col] / (features.get('MIN', 1) + 1e-8)
        
        # Offensive rebound opportunities
        if 'FGA' in features.columns:
            features['shot_volume_opp'] = features['FGA'] * 0.7  # Approximate miss rate
        
        return features
    
    def create_defensive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create defensive positioning and rebounding features."""
        features = df.copy()
        
        # Defensive metrics
        if 'DREB' in features.columns:
            features['dreb_rate'] = features['DREB'] / (features.get('MIN', 1) + 1e-8)
            features['total_reb_rate'] = (features['DREB'] + features.get('OREB', 0)) / (features.get('MIN', 1) + 1e-8)
        
        # Defensive positioning
        if 'STL' in features.columns and 'BLK' in features.columns:
            features['defensive_activity'] = features['STL'] + features['BLK']
            features['def_activity_rate'] = features['defensive_activity'] / (features.get('MIN', 1) + 1e-8)
        
        # Rebounding efficiency
        if 'DREB' in features.columns and 'FGA' in features.columns:
            features['dreb_efficiency'] = features['DREB'] / (features['FGA'] + 1e-8)
        
        return features
    
    def create_historical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create historical rebounding pattern features."""
        features = df.copy()
        
        # Sort by player and date for rolling calculations
        if 'PLAYER_ID' in features.columns and 'GAME_DATE' in features.columns:
            features = features.sort_values(['PLAYER_ID', 'GAME_DATE'])
            
            # Rolling averages for key rebounding metrics
            rebound_cols = ['DREB', 'OREB', 'REB']
            for col in rebound_cols:
                if col in features.columns:
                    features[f'{col}_rolling_3'] = features.groupby('PLAYER_ID')[col].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
                    features[f'{col}_rolling_10'] = features.groupby('PLAYER_ID')[col].rolling(10, min_periods=1).mean().reset_index(0, drop=True)
            
            # Recent form indicators
            if 'REB' in features.columns:
                features['recent_form'] = features.groupby('PLAYER_ID')['REB'].rolling(5, min_periods=1).mean().reset_index(0, drop=True)
                features['form_trend'] = features.groupby('PLAYER_ID')['recent_form'].pct_change().reset_index(0, drop=True)
        
        return features
    
    def create_opportunity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced rebound opportunity features with expert logic."""
        features = df.copy()

        # 🏀 EXPERT REBOUND OPPORTUNITY ANALYSIS

        # 1. SHOT QUALITY AND MISS PROBABILITY
        if 'FGA' in features.columns:
            features['shot_volume'] = features['FGA']
            fg_pct = features.get('FG_PCT', 0.45)
            features['estimated_misses'] = features['FGA'] * (1 - fg_pct)

            # Shot distance impact on rebound probability
            if 'FG3A' in features.columns:
                three_pct = features.get('FG3_PCT', 0.35)
                two_pt_attempts = features['FGA'] - features['FG3A']
                two_pt_pct = features.get('FG2_PCT', 0.52)

                # Long rebounds from 3-pointers (guards benefit)
                features['long_rebound_opportunities'] = features['FG3A'] * (1 - three_pct) * 0.7

                # Short rebounds from 2-pointers (bigs benefit)
                features['short_rebound_opportunities'] = two_pt_attempts * (1 - two_pt_pct) * 0.8
            else:
                # Estimate if 3PA not available
                estimated_3pa = features['FGA'] * 0.35  # WNBA average
                features['long_rebound_opportunities'] = estimated_3pa * 0.65 * 0.7
                features['short_rebound_opportunities'] = (features['FGA'] - estimated_3pa) * 0.48 * 0.8

        # 2. FREE THROW REBOUND OPPORTUNITIES
        if 'FTA' in features.columns:
            ft_pct = features.get('FT_PCT', 0.80)
            features['ft_rebound_opportunities'] = features['FTA'] * (1 - ft_pct)

            # Technical free throws (no rebounding)
            features['technical_ft_rate'] = 0.05  # Estimate
            features['reboundable_ft_misses'] = features['ft_rebound_opportunities'] * (1 - features['technical_ft_rate'])

        # 3. MINUTES-BASED OPPORTUNITIES
        if 'MIN' in features.columns:
            features['min_z'] = (features['MIN'] - features['MIN'].mean()) / (features['MIN'].std() + 1e-8)
            features['high_minutes'] = (features['MIN'] > features['MIN'].quantile(0.75)).astype(int)

            # Opportunities per minute
            features['opportunities_per_minute'] = features.get('estimated_misses', 0) / (features['MIN'] + 1e-6)
            features['high_opportunity_games'] = (features['opportunities_per_minute'] > 1.5).astype(int)

        # 4. REBOUND OPPORTUNITY INDEX (Composite metric)
        features['rebound_opportunity_index'] = (
            features.get('short_rebound_opportunities', 0) * 1.2 +  # Favor close rebounds
            features.get('long_rebound_opportunities', 0) * 0.8 +   # Long rebounds harder
            features.get('reboundable_ft_misses', 0) * 1.5          # FT rebounds valuable
        )

        # 5. SHOT ANGLE AND DISTANCE IMPACT
        # Estimate shot distribution impact on rebound location
        features['paint_shot_rate'] = 0.35  # Estimate paint shots
        features['mid_range_rate'] = 0.30   # Mid-range shots
        features['three_point_rate'] = 0.35 # Three-point shots

        # Rebound angle predictability
        features['predictable_rebound_rate'] = (
            features['paint_shot_rate'] * 0.8 +      # Paint shots = predictable rebounds
            features['mid_range_rate'] * 0.6 +       # Mid-range = somewhat predictable
            features['three_point_rate'] * 0.3       # 3-pointers = unpredictable
        )

        # 6. TEAM REBOUNDING CONTEXT
        if 'TEAM_ID' in features.columns:
            team_stats = features.groupby('TEAM_ID').agg({
                'REB': ['mean', 'std'],
                'DREB': ['mean', 'std'],
                'OREB': ['mean', 'std'],
                'FGA': ['mean'],
                'FG_PCT': ['mean']
            }).reset_index()

            team_stats.columns = ['TEAM_ID', 'team_reb_mean', 'team_reb_std',
                                 'team_dreb_mean', 'team_dreb_std',
                                 'team_oreb_mean', 'team_oreb_std',
                                 'team_fga_mean', 'team_fg_pct_mean']

            features = features.merge(team_stats, on='TEAM_ID', how='left')

            # Team rebounding rates
            features['team_oreb_rate'] = features['team_oreb_mean'] / (features['team_oreb_mean'] + 30)  # Estimate opp dreb
            features['team_dreb_rate'] = features['team_dreb_mean'] / (features['team_dreb_mean'] + 10)  # Estimate opp oreb

        return features

    def create_positioning_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced positioning and boxout features with expert rebounding logic."""
        features = df.copy()

        # 🏀 EXPERT POSITIONING AND BOXOUT ANALYSIS

        # 1. BOXOUT STRENGTH ESTIMATION (Based on physical + defensive metrics)
        if 'STL' in features.columns and 'BLK' in features.columns and 'PF' in features.columns:
            # Defensive activity indicates positioning awareness
            defensive_iq = (features['STL'] + features['BLK']) / (features.get('MIN', 1) + 1e-8)

            # Fouls can indicate aggressive positioning (both good and bad)
            positioning_aggression = features['PF'] / (features.get('MIN', 1) + 1e-8)

            # Combine with physical attributes for boxout strength
            physical_strength = features.get('rebound_strength_index', 0)

            features['boxout_strength'] = (
                defensive_iq * 0.4 +                    # Defensive awareness
                physical_strength * 0.4 +               # Physical capability
                positioning_aggression * 0.2            # Positioning aggression
            )
        else:
            # Estimate based on position if defensive stats unavailable
            features['boxout_strength'] = 0.5  # Default
            features.loc[features.get('POSITION', '').str.contains('C', na=False), 'boxout_strength'] = 0.8
            features.loc[features.get('POSITION', '').str.contains('PF', na=False), 'boxout_strength'] = 0.7
            features.loc[features.get('POSITION', '').str.contains('SF', na=False), 'boxout_strength'] = 0.5
            features.loc[features.get('POSITION', '').str.contains('SG|PG', na=False), 'boxout_strength'] = 0.3

        # 2. DEFENSIVE REBOUNDING POSITIONING
        if 'DREB' in features.columns and 'MIN' in features.columns:
            features['dreb_rate'] = features['DREB'] / (features['MIN'] + 1e-8)

            # Position-specific defensive rebounding expectations
            features['dreb_vs_position_expectation'] = 0.0
            center_mask = features.get('POSITION', '').str.contains('C', na=False)
            forward_mask = features.get('POSITION', '').str.contains('PF|SF', na=False)
            guard_mask = features.get('POSITION', '').str.contains('PG|SG', na=False)

            # Expected DREB rates by position (per minute)
            features.loc[center_mask, 'dreb_vs_position_expectation'] = features.loc[center_mask, 'dreb_rate'] - 0.35
            features.loc[forward_mask, 'dreb_vs_position_expectation'] = features.loc[forward_mask, 'dreb_rate'] - 0.25
            features.loc[guard_mask, 'dreb_vs_position_expectation'] = features.loc[guard_mask, 'dreb_rate'] - 0.15

            # Defensive rebounding radius (area of influence)
            features['def_reb_radius'] = (
                features['boxout_strength'] * 0.6 +
                features.get('wingspan_advantage', 0) * 0.4
            )

        # 3. OFFENSIVE REBOUNDING POSITIONING
        if 'OREB' in features.columns and 'MIN' in features.columns:
            features['oreb_rate'] = features['OREB'] / (features['MIN'] + 1e-8)

            # Offensive rebounding requires different skills than defensive
            features['off_reb_positioning'] = (
                features.get('VERTICAL_LEAP', 24) / 30.0 * 0.5 +    # Athleticism for tip-ins
                features.get('boxout_strength', 0.5) * 0.3 +        # Strength to fight for position
                (1 - features.get('is_guard', 0)) * 0.2             # Size advantage
            )

            # Putback opportunity estimation
            if 'FGA' in features.columns:
                team_miss_rate = 1 - features.get('FG_PCT', 0.45)
                features['putback_opportunities'] = features['FGA'] * team_miss_rate * features['off_reb_positioning']

        # 4. REBOUNDING MOTOR AND EFFORT
        # High-effort rebounders often have more rebounds than expected from physical traits
        if 'REB' in features.columns:
            expected_rebounds = (
                features.get('rebound_strength_index', 0) * 0.1 +
                features.get('MIN', 20) * 0.15
            )
            features['rebounding_motor'] = features['REB'] / (expected_rebounds + 1e-8)
            features['high_motor_rebounder'] = (features['rebounding_motor'] > 1.2).astype(int)

        # 5. CONTESTED REBOUND ABILITY
        # Estimate ability to secure rebounds in traffic
        features['contested_reb_ability'] = (
            features.get('boxout_strength', 0.5) * 0.4 +
            features.get('VERTICAL_LEAP', 24) / 30.0 * 0.3 +
            features.get('wingspan_to_height_ratio', 1.05) * 0.3
        )

        # 6. REBOUND CHASE SPEED (Guards vs Bigs)
        # Guards are faster to long rebounds, bigs dominate close rebounds
        features['rebound_chase_speed'] = 0.5  # Default
        features.loc[guard_mask, 'rebound_chase_speed'] = 0.8      # Guards: fast
        features.loc[forward_mask, 'rebound_chase_speed'] = 0.6    # Forwards: medium
        features.loc[center_mask, 'rebound_chase_speed'] = 0.4     # Centers: slow but strong

        # 7. TIP-OUT RATE (Offensive rebounding skill)
        if 'OREB' in features.columns and 'AST' in features.columns:
            # Players who tip out vs secure offensive rebounds
            total_offensive_plays = features['OREB'] + features.get('AST', 0) * 0.1  # Estimate
            features['tip_out_rate'] = features.get('AST', 0) * 0.1 / (total_offensive_plays + 1e-8)

        # 8. BOXOUT FORCE (Ability to create space)
        features['boxout_force'] = (
            features.get('rebound_strength_index', 0) * 0.5 +
            features.get('boxout_strength', 0.5) * 0.5
        )

        # 9. OUTLET PASS ACCURACY (Defensive rebounding value-add)
        if 'AST' in features.columns and 'DREB' in features.columns:
            # Good rebounders who can outlet increase team value
            features['outlet_pass_accuracy'] = features['AST'] / (features['DREB'] + 1e-8)
            features['outlet_pass_accuracy'] = np.clip(features['outlet_pass_accuracy'], 0, 1)

        return features

    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Main feature engineering pipeline."""
        logger.info("Starting rebound feature engineering...")
        
        # Apply all feature engineering steps
        df = self.create_physical_features(df)
        df = self.create_pace_features(df)
        df = self.create_defensive_features(df)
        df = self.create_historical_features(df)
        df = self.create_opportunity_features(df)
        df = self.create_positioning_features(df)
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        
        logger.info(f"Feature engineering complete. Shape: {df.shape}")
        return df


class OpponentReboundFeatures:
    """
    Opponent interaction feature engineering for rebounding.
    
    Creates features that capture how opponent characteristics
    affect rebounding opportunities and success rates.
    """
    
    def __init__(self):
        self.opponent_scalers = {}
        
    def create_opponent_strength_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create opponent rebounding strength features."""
        features = df.copy()
        
        # Opponent rebounding metrics
        if 'OPP_DREB' in features.columns:
            features['opp_dreb_rate'] = features['OPP_DREB'] / (features.get('OPP_MIN', 1) + 1e-8)
            features['opp_total_reb_rate'] = (features['OPP_DREB'] + features.get('OPP_OREB', 0)) / (features.get('OPP_MIN', 1) + 1e-8)
        
        # Opponent size metrics
        if 'OPP_HEIGHT_AVG' in features.columns:
            features['height_disadvantage'] = features.get('HEIGHT', 0) - features['OPP_HEIGHT_AVG']
            features['size_mismatch'] = (features['height_disadvantage'] < -2).astype(int)
        
        # Opponent pace impact
        if 'OPP_PACE' in features.columns:
            features['pace_differential'] = features.get('PACE', 0) - features['OPP_PACE']
            features['pace_advantage'] = (features['pace_differential'] > 0).astype(int)
        
        return features
    
    def create_matchup_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create specific matchup features."""
        features = df.copy()
        
        # Rebounding matchup strength
        if 'DREB' in features.columns and 'OPP_DREB' in features.columns:
            features['reb_matchup_strength'] = features['DREB'] / (features['OPP_DREB'] + 1e-8)
        
        # Defensive pressure
        if 'OPP_STL' in features.columns and 'OPP_BLK' in features.columns:
            features['opp_defensive_pressure'] = features['OPP_STL'] + features['OPP_BLK']
        
        # Shot volume impact
        if 'OPP_FGA' in features.columns:
            features['opp_shot_volume'] = features['OPP_FGA']
            features['reb_opportunity_index'] = features['opp_shot_volume'] * 0.7  # Miss rate approximation
        
        return features
    
    def engineer_opponent_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Main opponent feature engineering pipeline."""
        logger.info("Starting opponent feature engineering...")
        
        df = self.create_opponent_strength_features(df)
        df = self.create_matchup_features(df)
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        
        logger.info(f"Opponent feature engineering complete. Shape: {df.shape}")
        return df


class ReboundDataset(Dataset):
    """
    PyTorch Dataset for rebound prediction.
    
    Handles feature preparation, position encoding, and target preparation
    for the Zero-Inflated Poisson model.
    """
    
    def __init__(self, features: np.ndarray, position_indices: np.ndarray,
                 opponent_features: np.ndarray, targets: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.position_indices = torch.LongTensor(position_indices)
        self.opponent_features = torch.FloatTensor(opponent_features)
        self.targets = torch.FloatTensor(targets)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return {
            'features': self.features[idx],
            'position_indices': self.position_indices[idx],
            'opponent_features': self.opponent_features[idx],
            'rebounds': self.targets[idx]
        }


class ReboundDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning DataModule for rebound prediction.
    
    Handles data loading, preprocessing, feature engineering,
    and dataset preparation for training and validation.
    """
    
    def __init__(
        self,
        data_path: str,
        batch_size: int = 32,
        num_workers: int = 4,
        val_split: float = 0.2,
        test_split: float = 0.1,
        target_column: str = 'REB',
        position_column: str = 'POSITION',
        min_minutes: float = 5.0,
        **kwargs
    ):
        super().__init__()
        self.data_path = Path(data_path)
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.val_split = val_split
        self.test_split = test_split
        self.target_column = target_column
        self.position_column = position_column
        self.min_minutes = min_minutes
        
        # Feature engineering modules
        self.feature_engineer = ReboundFeatureEngineering()
        self.opponent_engineer = OpponentReboundFeatures()
        
        # Data storage
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        # Feature information
        self.feature_names = []
        self.opponent_feature_names = []
        self.scalers = {}
        
    def prepare_data(self):
        """Prepare data (download, tokenize, etc.)."""
        if not self.data_path.exists():
            raise FileNotFoundError(f"Data path {self.data_path} does not exist")
        
        logger.info(f"Data path verified: {self.data_path}")
    
    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing."""
        logger.info("Setting up rebound data module...")
        
        # Load data
        data = self._load_data()
        
        # Feature engineering
        data = self.feature_engineer.engineer_features(data)
        data = self.opponent_engineer.engineer_opponent_features(data)
        
        # Filter data
        data = self._filter_data(data)
        
        # Prepare features and targets
        features, position_indices, opponent_features, targets = self._prepare_features_targets(data)
        
        # Split data
        train_idx, temp_idx = train_test_split(
            range(len(features)), 
            test_size=self.val_split + self.test_split, 
            random_state=42,
            stratify=position_indices
        )
        
        val_idx, test_idx = train_test_split(
            temp_idx,
            test_size=self.test_split / (self.val_split + self.test_split),
            random_state=42,
            stratify=position_indices[temp_idx]
        )
        
        # Create datasets
        self.train_dataset = ReboundDataset(
            features[train_idx], position_indices[train_idx],
            opponent_features[train_idx], targets[train_idx]
        )
        
        self.val_dataset = ReboundDataset(
            features[val_idx], position_indices[val_idx],
            opponent_features[val_idx], targets[val_idx]
        )
        
        self.test_dataset = ReboundDataset(
            features[test_idx], position_indices[test_idx],
            opponent_features[test_idx], targets[test_idx]
        )
        
        logger.info(f"Dataset sizes - Train: {len(self.train_dataset)}, "
                   f"Val: {len(self.val_dataset)}, Test: {len(self.test_dataset)}")
    
    def _load_data(self) -> pd.DataFrame:
        """Load data from various sources."""
        logger.info("Loading rebound data...")
        
        # Try to load from different possible sources
        possible_paths = [
            self.data_path / "unified_data.csv",
            self.data_path / "player_stats.csv",
            self.data_path / "consolidated_data.csv"
        ]
        
        data = None
        for path in possible_paths:
            if path.exists():
                logger.info(f"Loading data from {path}")
                data = pd.read_csv(path)
                break
        
        if data is None:
            # Create sample data for testing
            logger.warning("No data file found, creating sample data...")
            data = self._create_sample_data()
        
        return data
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample data for testing purposes."""
        np.random.seed(42)
        n_samples = 1000
        
        # Position mapping
        positions = ['PG', 'SG', 'SF', 'PF', 'C']
        position_weights = [0.15, 0.15, 0.2, 0.25, 0.25]  # Centers and forwards get more rebounds
        
        data = {
            'PLAYER_ID': np.random.randint(1, 100, n_samples),
            'TEAM_ID': np.random.randint(1, 13, n_samples),
            'GAME_DATE': pd.date_range('2023-01-01', periods=n_samples, freq='D')[:n_samples],
            'POSITION': np.random.choice(positions, n_samples, p=position_weights),
            'MIN': np.random.uniform(5, 40, n_samples),
            'FGA': np.random.poisson(8, n_samples),
            'FG3A': np.random.poisson(3, n_samples),
            'FTA': np.random.poisson(2, n_samples),
            'DREB': np.random.poisson(4, n_samples),
            'OREB': np.random.poisson(1, n_samples),
            'STL': np.random.poisson(1, n_samples),
            'BLK': np.random.poisson(0.5, n_samples),
            'HEIGHT': np.random.normal(72, 4, n_samples),
            'PACE': np.random.uniform(95, 105, n_samples),
            # Opponent features
            'OPP_DREB': np.random.poisson(35, n_samples),
            'OPP_OREB': np.random.poisson(10, n_samples),
            'OPP_FGA': np.random.poisson(85, n_samples),
            'OPP_HEIGHT_AVG': np.random.normal(72, 2, n_samples),
            'OPP_PACE': np.random.uniform(95, 105, n_samples),
            'OPP_STL': np.random.poisson(7, n_samples),
            'OPP_BLK': np.random.poisson(4, n_samples),
            'OPP_MIN': np.random.uniform(200, 250, n_samples)
        }
        
        df = pd.DataFrame(data)
        df['REB'] = df['DREB'] + df['OREB']
        
        # Adjust rebounds based on position
        position_multipliers = {'PG': 0.6, 'SG': 0.7, 'SF': 0.9, 'PF': 1.3, 'C': 1.5}
        for pos, mult in position_multipliers.items():
            mask = df['POSITION'] == pos
            df.loc[mask, 'REB'] = (df.loc[mask, 'REB'] * mult).round().astype(int)
            df.loc[mask, 'DREB'] = (df.loc[mask, 'DREB'] * mult).round().astype(int)
            df.loc[mask, 'OREB'] = (df.loc[mask, 'OREB'] * mult * 0.5).round().astype(int)
        
        return df
    
    def _filter_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Filter data based on minimum requirements."""
        logger.info("Filtering data...")
        
        # Remove players with insufficient minutes
        if 'MIN' in data.columns:
            data = data[data['MIN'] >= self.min_minutes]
        
        # Remove rows with missing essential data
        essential_cols = [self.target_column, self.position_column]
        data = data.dropna(subset=essential_cols)
        
        logger.info(f"Filtered data shape: {data.shape}")
        return data
    
    def _prepare_features_targets(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Prepare features and targets for model training."""
        logger.info("Preparing features and targets...")
        
        # Position encoding
        position_map = {'PG': 0, 'SG': 0, 'SF': 1, 'PF': 1, 'C': 2}  # Guard, Forward, Center
        position_indices = data[self.position_column].map(position_map).fillna(1).astype(int).values
        
        # Select feature columns
        feature_cols = [col for col in data.columns if col not in [
            self.target_column, self.position_column, 'PLAYER_ID', 'TEAM_ID', 'GAME_DATE'
        ] and not col.startswith('OPP_')]
        
        opponent_feature_cols = [col for col in data.columns if col.startswith('OPP_')]
        
        # Prepare features
        features = data[feature_cols].values
        opponent_features = data[opponent_feature_cols].values
        
        # Scale features
        from sklearn.preprocessing import StandardScaler
        self.scalers['features'] = StandardScaler()
        self.scalers['opponent'] = StandardScaler()
        
        features = self.scalers['features'].fit_transform(features)
        opponent_features = self.scalers['opponent'].fit_transform(opponent_features)
        
        # Prepare targets
        targets = data[self.target_column].values.reshape(-1, 1)
        
        self.feature_names = feature_cols
        self.opponent_feature_names = opponent_feature_cols
        
        logger.info(f"Features shape: {features.shape}")
        logger.info(f"Opponent features shape: {opponent_features.shape}")
        logger.info(f"Targets shape: {targets.shape}")
        
        return features, position_indices, opponent_features, targets
    
    def train_dataloader(self):
        """Return training dataloader."""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def val_dataloader(self):
        """Return validation dataloader."""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def test_dataloader(self):
        """Return test dataloader."""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get information about features."""
        return {
            'feature_names': self.feature_names,
            'opponent_feature_names': self.opponent_feature_names,
            'n_features': len(self.feature_names),
            'n_opponent_features': len(self.opponent_feature_names),
            'scalers': self.scalers
        }
