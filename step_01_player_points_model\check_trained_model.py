"""
🔍 Check Trained Model Performance

Load the best trained model and evaluate its performance with team consistency validation.
"""

import sys
import torch
import pandas as pd
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from models.player_points_model import PlayerPointsModel
from data_processing.wnba_data_module import WNBADataModule
from validation.consistency_checks import validate_player_consistency


def load_best_model():
    """Load the best trained model from checkpoints"""
    
    checkpoint_dir = Path("checkpoints")
    if not checkpoint_dir.exists():
        print("❌ No checkpoints directory found")
        return None
    
    # Find the best checkpoint (lowest validation loss)
    checkpoints = list(checkpoint_dir.glob("*.ckpt"))
    if not checkpoints:
        print("❌ No checkpoint files found")
        return None
    
    # Sort by validation loss (extract from filename)
    best_checkpoint = None
    best_val_loss = float('inf')
    
    for ckpt in checkpoints:
        try:
            # Extract val_loss from filename like "standard-player-points-epoch=02-val_loss=349.33.ckpt"
            parts = ckpt.stem.split('-')
            for part in parts:
                if part.startswith('val_loss='):
                    val_loss = float(part.split('=')[1])
                    if val_loss < best_val_loss:
                        best_val_loss = val_loss
                        best_checkpoint = ckpt
                    break
        except:
            continue
    
    if best_checkpoint is None:
        print("❌ Could not find valid checkpoint")
        return None
    
    print(f"📁 Loading best model: {best_checkpoint}")
    print(f"🎯 Best validation loss: {best_val_loss:.2f}")
    
    try:
        # Load the model
        model = PlayerPointsModel.load_from_checkpoint(str(best_checkpoint))
        model.eval()
        print("✅ Model loaded successfully!")
        return model
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None


def evaluate_trained_model():
    """Evaluate the trained model performance"""
    
    print("🔍 Evaluating Trained WNBA Player Points Model")
    print("=" * 60)
    
    # Load the trained model
    model = load_best_model()
    if model is None:
        return
    
    # Load data
    data_path = "../consolidated_wnba/01_player_data/basic_stats/multi_year_wnba_dataset.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Dataset not found: {data_path}")
        return
    
    print(f"📊 Loading data from: {data_path}")
    
    # Set up data module
    data_module = WNBADataModule(
        data_path=data_path,
        batch_size=32,
        num_workers=0,
        seq_len=10
    )
    
    # Setup data
    data_module.setup()
    
    print(f"✅ Data loaded successfully!")
    print(f"📊 Model input dimension: {model.hparams.input_dim}")
    print(f"📊 Data features: {len(data_module.feature_columns)}")
    
    # Run validation on the trained model
    print(f"\n🔍 Running Team Consistency Validation on Trained Model...")
    
    validation_results = validate_player_consistency(
        model, 
        data_module.val_dataloader(),
        device='cpu'
    )
    
    # Print validation report
    print(f"\n📊 TRAINED MODEL VALIDATION RESULTS:")
    print("=" * 60)
    print(validation_results['validation_report'])
    
    # Extract key metrics
    player_metrics = validation_results['player_metrics']
    team_metrics = validation_results['team_metrics']
    
    print(f"\n📈 Trained Model Performance Summary:")
    print(f"  Player MAE: {player_metrics['player_mae']:.2f} points")
    print(f"  Player RMSE: {player_metrics['player_rmse']:.2f} points")
    print(f"  Player Correlation: {player_metrics['player_correlation']:.3f}")
    print(f"  Team MAE: {team_metrics['team_mae_mean']:.2f} points")
    print(f"  Team Correlation: {team_metrics['team_correlation']:.3f}")
    
    # Check against benchmarks
    print(f"\n🎯 Benchmark Assessment:")
    
    # For season totals, we use relaxed benchmarks
    player_benchmark = player_metrics['player_mae'] < 50  # 50 points per season
    team_benchmark = team_metrics['team_mae_mean'] < 100  # 100 points per season
    correlation_benchmark = player_metrics['player_correlation'] > 0.7
    
    print(f"  Player MAE < 50 points: {'✅ PASS' if player_benchmark else '❌ FAIL'} ({player_metrics['player_mae']:.1f})")
    print(f"  Team MAE < 100 points: {'✅ PASS' if team_benchmark else '❌ FAIL'} ({team_metrics['team_mae_mean']:.1f})")
    print(f"  Correlation > 0.7: {'✅ PASS' if correlation_benchmark else '❌ FAIL'} ({player_metrics['player_correlation']:.3f})")
    
    benchmarks_met = player_benchmark and team_benchmark and correlation_benchmark
    
    if benchmarks_met:
        print(f"\n🏆 Trained model meets all performance benchmarks!")
    else:
        print(f"\n⚠️  Trained model needs improvement in some areas")
    
    # Model architecture summary
    print(f"\n🔧 Model Architecture Summary:")
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")
    print(f"  Model size: ~{total_params * 4 / 1024 / 1024:.1f} MB")
    print(f"  Input features: {model.hparams.input_dim}")
    print(f"  Architecture: {model.hparams.input_dim} → 512 → 256 → 128 → 64 → 1")
    print(f"  Loss function: Huber Loss (delta=1.5)")
    print(f"  Optimizer: AdamW")
    
    return validation_results


if __name__ == '__main__':
    try:
        results = evaluate_trained_model()
        print(f"\n🎉 Trained model evaluation completed!")
        
    except Exception as e:
        print(f"\n❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
