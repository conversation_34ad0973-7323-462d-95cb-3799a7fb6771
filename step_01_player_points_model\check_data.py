"""
🔍 Data Structure Checker for WNBA Player Points Model

This script checks the structure and quality of the WNBA data
without requiring PyTorch Lightning dependencies.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

# Add utils to path
sys.path.append(str(Path(__file__).parent))

try:
    from utils.feature_engineering import engineer_all_features
    FEATURE_ENGINEERING_AVAILABLE = True
except ImportError:
    print("⚠️  Feature engineering module not available (missing dependencies)")
    FEATURE_ENGINEERING_AVAILABLE = False


def check_data_structure():
    """Check the structure and quality of the WNBA data"""
    
    print("🔍 WNBA Data Structure Check")
    print("=" * 50)
    
    # Path to the consolidated WNBA data
    base_path = Path(__file__).parent.parent
    data_path = base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features_with_metadata.csv"
    
    if not data_path.exists():
        print(f"❌ Data file not found: {data_path}")
        print("\nTrying alternative paths...")
        
        # Try other possible paths
        alt_paths = [
            base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features.csv",
            base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "wnba_players_2024.csv",
            base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "wnba_players_2023.csv"
        ]
        
        for alt_path in alt_paths:
            if alt_path.exists():
                print(f"✅ Found alternative data file: {alt_path}")
                data_path = alt_path
                break
        else:
            print("❌ No suitable data file found!")
            return
    
    print(f"📁 Using data file: {data_path}")
    
    try:
        # Load the data
        print(f"\n📊 Loading data...")
        df = pd.read_csv(data_path)
        
        print(f"✅ Data loaded successfully!")
        print(f"📊 Data Shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
        
        # Basic info
        print(f"\n📋 Column Information:")
        print(f"  Total columns: {len(df.columns)}")
        print(f"  Numeric columns: {len(df.select_dtypes(include=[np.number]).columns)}")
        print(f"  Text columns: {len(df.select_dtypes(include=['object']).columns)}")
        
        # Show all columns
        print(f"\n📝 All Columns ({len(df.columns)}):")
        for i, col in enumerate(df.columns, 1):
            dtype = str(df[col].dtype)
            null_count = df[col].isnull().sum()
            null_pct = (null_count / len(df)) * 100
            print(f"  {i:2d}. {col:<30} ({dtype:<10}) - {null_count:,} nulls ({null_pct:.1f}%)")
        
        # Target variable analysis
        print(f"\n🎯 Target Variable Analysis:")
        if 'points' in df.columns:
            points = df['points']
            print(f"  Column: 'points' ✅")
            print(f"  Mean: {points.mean():.2f}")
            print(f"  Median: {points.median():.2f}")
            print(f"  Std: {points.std():.2f}")
            print(f"  Min: {points.min():.2f}")
            print(f"  Max: {points.max():.2f}")
            print(f"  Missing: {points.isnull().sum():,} ({(points.isnull().sum()/len(df)*100):.1f}%)")
            
            # Distribution analysis
            print(f"\n📈 Points Distribution:")
            print(f"  0-5 points: {((points >= 0) & (points <= 5)).sum():,} players ({((points >= 0) & (points <= 5)).sum()/len(df)*100:.1f}%)")
            print(f"  6-10 points: {((points > 5) & (points <= 10)).sum():,} players ({((points > 5) & (points <= 10)).sum()/len(df)*100:.1f}%)")
            print(f"  11-15 points: {((points > 10) & (points <= 15)).sum():,} players ({((points > 10) & (points <= 15)).sum()/len(df)*100:.1f}%)")
            print(f"  16-20 points: {((points > 15) & (points <= 20)).sum():,} players ({((points > 15) & (points <= 20)).sum()/len(df)*100:.1f}%)")
            print(f"  21+ points: {(points > 20).sum():,} players ({(points > 20).sum()/len(df)*100:.1f}%)")
            
        else:
            print(f"  ❌ 'points' column not found!")
            print(f"  Available columns that might be targets:")
            possible_targets = [col for col in df.columns if any(word in col.lower() for word in ['point', 'score', 'pts'])]
            for col in possible_targets:
                print(f"    - {col}")
        
        # Key feature analysis
        print(f"\n🔧 Key Features Analysis:")
        key_features = ['minutes_per_game', 'field_goal_percentage', 'free_throw_percentage', 'rebounds', 'assists', 'age']
        
        for feature in key_features:
            if feature in df.columns:
                values = df[feature]
                print(f"  ✅ {feature}: mean={values.mean():.2f}, std={values.std():.2f}, nulls={values.isnull().sum()}")
            else:
                print(f"  ❌ {feature}: not found")
        
        # Player and team info
        print(f"\n👥 Player & Team Information:")
        if 'player_name' in df.columns:
            unique_players = df['player_name'].nunique()
            print(f"  Unique players: {unique_players:,}")
        
        if 'team_abbreviation' in df.columns:
            unique_teams = df['team_abbreviation'].nunique()
            teams = df['team_abbreviation'].value_counts()
            print(f"  Unique teams: {unique_teams}")
            print(f"  Team distribution:")
            for team, count in teams.head(10).items():
                print(f"    {team}: {count:,} records")
        
        if 'season' in df.columns:
            seasons = df['season'].value_counts().sort_index()
            print(f"  Seasons available: {list(seasons.index)}")
            print(f"  Records per season:")
            for season, count in seasons.items():
                print(f"    {season}: {count:,} records")
        
        # Sample data
        print(f"\n📋 Sample Data (first 3 rows):")
        print(df.head(3).to_string())
        
        # Data quality assessment
        print(f"\n🔍 Data Quality Assessment:")
        
        # Missing data
        total_missing = df.isnull().sum().sum()
        total_cells = df.shape[0] * df.shape[1]
        missing_pct = (total_missing / total_cells) * 100
        print(f"  Total missing values: {total_missing:,} ({missing_pct:.2f}% of all data)")
        
        # Duplicate rows
        duplicates = df.duplicated().sum()
        print(f"  Duplicate rows: {duplicates:,}")
        
        # Numeric data ranges
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        print(f"  Numeric columns with negative values:")
        for col in numeric_cols:
            negative_count = (df[col] < 0).sum()
            if negative_count > 0:
                print(f"    {col}: {negative_count:,} negative values")
        
        # Feature engineering test
        if FEATURE_ENGINEERING_AVAILABLE:
            print(f"\n🔧 Testing Feature Engineering:")
            try:
                df_engineered = engineer_all_features(df.copy())
                new_features = len(df_engineered.columns) - len(df.columns)
                print(f"  ✅ Feature engineering successful!")
                print(f"  📈 Added {new_features} new features")
                print(f"  📊 Total features after engineering: {len(df_engineered.columns)}")
                
                # Show new features
                new_feature_names = [col for col in df_engineered.columns if col not in df.columns]
                if new_feature_names:
                    print(f"  🆕 New features added:")
                    for i, feat in enumerate(new_feature_names[:10], 1):  # Show first 10
                        print(f"    {i:2d}. {feat}")
                    if len(new_feature_names) > 10:
                        print(f"    ... and {len(new_feature_names) - 10} more")
                
            except Exception as e:
                print(f"  ❌ Feature engineering failed: {e}")
        
        # Model readiness assessment
        print(f"\n🎯 Model Readiness Assessment:")
        
        readiness_score = 0
        max_score = 6
        
        # Check 1: Target variable exists
        if 'points' in df.columns:
            print(f"  ✅ Target variable ('points') available")
            readiness_score += 1
        else:
            print(f"  ❌ Target variable ('points') missing")
        
        # Check 2: Sufficient data
        if len(df) >= 500:
            print(f"  ✅ Sufficient data ({len(df):,} records)")
            readiness_score += 1
        else:
            print(f"  ⚠️  Limited data ({len(df):,} records, recommend 500+)")
        
        # Check 3: Key features available
        key_features_available = sum(1 for feat in key_features if feat in df.columns)
        if key_features_available >= 4:
            print(f"  ✅ Key features available ({key_features_available}/{len(key_features)})")
            readiness_score += 1
        else:
            print(f"  ⚠️  Limited key features ({key_features_available}/{len(key_features)})")
        
        # Check 4: Low missing data
        if missing_pct < 10:
            print(f"  ✅ Low missing data ({missing_pct:.1f}%)")
            readiness_score += 1
        else:
            print(f"  ⚠️  High missing data ({missing_pct:.1f}%)")
        
        # Check 5: Multiple seasons/time periods
        if 'season' in df.columns and df['season'].nunique() >= 2:
            print(f"  ✅ Multiple seasons available ({df['season'].nunique()})")
            readiness_score += 1
        else:
            print(f"  ⚠️  Limited temporal data")
        
        # Check 6: Player diversity
        if 'player_name' in df.columns and df['player_name'].nunique() >= 50:
            print(f"  ✅ Good player diversity ({df['player_name'].nunique()} players)")
            readiness_score += 1
        else:
            print(f"  ⚠️  Limited player diversity")
        
        # Final assessment
        print(f"\n📊 Overall Readiness Score: {readiness_score}/{max_score}")
        
        if readiness_score >= 5:
            print(f"🎉 Data is ready for model training!")
        elif readiness_score >= 3:
            print(f"⚠️  Data needs some preparation before training")
        else:
            print(f"❌ Data requires significant preparation")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if readiness_score < max_score:
            if 'points' not in df.columns:
                print(f"  • Add or identify the target variable (points)")
            if len(df) < 500:
                print(f"  • Collect more data (current: {len(df)}, target: 500+)")
            if missing_pct > 10:
                print(f"  • Address missing data (current: {missing_pct:.1f}%)")
            if key_features_available < 4:
                print(f"  • Add more basketball features")
        
        print(f"  • Consider feature engineering to improve model performance")
        print(f"  • Implement time-based train/validation splits")
        print(f"  • Set up data validation pipeline")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    check_data_structure()
