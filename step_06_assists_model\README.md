# Assists Model (Step 6 of 9) - Playmaking-Focused Basketball Analytics

## Overview

The Assists Model is a sophisticated neural network designed to predict player assists in basketball games, with a focus on playmaking analysis and teammate interactions. This model is part of a comprehensive basketball analytics pipeline and represents the sixth step in the nine-step modeling process.

## Key Features

### 🎯 Playmaking-Focused Architecture
- **Teammate Interaction Module**: Models synergy between players and their teammates
- **Usage Rate Modeling**: Incorporates usage patterns and assist opportunities
- **Contextual Predictions**: Considers game context, pace, and team dynamics

### 📊 Advanced Feature Engineering
- **Ball Handling Metrics**: Time of possession, dribbles, passes per possession
- **Assist Types**: Primary, secondary, and potential assists
- **Court Area Analysis**: Frontcourt, paint, post, and elbow touches
- **Playmaking Efficiency**: Assist-to-usage ratios and playmaking rates

### 🔍 Comprehensive Validation
- **Usage Consistency**: Validates assist predictions against usage patterns
- **Playmaking Efficiency**: Evaluates assist-to-usage ratios
- **Teammate Synergy**: Analyzes teammate interaction effects

## Model Architecture

```
AssistsModel
├── Feature Processing
│   ├── Core Features (usage, assists rate, minutes)
│   ├── Ball Handling (possession time, dribbles, passes)
│   ├── Assist Types (primary, secondary, potential)
│   └── Context (pace, opponent, teammate features)
├── Neural Network
│   ├── Input Layer (64 features)
│   ├── Hidden Layers (128 units with dropout)
│   ├── Teammate Interaction Module
│   └── Output Layer (assists prediction)
└── Loss Function: Huber Loss (robust to outliers)
```

## Installation

```bash
cd step_06_assists_model
pip install -r requirements.txt
```

## Quick Start

### Demo Training
```bash
python demo_training.py
```

### Custom Training
```python
from step_06_assists_model import AssistsModel, AssistsDataModule
from step_06_assists_model.training import train_assists_model, AssistsTrainingConfig

# Configure training
config = AssistsTrainingConfig(
    input_dim=64,
    hidden_dim=128,
    learning_rate=0.001,
    batch_size=32,
    max_epochs=100,
    data_path="path/to/your/data.csv"
)

# Train model
results = train_assists_model(config)
```

### Model Inference
```python
from step_06_assists_model import AssistsModel
import torch

# Load trained model
model = AssistsModel.load_from_checkpoint("path/to/checkpoint.ckpt")

# Make predictions
with torch.no_grad():
    predictions = model(input_features)
```

## Data Format

The model expects data in CSV format with the following key columns:

### Core Features
- `usage_rate`: Player usage rate (0.0-1.0)
- `assist_rate_ema10`: Exponential moving average of assist rate
- `minutes_per_game`: Minutes played per game

### Ball Handling Features
- `time_of_possession`: Time with ball per possession
- `dribbles_per_possession`: Dribbles per possession
- `passes_per_possession`: Passes per possession
- `touches_per_possession`: Touches per possession

### Assist Features
- `potential_assists`: Potential assists (passes leading to shot attempts)
- `secondary_assists`: Secondary assists (pass before the assist)
- `hockey_assists`: Hockey-style assists (two passes before score)

### Context Features
- `teammate_fg_pct`: Teammate field goal percentage
- `teammate_3p_pct`: Teammate three-point percentage
- `opponent_pace`: Opponent team pace
- `opponent_def_rating`: Opponent defensive rating

### Target
- `assists`: Actual assists (target variable)

## Model Performance

### Key Metrics
- **RMSE**: Root Mean Square Error for prediction accuracy
- **MAE**: Mean Absolute Error for average prediction error
- **R²**: Coefficient of determination for explained variance
- **Assists Accuracy**: Predictions within ±1 assist tolerance
- **Playmaking Efficiency**: High-assist game prediction accuracy

### Validation Metrics
- **Usage Consistency**: Assist-to-usage ratio validation
- **Range Compliance**: Predictions within realistic ranges (0-15 assists)
- **Distribution Similarity**: Predicted vs. actual assist distributions

## Training Process

### 1. Data Preparation
```python
data_module = AssistsDataModule(
    data_path="assists_data.csv",
    batch_size=32,
    train_split=0.7,
    val_split=0.15,
    test_split=0.15
)
```

### 2. Model Configuration
```python
model = AssistsModel(
    input_dim=64,
    hidden_dim=128,
    teammate_dim=16,
    dropout_rate=0.2,
    learning_rate=0.001
)
```

### 3. Training
```python
trainer = pl.Trainer(
    max_epochs=100,
    callbacks=[ModelCheckpoint(), EarlyStopping()],
    logger=TensorBoardLogger()
)
trainer.fit(model, data_module)
```

## Evaluation

### Core Metrics
```python
from step_06_assists_model.utils import evaluate_assists_model

# Evaluate model
results = evaluate_assists_model(y_true, y_pred, features)
print(f"RMSE: {results['core_metrics']['rmse']:.4f}")
print(f"Assists Accuracy: {results['core_metrics']['assists_accuracy']:.4f}")
```

### Validation
```python
from step_06_assists_model.validation import validate_assists_model

# Validate predictions
validation = validate_assists_model(predictions, features)
print(f"Overall Score: {validation['core_validation']['overall_score']:.4f}")
```

## Key Components

### Models
- `AssistsModel`: Main neural network for assists prediction
- `TeammateInteractionModule`: Teammate synergy modeling
- `UsageAttentionModule`: Usage-based attention mechanism

### Data Processing
- `AssistsDataModule`: PyTorch Lightning data module
- `PlaymakingFeatureEngineering`: Feature engineering for playmaking
- `TeammateProcessor`: Teammate interaction processing

### Utilities
- `AssistsMetrics`: Specialized metrics for assists evaluation
- `PlaymakingEvaluator`: Playmaking efficiency evaluation
- `UsageRatioValidator`: Usage consistency validation

### Training
- `AssistsTrainer`: Main training orchestrator
- `AssistsTrainingConfig`: Training configuration management

## Integration with Pipeline

This model integrates with the broader basketball analytics pipeline:

1. **Data Source**: Uses unified data from `consolidated_wnba/`
2. **Feature Engineering**: Builds on player statistics and game context
3. **Model Integration**: Can be combined with other prediction models
4. **Validation**: Consistent with pipeline validation standards

## Advanced Features

### Teammate Interaction Modeling
- Analyzes how teammate abilities affect assist opportunities
- Models synergy between players
- Considers teammate shooting percentages and playing styles

### Usage Rate Analysis
- Validates assist predictions against usage patterns
- Ensures realistic assist-to-usage ratios
- Identifies playmaking efficiency patterns

### Contextual Predictions
- Incorporates game pace and opponent strength
- Considers court area touches and possession patterns
- Adapts to different playing styles and situations

## Troubleshooting

### Common Issues

1. **Data Format Errors**
   - Ensure CSV has all required columns
   - Check for missing values in key features
   - Verify data types (numeric for all features)

2. **Training Issues**
   - Reduce learning rate if loss is unstable
   - Increase batch size for more stable gradients
   - Check for data leakage in train/val/test splits

3. **Validation Failures**
   - Review feature engineering for unrealistic values
   - Check assist-to-usage ratio consistency
   - Verify prediction ranges (0-15 assists)

### Performance Optimization
- Use GPU training for larger datasets
- Implement mixed precision training
- Consider hyperparameter tuning with Optuna

## Contributing

1. Follow the existing code structure
2. Add comprehensive tests for new features
3. Update documentation for any changes
4. Ensure consistency with pipeline standards

## License

This project is part of the HMNV Basketball Analytics Pipeline.

## Contact

For questions or support, please contact the HMNV Analytics Team.
