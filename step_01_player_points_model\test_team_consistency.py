"""
🧪 Team Consistency Validation Test

This script tests the team-consistency validation framework using a simple
baseline model to ensure the validation logic works correctly before full training.
"""

import sys
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from pathlib import Path
from collections import defaultdict

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from data_processing.wnba_data_module import WNBADataModule
from validation.consistency_checks import validate_player_consistency, plot_validation_results, check_basketball_logic
from utils.feature_engineering import engineer_all_features


class SimpleBaselineModel(nn.Module):
    """
    Simple baseline model for testing validation framework
    Uses basic linear regression with some basketball logic
    """
    
    def __init__(self, input_dim):
        super().__init__()
        self.linear = nn.Linear(input_dim, 1)
        
        # Initialize with reasonable weights for basketball
        with torch.no_grad():
            # Give higher weight to minutes and shooting percentage
            self.linear.weight.fill_(0.1)
            self.linear.bias.fill_(5.0)  # Baseline 5 points
    
    def forward(self, x):
        return self.linear(x).squeeze()
    
    def eval(self):
        """Set to evaluation mode"""
        return super().eval()
    
    def to(self, device):
        """Move to device"""
        return super().to(device)


def create_synthetic_game_data(df, players_per_team=12):
    """
    Create synthetic game data from season averages for testing
    
    Args:
        df: DataFrame with player season data
        players_per_team: Number of players per team per game
    
    Returns:
        DataFrame with game-level data
    """
    print("🎮 Creating synthetic game data for validation...")
    
    # Get unique teams
    teams = df['team_abbreviation'].unique()
    
    game_data = []
    game_id = 0
    
    # Create 10 synthetic games
    for game_num in range(10):
        # Pick two random teams
        team_a, team_b = np.random.choice(teams, 2, replace=False)
        
        # Get players from each team
        team_a_players = df[df['team_abbreviation'] == team_a].sample(
            min(players_per_team, len(df[df['team_abbreviation'] == team_a]))
        )
        team_b_players = df[df['team_abbreviation'] == team_b].sample(
            min(players_per_team, len(df[df['team_abbreviation'] == team_b]))
        )
        
        # Add game context to players
        for _, player in team_a_players.iterrows():
            player_data = player.copy()
            player_data['game_id'] = game_id
            player_data['team_id'] = 0  # Team A
            # Scale points to per-game (assuming ~30 games per season)
            player_data['points'] = player_data['points'] / 30.0
            game_data.append(player_data)
        
        for _, player in team_b_players.iterrows():
            player_data = player.copy()
            player_data['game_id'] = game_id
            player_data['team_id'] = 1  # Team B
            # Scale points to per-game
            player_data['points'] = player_data['points'] / 30.0
            game_data.append(player_data)
        
        game_id += 1
    
    game_df = pd.DataFrame(game_data)
    print(f"✅ Created {len(game_df)} player-game records across {game_id} games")
    
    return game_df


def test_team_consistency_validation():
    """Test the team consistency validation framework"""
    
    print("🧪 Testing Team Consistency Validation Framework")
    print("=" * 60)
    
    # Load and prepare data
    base_path = Path(__file__).parent.parent
    data_path = base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features_with_metadata.csv"
    
    if not data_path.exists():
        print(f"❌ Data file not found: {data_path}")
        return
    
    print(f"📊 Loading data from: {data_path}")
    df = pd.read_csv(data_path)
    
    # Apply feature engineering
    print("🔧 Applying feature engineering...")
    df_engineered = engineer_all_features(df)
    
    # Create synthetic game data
    game_df = create_synthetic_game_data(df_engineered)
    
    # Set up data module
    print("\n📦 Setting up data module...")
    
    # Save temporary game data
    temp_data_path = "temp_game_data.csv"
    game_df.to_csv(temp_data_path, index=False)
    
    try:
        data_module = WNBADataModule(
            data_path=temp_data_path,
            batch_size=32,
            num_workers=0,  # Avoid multiprocessing issues
            seq_len=10
        )
        
        # Setup data
        data_module.setup()
        
        # Get validation dataloader
        val_dataloader = data_module.val_dataloader()
        
        print(f"✅ Data module ready with {len(data_module.feature_columns)} features")
        
        # Create simple baseline model
        print("\n🤖 Creating baseline model...")
        input_dim = len(data_module.feature_columns)
        model = SimpleBaselineModel(input_dim)
        
        print(f"✅ Baseline model created with {input_dim} input features")
        
        # Run validation
        print("\n🔍 Running team consistency validation...")
        
        validation_results = validate_player_consistency(
            model, 
            val_dataloader,
            device='cpu'
        )
        
        # Print validation report
        print("\n📊 VALIDATION RESULTS:")
        print("=" * 40)
        print(validation_results['validation_report'])
        
        # Extract key metrics
        player_metrics = validation_results['player_metrics']
        team_metrics = validation_results['team_metrics']
        
        print(f"\n📈 Key Metrics Summary:")
        print(f"  Player MAE: {player_metrics['player_mae']:.2f}")
        print(f"  Player Correlation: {player_metrics['player_correlation']:.3f}")
        print(f"  Team MAE: {team_metrics['team_mae_mean']:.2f}")
        print(f"  Team Correlation: {team_metrics['team_correlation']:.3f}")
        
        # Basketball logic checks
        print(f"\n🏀 Basketball Logic Checks:")
        logic_results = check_basketball_logic(validation_results)
        
        for check_name, result in logic_results.items():
            status = "✅ PASS" if result['pass'] else "❌ FAIL"
            print(f"  {check_name}: {status} (count: {result['count']})")
        
        # Team total analysis
        print(f"\n🏆 Team Total Analysis:")
        team_totals_actual = list(team_metrics['team_totals_actual'].values())
        team_totals_pred = list(team_metrics['team_totals_pred'].values())
        
        print(f"  Actual team totals: {np.mean(team_totals_actual):.1f} ± {np.std(team_totals_actual):.1f}")
        print(f"  Predicted team totals: {np.mean(team_totals_pred):.1f} ± {np.std(team_totals_pred):.1f}")
        print(f"  Range actual: {np.min(team_totals_actual):.1f} - {np.max(team_totals_actual):.1f}")
        print(f"  Range predicted: {np.min(team_totals_pred):.1f} - {np.max(team_totals_pred):.1f}")
        
        # Validation framework assessment
        print(f"\n🎯 Validation Framework Assessment:")
        
        framework_checks = {
            "Player predictions generated": len(validation_results['player_data']) > 0,
            "Team totals calculated": len(team_metrics['team_totals_actual']) > 0,
            "Metrics computed": player_metrics['player_mae'] is not None,
            "Basketball logic validated": len(logic_results) > 0,
            "Report generated": len(validation_results['validation_report']) > 0
        }
        
        all_passed = True
        for check, passed in framework_checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check}")
            if not passed:
                all_passed = False
        
        # Overall assessment
        print(f"\n🏁 Overall Assessment:")
        if all_passed:
            print("✅ Team consistency validation framework is working correctly!")
            print("🚀 Ready for full model training and validation")
        else:
            print("❌ Issues detected in validation framework")
            print("🔧 Framework needs debugging before full training")
        
        # Expected vs actual behavior
        print(f"\n💡 Expected Behavior Analysis:")
        print(f"  • Baseline model should have moderate accuracy")
        print(f"  • Team totals should be in reasonable range (30-120 points)")
        print(f"  • Player predictions should sum close to team totals")
        print(f"  • No negative predictions or extreme outliers")
        
        reasonable_team_range = all(30 <= total <= 120 for total in team_totals_pred)
        reasonable_player_range = all(0 <= pred <= 50 for pred in [d['pred_points'] for d in validation_results['player_data']])
        
        print(f"\n✅ Reasonableness Checks:")
        print(f"  Team totals in range (30-120): {'✅' if reasonable_team_range else '❌'}")
        print(f"  Player predictions in range (0-50): {'✅' if reasonable_player_range else '❌'}")
        
        # Save validation plots
        try:
            plot_path = "team_consistency_validation_plots.png"
            plot_validation_results(validation_results, save_path=plot_path)
            print(f"\n📊 Validation plots saved to: {plot_path}")
        except Exception as e:
            print(f"⚠️  Could not save plots: {e}")
        
        return validation_results
        
    finally:
        # Clean up temporary file
        import os
        if os.path.exists(temp_data_path):
            os.remove(temp_data_path)


if __name__ == '__main__':
    try:
        results = test_team_consistency_validation()
        print(f"\n🎉 Team consistency validation test completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
