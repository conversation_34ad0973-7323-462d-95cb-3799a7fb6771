"""
🔍 Debug NaN Issue in Step 2 Game Totals Model

This script helps identify what's causing NaN values in the model predictions and losses.
"""

import pandas as pd
import numpy as np
import torch
from data_processing.game_totals_data_module import GameTotalsDataModule

def debug_nan_issue():
    """Debug the NaN issue in Step 2 model"""
    
    print("🔍 Debugging NaN Issue in Step 2 Model")
    print("=" * 60)
    
    # Initialize data module
    data_module = GameTotalsDataModule(
        data_path=r"c:\Users\<USER>\Documents\HMNV_WNBA\unified_data\unified_game_data.csv",
        batch_size=32,
        player_model_path=r"c:\Users\<USER>\Documents\HMNV_WNBA\step_01_player_points_model\checkpoints\standard-player-points-epoch=21-val_loss=0.34.ckpt"
    )
    
    # Setup data
    data_module.setup()
    
    # Check training data
    train_loader = data_module.train_dataloader()
    val_loader = data_module.val_dataloader()
    test_loader = data_module.test_dataloader()
    
    print(f"📊 Dataset sizes:")
    print(f"  Training batches: {len(train_loader)}")
    print(f"  Validation batches: {len(val_loader)}")
    print(f"  Test batches: {len(test_loader)}")
    
    # Check for NaN values in training data
    print("\n🔍 Checking for NaN values in training data...")
    train_batch = next(iter(train_loader))
    features, targets = train_batch
    
    print(f"Features shape: {features.shape}")
    print(f"Targets shape: {targets.shape}")
    
    # Check for NaN in features
    nan_features = torch.isnan(features).sum().item()
    print(f"NaN values in features: {nan_features}")
    
    # Check for NaN in targets
    nan_targets = torch.isnan(targets).sum().item()
    print(f"NaN values in targets: {nan_targets}")
    
    # Check for infinite values
    inf_features = torch.isinf(features).sum().item()
    inf_targets = torch.isinf(targets).sum().item()
    print(f"Infinite values in features: {inf_features}")
    print(f"Infinite values in targets: {inf_targets}")
    
    # Check target statistics
    print(f"\n📈 Target statistics:")
    print(f"  Min: {targets.min().item():.2f}")
    print(f"  Max: {targets.max().item():.2f}")
    print(f"  Mean: {targets.mean().item():.2f}")
    print(f"  Std: {targets.std().item():.2f}")
    
    # Check feature statistics
    print(f"\n📊 Feature statistics:")
    for i in range(min(5, features.shape[1])):  # Check first 5 features
        feat = features[:, i]
        print(f"  Feature {i}: min={feat.min().item():.2f}, max={feat.max().item():.2f}, mean={feat.mean().item():.2f}")
    
    # Check validation data
    print(f"\n🔍 Checking validation data...")
    val_batch = next(iter(val_loader))
    val_features, val_targets = val_batch
    
    val_nan_features = torch.isnan(val_features).sum().item()
    val_nan_targets = torch.isnan(val_targets).sum().item()
    print(f"NaN values in validation features: {val_nan_features}")
    print(f"NaN values in validation targets: {val_nan_targets}")
    
    # Check test data
    print(f"\n🔍 Checking test data...")
    test_batch = next(iter(test_loader))
    test_features, test_targets = test_batch
    
    test_nan_features = torch.isnan(test_features).sum().item()
    test_nan_targets = torch.isnan(test_targets).sum().item()
    print(f"NaN values in test features: {test_nan_features}")
    print(f"NaN values in test targets: {test_nan_targets}")
    
    # Check if there are any problematic values
    print(f"\n🚨 Summary:")
    total_nan = nan_features + nan_targets + val_nan_features + val_nan_targets + test_nan_features + test_nan_targets
    total_inf = inf_features + inf_targets
    
    if total_nan > 0:
        print(f"❌ Found {total_nan} NaN values in the data!")
    else:
        print(f"✅ No NaN values found in the data")
    
    if total_inf > 0:
        print(f"❌ Found {total_inf} infinite values in the data!")
    else:
        print(f"✅ No infinite values found in the data")
    
    # Check specific feature columns that might cause issues
    print(f"\n🔍 Checking hierarchical features...")
    if hasattr(data_module, 'feature_columns'):
        hierarchical_features = [col for col in data_module.feature_columns if col.startswith('player_pred')]
        print(f"Hierarchical features: {hierarchical_features}")
        
        # Check if any hierarchical features have issues
        for i, col in enumerate(data_module.feature_columns):
            if col in hierarchical_features:
                feat_values = features[:, i]
                if torch.isnan(feat_values).sum() > 0:
                    print(f"❌ NaN found in feature '{col}'")
                if torch.isinf(feat_values).sum() > 0:
                    print(f"❌ Infinite values found in feature '{col}'")

if __name__ == "__main__":
    debug_nan_issue()
