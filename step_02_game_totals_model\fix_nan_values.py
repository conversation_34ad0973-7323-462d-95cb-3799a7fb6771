"""
🔧 Fix NaN Values in Step 2 Game Totals Model

This script identifies and fixes NaN values in the hierarchical features
that are causing the model to produce NaN metrics.
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import torch

# Add the data processing module to path
sys.path.append(str(Path(__file__).parent / "data_processing"))

from game_totals_data_module import GameTotalsDataModule

def fix_nan_values():
    """Fix NaN values in the data module"""
    
    print("🔧 Fixing NaN Values in Step 2 Game Totals Model")
    print("=" * 60)
    
    # Initialize data module
    unified_data_path = Path(__file__).parent.parent / "unified_data" / "unified_game_data.csv"
    
    print(f"📊 Loading data from: {unified_data_path}")
    
    dm = GameTotalsDataModule(
        data_path=str(unified_data_path),
        player_model_path="../step_01_player_points_model/checkpoints/standard-player-points-epoch=21-val_loss=0.34.ckpt"
    )
    
    # Setup data
    dm.prepare_data()
    dm.setup()
    
    print(f"🎯 Total games loaded: {len(dm.data)}")
    
    # Check for NaN values in each split
    train_data, val_data, test_data = dm._create_time_splits()
    
    print("\n🔍 Checking raw data for NaN values...")
    print(f"Train data NaN values: {train_data.isna().sum().sum()}")
    print(f"Val data NaN values: {val_data.isna().sum().sum()}")
    print(f"Test data NaN values: {test_data.isna().sum().sum()}")
    
    # Apply feature engineering
    print("\n🔧 Applying feature engineering...")
    train_data = dm._add_hierarchical_features(train_data)
    val_data = dm._add_hierarchical_features(val_data)
    test_data = dm._add_hierarchical_features(test_data)
    
    print("\n🔍 Checking after hierarchical features...")
    print(f"Train data NaN values: {train_data.isna().sum().sum()}")
    print(f"Val data NaN values: {val_data.isna().sum().sum()}")
    print(f"Test data NaN values: {test_data.isna().sum().sum()}")
    
    # Check specific hierarchical features
    hierarchical_features = [
        'player_pred_total', 'player_pred_mean', 'player_pred_std',
        'player_pred_max', 'player_pred_min', 'player_pred_count',
        'player_scoring_concentration', 'player_scoring_balance',
        'player_depth_score', 'player_top3_avg', 'player_bench_contribution'
    ]
    
    print("\n🔍 Checking hierarchical features specifically...")
    for feature in hierarchical_features:
        if feature in train_data.columns:
            train_nans = train_data[feature].isna().sum()
            val_nans = val_data[feature].isna().sum()
            test_nans = test_data[feature].isna().sum()
            
            if train_nans > 0 or val_nans > 0 or test_nans > 0:
                print(f"  {feature}: train={train_nans}, val={val_nans}, test={test_nans}")
    
    # Apply preprocessing
    print("\n🔧 Applying preprocessing...")
    dm._fit_scalers(train_data)
    
    try:
        train_data = dm._apply_preprocessing(train_data)
        print("✅ Train preprocessing successful")
    except Exception as e:
        print(f"❌ Train preprocessing failed: {e}")
    
    try:
        val_data = dm._apply_preprocessing(val_data)
        print("✅ Val preprocessing successful")
    except Exception as e:
        print(f"❌ Val preprocessing failed: {e}")
    
    try:
        test_data = dm._apply_preprocessing(test_data)
        print("✅ Test preprocessing successful")
    except Exception as e:
        print(f"❌ Test preprocessing failed: {e}")
    
    print("\n🔍 Final NaN check...")
    print(f"Train data NaN values: {train_data.isna().sum().sum()}")
    print(f"Val data NaN values: {val_data.isna().sum().sum()}")
    print(f"Test data NaN values: {test_data.isna().sum().sum()}")
    
    # Show columns with NaN values
    for data_name, data in [("Train", train_data), ("Val", val_data), ("Test", test_data)]:
        nan_cols = data.columns[data.isna().any()].tolist()
        if nan_cols:
            print(f"\n{data_name} columns with NaN: {nan_cols}")
            for col in nan_cols:
                print(f"  {col}: {data[col].isna().sum()} NaN values")

def suggest_fixes():
    """Suggest specific fixes for the NaN issues"""
    
    print("\n🔧 Suggested Fixes:")
    print("=" * 40)
    
    print("1. Add NaN handling in hierarchical feature generation:")
    print("   - Replace NaN predictions with median/mean values")
    print("   - Use fallback values for missing player predictions")
    print("   - Add validation to ensure all features are finite")
    
    print("\n2. Improve player prediction robustness:")
    print("   - Handle cases where player data is missing")
    print("   - Add default values for edge cases")
    print("   - Validate feature arrays before scaling")
    
    print("\n3. Add data validation pipeline:")
    print("   - Check for NaN/infinite values at each step")
    print("   - Log specific rows/columns with issues")
    print("   - Implement automatic data cleaning")

if __name__ == "__main__":
    fix_nan_values()
    suggest_fixes()
