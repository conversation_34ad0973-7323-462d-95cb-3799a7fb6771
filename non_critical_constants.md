📊 HMNV WNBA PIPELINE - NON-CRITICAL HARD-CODED CONSTANTS
================================================================

These are basketball-specific constants that are hard-coded throughout the pipeline. While not production-critical (they don't create mock data), they could be moved to configuration files for better maintainability.

## 🏀 BASKETBALL CONSTANTS

### 1. POSITION WEIGHTS & DISTRIBUTIONS
**Purpose**: Determine player position distributions and expected performance
**Pattern**: `[0.15, 0.15, 0.15, 0.15, 0.2, 0.2]` or `[0.15, 0.15, 0.2, 0.25, 0.25]`

**Locations**:
- `step_01_player_points_model/data_processing/wnba_data_module.py`: Lines 177, 196
- `step_02_game_totals_model/training/train_game_totals.py`: Line 253
- `step_03_moneyline_model/data_processing/moneyline_data_module.py`: Line 223
- `step_04_rebounds_model/demo_training.py`: Line 65
- `step_04_rebounds_model/data_processing/rebound_data_module.py`: Line 834 (commented out)

**Meaning**:
- PG (Point Guard): 15% of players
- SG (Shooting Guard): 15% of players  
- SF (Small Forward): 15-20% of players
- PF (Power Forward): 20-25% of players
- C (Center): 20-25% of players

### 2. REBOUND SPLIT RATIOS
**Purpose**: Split total rebounds into offensive and defensive rebounds
**Pattern**: `0.75` (defensive) and `0.25` (offensive)

**Locations**:
- `step_04_rebounds_model/data_processing/rebound_data_module.py`: 
  - Line 486: `estimated_dreb = features['rebounds'] * 0.75`
  - Line 504: `features['dreb_rate'] = features.get('rebounds_per_minute', 0) * 0.75`
  - Line 511: `estimated_oreb = features['rebounds'] * 0.25`
  - Line 529: `features['oreb_rate'] = features.get('rebounds_per_minute', 0) * 0.25`

**Meaning**: ~75% of rebounds are defensive, ~25% are offensive (league average)

### 3. SHOOTING PERCENTAGES
**Purpose**: Default/fallback shooting percentages when data is missing
**Common Values**:
- `0.35`: Three-point shooting percentage (~35%)
- `0.45`: Field goal percentage (~45%)
- `0.75`: Free throw percentage (~75%)

**Locations**:
- `step_04_rebounds_model/data_processing/rebound_data_module.py`: Lines 331, 351, 357, 402, 404
- `step_06_assists_model/data_processing/assists_data_module.py`: Lines 101, 102, 163
- `step_07_threes_model/data_processing/threes_data_module.py`: Lines 143, 160, 186, 225, 326, 367
- `unified_data/unified_player_data.csv`: Multiple entries with `0.35` default

### 4. VALIDATION THRESHOLDS
**Purpose**: Performance benchmarks for model validation
**Pattern**: AUC targets, MAE limits, accuracy minimums

**Locations**:
- `step_02_game_totals_model/models/game_totals_model.py`: 
  - Line 519: `AUC > 0.75` (overtime detection)
  - Line 604: `overtime_target': overtime_auc > 0.75`
- `pipeline_status_report.py`: Line 252: `'Overtime Detection': 'AUC > 0.75'`

### 5. TRAINING SPLIT RATIOS
**Purpose**: Train/validation/test data splits
**Pattern**: `0.15` (15% validation, 15% test)

**Locations**:
- `step_06_assists_model/training/train_assists_model.py`: Lines 63, 64, 342, 343
- `step_06_assists_model/demo_training.py`: Lines 160, 161

### 6. PERFORMANCE RATES & USAGE
**Purpose**: Player performance baselines and usage rates
**Examples**:
- `0.15`: Assist rates, turnover rates, usage minimums
- `0.25`: Dropout rates, position expectations
- `0.35`: Usage rate maximums, shooting rate estimates

**Locations**:
- `step_04_rebounds_model/data_processing/rebound_data_module.py`: Lines 91, 92, 98, 203-205, 258, 387, 493, 538
- `step_06_assists_model/data_processing/assists_data_module.py`: Lines 55, 181, 367, 389-390
- `step_03_moneyline_model/models/moneyline_model.py`: Line 37 (dropout rate)

### 7. PACE & GAME FLOW
**Purpose**: Game pace and flow metrics
**Examples**:
- High pace threshold: `0.75` quantile
- Turnover rates: `0.15` typical rate
- Shot distribution: `0.35` paint vs three-point rates

**Locations**:
- `step_04_rebounds_model/data_processing/rebound_data_module.py`: Lines 258, 387, 402, 404
- `step_05_spread_model/data_processing/spread_data_module.py`: Lines 403-406, 475-478

## 🔧 CONFIGURATION RECOMMENDATIONS

### High Priority (Most Used)
1. **Position Weights**: Move to `config/player_positions.yaml`
2. **Rebound Ratios**: Move to `config/basketball_constants.yaml`
3. **Shooting Percentages**: Move to `config/league_averages.yaml`

### Medium Priority
4. **Validation Thresholds**: Move to `config/model_benchmarks.yaml`
5. **Training Splits**: Move to `config/training_config.yaml`

### Low Priority
6. **Performance Rates**: Move to `config/performance_baselines.yaml`
7. **Pace Metrics**: Move to `config/game_flow.yaml`

## 📋 EXAMPLE CONFIGURATION FILES

### config/basketball_constants.yaml
```yaml
rebounds:
  defensive_ratio: 0.75
  offensive_ratio: 0.25

shooting:
  three_point_avg: 0.35
  field_goal_avg: 0.45
  free_throw_avg: 0.75

positions:
  weights: [0.15, 0.15, 0.2, 0.25, 0.25]  # PG, SG, SF, PF, C
  names: ['PG', 'SG', 'SF', 'PF', 'C']

usage:
  min_assist_rate: 0.15
  max_usage_rate: 0.35
  typical_turnover_rate: 0.15
```

### config/model_benchmarks.yaml
```yaml
validation_thresholds:
  overtime_detection_auc: 0.75
  player_points_mae: 2.5
  game_totals_mae: 8.0
  moneyline_auc: 0.72
  rebounds_poisson_deviance: 1.5
  
training:
  validation_split: 0.15
  test_split: 0.15
  
neural_network:
  dropout_rate: 0.25
```

## 🎯 IMPACT ASSESSMENT

### Current State
- **Maintainability**: Medium - Constants scattered across files
- **Flexibility**: Low - Changes require code modifications
- **Consistency**: Medium - Some values duplicated/inconsistent

### After Configuration
- **Maintainability**: High - Centralized configuration
- **Flexibility**: High - Easy to adjust without code changes
- **Consistency**: High - Single source of truth

## 📈 IMPLEMENTATION PRIORITY

### Phase 1: Critical Constants (Week 1)
- Position weights and distributions
- Rebound ratios (most frequently used)
- Core shooting percentages

### Phase 2: Performance Metrics (Week 2)
- Validation thresholds
- Training configuration
- Model benchmarks

### Phase 3: Fine-tuning (Week 3)
- Performance baselines
- Pace and flow metrics
- Advanced configuration options

## ✅ VALIDATION AFTER CHANGES

After moving constants to configuration:
1. Run all model training pipelines
2. Verify identical performance metrics
3. Test configuration loading/validation
4. Ensure backward compatibility
5. Update documentation

---
**Status**: Non-critical - Safe for production as-is
**Recommendation**: Implement configuration in phases for better maintainability
**Priority**: Medium - Quality of life improvement, not production blocker
