"""
📊 Create Game Totals from Step 1 Player Data

This script aggregates the same player-level data used by Step 1 
into team-level game totals for Step 2, ensuring both models 
train on the same underlying WNBA games.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import os
from datetime import datetime


def aggregate_player_data_to_team_games(player_data_path: str, output_path: str) -> None:
    """
    Aggregate player-level data to team-level game totals.
    
    This ensures Step 2 trains on the same games as Step 1, just aggregated to team level.
    
    Args:
        player_data_path: Path to the player-level data used by Step 1
        output_path: Where to save the aggregated team game data
    """
    
    print(f"📊 Loading player data from: {player_data_path}")
    
    # Load the player data
    df = pd.read_csv(player_data_path)
    print(f"Loaded {len(df)} player records")
    print(f"Columns: {list(df.columns)}")
    
    # Identify the columns we need for aggregation
    required_cols = ['game_id', 'team_id', 'date', 'points']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ Missing required columns: {missing_cols}")
        return
    
    # Check if we have additional stats to aggregate
    stat_columns = {
        'field_goals_made': ['fgm', 'fg_made', 'field_goals_made'],
        'field_goals_attempted': ['fga', 'fg_attempted', 'field_goals_attempted'], 
        'three_pointers_made': ['fg3m', 'three_made', 'three_pointers_made'],
        'three_pointers_attempted': ['fg3a', 'three_attempted', 'three_pointers_attempted'],
        'free_throws_made': ['ftm', 'ft_made', 'free_throws_made'],
        'free_throws_attempted': ['fta', 'ft_attempted', 'free_throws_attempted'],
        'rebounds': ['reb', 'rebounds', 'total_rebounds'],
        'assists': ['ast', 'assists'],
        'steals': ['stl', 'steals'],
        'blocks': ['blk', 'blocks'],
        'turnovers': ['tov', 'turnovers', 'to']
    }
    
    # Map actual column names to standard names
    column_mapping = {}
    for standard_name, possible_names in stat_columns.items():
        for possible_name in possible_names:
            if possible_name in df.columns:
                column_mapping[possible_name] = standard_name
                break
    
    print(f"Found stat columns: {column_mapping}")
    
    # Rename columns to standard names
    df_renamed = df.rename(columns=column_mapping)
    
    # Group by game and team to aggregate
    print("📈 Aggregating player stats to team level...")
    
    agg_functions = {
        'points': 'sum',
        'date': 'first'  # All players in same game should have same date
    }
    
    # Add aggregation for available stats
    for standard_name in stat_columns.keys():
        if standard_name in df_renamed.columns:
            agg_functions[standard_name] = 'sum'
    
    # Also get opponent info for each game
    team_games = []
    
    for game_id in df_renamed['game_id'].unique():
        game_data = df_renamed[df_renamed['game_id'] == game_id]
        teams_in_game = game_data['team_id'].unique()
        
        # Create team totals for each team in this game
        for team_id in teams_in_game:
            team_data = game_data[game_data['team_id'] == team_id]
            
            # Get opponent (other team in the game)
            opponent_teams = [t for t in teams_in_game if t != team_id]
            opponent_id = opponent_teams[0] if opponent_teams else team_id
            
            # Aggregate team stats
            team_totals = {
                'game_id': game_id,
                'date': team_data['date'].iloc[0],
                'team_id': team_id,
                'opponent_id': opponent_id,
                'team_total_points': team_data['points'].sum()
            }
            
            # Add other stats if available
            for standard_name in stat_columns.keys():
                if standard_name in team_data.columns:
                    team_totals[standard_name] = team_data[standard_name].sum()
            
            team_games.append(team_totals)
    
    # Create DataFrame
    team_df = pd.DataFrame(team_games)
    
    # Calculate percentage stats if we have the components
    if 'field_goals_made' in team_df.columns and 'field_goals_attempted' in team_df.columns:
        team_df['field_goal_percentage'] = team_df['field_goals_made'] / team_df['field_goals_attempted'].replace(0, 1)
    
    if 'three_pointers_made' in team_df.columns and 'three_pointers_attempted' in team_df.columns:
        team_df['three_point_percentage'] = team_df['three_pointers_made'] / team_df['three_pointers_attempted'].replace(0, 1)
    
    if 'free_throws_made' in team_df.columns and 'free_throws_attempted' in team_df.columns:
        team_df['free_throw_percentage'] = team_df['free_throws_made'] / team_df['free_throws_attempted'].replace(0, 1)
    
    # Sort by date and game
    team_df = team_df.sort_values(['date', 'game_id', 'team_id'])
    
    # Create output directory
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save to CSV
    team_df.to_csv(output_path, index=False)
    
    print(f"✅ Created team game totals: {len(team_df)} team games saved to {output_path}")
    print(f"📈 Data covers {team_df['game_id'].nunique()} unique games")
    print(f"🏀 Teams: {sorted(team_df['team_id'].unique())}")
    print(f"📅 Date range: {team_df['date'].min()} to {team_df['date'].max()}")


def main():
    """Main function to create team game data from Step 1 player data"""
    
    # Set up paths
    current_dir = Path(__file__).parent
    base_path = current_dir.parent.parent  # Go up to HMNV_WNBA directory
    
    # Use the same data source as Step 1
    player_data_path = base_path / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features_with_metadata.csv"
    output_path = current_dir.parent / "data" / "team_game_totals_from_step1.csv"
    
    if not player_data_path.exists():
        print(f"❌ Step 1 data file not found: {player_data_path}")
        print("Step 2 should use the same data source as Step 1")
        return
    
    # Process the data
    aggregate_player_data_to_team_games(str(player_data_path), str(output_path))


if __name__ == "__main__":
    main()
