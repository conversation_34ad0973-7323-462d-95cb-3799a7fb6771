"""
Demo Training for Assists Model (Step 6 of 9)
==============================================

This script demonstrates the assists model training with demo data.
It shows the complete pipeline from data preparation to model evaluation.

Usage:
    python demo_training.py

The script will:
1. Create demo assist data
2. Train the assists model
3. Evaluate model performance
4. Display training and validation results
"""

import torch
import numpy as np
import pandas as pd
from pathlib import Path
import logging
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import project modules
from models.assists_model import AssistsModel
from data_processing.assists_data_module import AssistsDataModule
from training.train_assists_model import AssistsTrainer, AssistsTrainingConfig
from utils.assists_metrics import evaluate_assists_model
from validation.assists_validation import validate_assists_model


def create_demo_data(n_samples: int = 1000) -> str:
    """Create demo assists data"""
    
    logger.info(f"Creating demo assists data with {n_samples} samples...")
    
    np.random.seed(42)
    
    # Create demo features
    data = {
        # Player identification
        'player_id': [f'player_{i}' for i in range(n_samples)],
        'game_id': [f'game_{i % 100}' for i in range(n_samples)],
        'team_id': [f'team_{i % 12}' for i in range(n_samples)],
        
        # Core playmaking features
        'usage_rate': np.random.normal(0.22, 0.05, n_samples).clip(0.10, 0.40),
        'assist_rate_ema10': np.random.normal(0.15, 0.08, n_samples).clip(0.0, 0.40),
        'minutes_per_game': np.random.normal(25.0, 8.0, n_samples).clip(5.0, 40.0),
        
        # Ball handling features
        'time_of_possession': np.random.normal(6.0, 2.0, n_samples).clip(1.0, 12.0),
        'dribbles_per_possession': np.random.normal(2.5, 1.0, n_samples).clip(0.5, 6.0),
        'passes_per_possession': np.random.normal(3.0, 1.2, n_samples).clip(1.0, 8.0),
        'touches_per_possession': np.random.normal(4.0, 1.5, n_samples).clip(1.5, 8.0),
        
        # Assist types
        'potential_assists': np.random.normal(2.0, 1.5, n_samples).clip(0.0, 8.0),
        'secondary_assists': np.random.normal(1.0, 0.8, n_samples).clip(0.0, 4.0),
        'hockey_assists': np.random.normal(0.5, 0.6, n_samples).clip(0.0, 3.0),
        
        # Passing metrics
        'pass_accuracy': np.random.normal(0.85, 0.08, n_samples).clip(0.60, 0.95),
        'drive_and_kick_rate': np.random.normal(0.12, 0.05, n_samples).clip(0.0, 0.30),
        'pick_and_roll_frequency': np.random.normal(0.25, 0.10, n_samples).clip(0.0, 0.50),
        
        # Court area touches
        'frontcourt_touches': np.random.normal(15.0, 5.0, n_samples).clip(5.0, 30.0),
        'paint_touches': np.random.normal(3.0, 2.0, n_samples).clip(0.0, 10.0),
        'post_touches': np.random.normal(1.0, 1.0, n_samples).clip(0.0, 5.0),
        'elbow_touches': np.random.normal(2.0, 1.5, n_samples).clip(0.0, 8.0),
        
        # Teammate features
        'teammate_fg_pct': np.random.normal(0.45, 0.05, n_samples).clip(0.35, 0.55),
        'teammate_3p_pct': np.random.normal(0.35, 0.06, n_samples).clip(0.25, 0.45),
        
        # Pace and possessions
        'pace_adjusted_possessions': np.random.normal(65.0, 8.0, n_samples).clip(50.0, 85.0),
        
        # Position encoding (one-hot)
        'position_G': np.random.choice([0, 1], n_samples, p=[0.6, 0.4]),
        'position_F': np.random.choice([0, 1], n_samples, p=[0.7, 0.3]),
        'position_C': np.random.choice([0, 1], n_samples, p=[0.8, 0.2]),
        
        # Matchup features
        'opponent_pace': np.random.normal(98.0, 5.0, n_samples).clip(88.0, 108.0),
        'opponent_def_rating': np.random.normal(105.0, 8.0, n_samples).clip(95.0, 120.0),
        
        # Recent performance
        'assists_last_3_games': np.random.normal(4.0, 2.5, n_samples).clip(0.0, 15.0),
        'assists_last_5_games': np.random.normal(4.2, 2.3, n_samples).clip(0.0, 15.0),
        'assists_season_avg': np.random.normal(4.5, 2.8, n_samples).clip(0.0, 15.0),
    }
    
    # Create target (assists) with realistic relationships
    assists = (
        2.0 * data['usage_rate'] +
        8.0 * data['assist_rate_ema10'] +
        0.05 * data['minutes_per_game'] +
        0.3 * data['time_of_possession'] +
        0.2 * data['passes_per_possession'] +
        0.1 * data['potential_assists'] +
        2.0 * data['pass_accuracy'] +
        3.0 * data['drive_and_kick_rate'] +
        1.0 * data['pick_and_roll_frequency'] +
        2.0 * data['position_G'] +  # Guards typically have more assists
        0.3 * data['assists_last_3_games'] +
        np.random.normal(0, 1.5, n_samples)  # Noise
    )
    
    # Ensure realistic assists range
    assists = np.clip(assists, 0.0, 15.0)
    data['assists'] = assists
    
    # Calculate derived features
    data['assist_to_usage_ratio'] = data['assists'] / (data['usage_rate'] + 1e-6)
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Save to CSV
    demo_data_dir = Path('demo_data')
    demo_data_dir.mkdir(exist_ok=True)
    
    demo_path = demo_data_dir / 'demo_assists_data.csv'
    df.to_csv(demo_path, index=False)
    
    logger.info(f"Demo data created and saved to {demo_path}")
    logger.info(f"Assists statistics: mean={np.mean(assists):.2f}, std={np.std(assists):.2f}")
    
    return str(demo_path)


def run_demo_training():
    """Run demo training for assists model"""
    
    logger.info("Starting Assists Model Demo Training")
    
    # Create demo data
    demo_data_path = create_demo_data(n_samples=1000)
    
    # Training configuration
    config = AssistsTrainingConfig(
        input_dim=64,
        hidden_dim=128,
        teammate_dim=16,
        dropout_rate=0.2,
        learning_rate=0.001,
        batch_size=32,
        max_epochs=20,  # Reduced for demo
        patience=8,
        data_path=demo_data_path,
        train_split=0.7,
        val_split=0.15,
        test_split=0.15,
        checkpoint_dir="demo_checkpoints",
        log_dir="demo_logs"
    )
    
    # Initialize trainer
    trainer = AssistsTrainer(config)
    
    # Train model
    logger.info("Training assists model...")
    training_results = trainer.train()
    
    # Evaluate model
    logger.info("Evaluating model...")
    evaluation_results = trainer.evaluate()
    
    # Display results
    print("\\n" + "="*60)
    print("ASSISTS MODEL DEMO TRAINING RESULTS")
    print("="*60)
    
    # Training results
    print(f"Training Status: {'✓ Complete' if training_results['training_complete'] else '✗ Failed'}")
    print(f"Best Validation Loss: {training_results['best_val_loss']:.4f}")
    print(f"Model Checkpoint: {training_results['checkpoint_path']}")
    
    # Evaluation results
    eval_results = evaluation_results['evaluation_results']
    
    if 'core_metrics' in eval_results:
        core_metrics = eval_results['core_metrics']
        print(f"\\nCore Performance Metrics:")
        print(f"  RMSE: {core_metrics['rmse']:.4f}")
        print(f"  MAE: {core_metrics['mae']:.4f}")
        print(f"  R²: {core_metrics['r2']:.4f}")
        print(f"  Assists Accuracy (±1): {core_metrics['assists_accuracy']:.4f}")
        print(f"  Playmaking Efficiency: {core_metrics['playmaking_efficiency']:.4f}")
    
    if 'advanced_metrics' in eval_results:
        advanced_metrics = eval_results['advanced_metrics']
        print(f"\\nAdvanced Metrics:")
        print(f"  Zero-Assist Accuracy: {advanced_metrics['zero_assist_accuracy']:.4f}")
        print(f"  High-Assist Precision: {advanced_metrics['high_assist_precision']:.4f}")
        print(f"  Consistency Score: {advanced_metrics['consistency_score']:.4f}")
    
    # Validation results
    val_results = evaluation_results['validation_results']
    
    if 'core_validation' in val_results:
        core_val = val_results['core_validation']
        print(f"\\nValidation Results:")
        print(f"  Overall Validation Score: {core_val['overall_score']:.4f}")
        
        range_val = core_val['range_validation']
        print(f"  Range Compliance: {range_val['range_compliance']:.4f}")
        print(f"  Prediction Range: [{range_val['min_prediction']:.2f}, {range_val['max_prediction']:.2f}]")
        
        dist_val = core_val['distribution_validation']
        print(f"  Mean Prediction: {dist_val['mean_prediction']:.2f}")
        print(f"  Std Prediction: {dist_val['std_prediction']:.2f}")
    
    # Usage validation
    if 'usage_validation' in val_results:
        usage_val = val_results['usage_validation']
        print(f"\\nUsage Validation:")
        
        if 'core_validation' in usage_val:
            core_usage = usage_val['core_validation']
            print(f"  Monotonic Relationship: {'✓' if core_usage['monotonic_relationship'] else '✗'}")
            print(f"  Low Usage → Assists: {core_usage['low_usage_assists']:.2f}")
            print(f"  Med Usage → Assists: {core_usage['med_usage_assists']:.2f}")
            print(f"  High Usage → Assists: {core_usage['high_usage_assists']:.2f}")
    
    # Playmaking validation
    if 'playmaking_validation' in val_results:
        playmaking_val = val_results['playmaking_validation']
        print(f"\\nPlaymaking Validation:")
        
        if 'assist_rate_validation' in playmaking_val:
            assist_rate_val = playmaking_val['assist_rate_validation']
            print(f"  Assist Rate Correlation: {assist_rate_val['correlation']:.4f}")
            print(f"  Strong Correlation: {'✓' if assist_rate_val['strong_correlation'] else '✗'}")
        
        if 'ratio_validation' in playmaking_val:
            ratio_val = playmaking_val['ratio_validation']
            print(f"  Ratio Validity: {ratio_val['ratio_validity']:.4f}")
            print(f"  Mean Assist-to-Usage Ratio: {ratio_val['mean_ratio']:.2f}")
    
    print("\\n" + "="*60)
    print("Demo training completed successfully!")
    print("="*60)
    
    return {
        'training_results': training_results,
        'evaluation_results': evaluation_results,
        'demo_data_path': demo_data_path
    }


if __name__ == "__main__":
    # Run demo training
    demo_results = run_demo_training()
