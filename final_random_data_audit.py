#!/usr/bin/env python3
"""
Final Random Data Audit for HMNV WNBA Pipeline
==============================================

This script performs a comprehensive audit of all random data generation
patterns in the WNBA analytics pipeline and provides remediation recommendations.
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Critical patterns that must be eliminated for production
CRITICAL_PATTERNS = [
    (r'np\.random\.', 'NumPy random generation'),
    (r'torch\.randn\(', 'PyTorch random tensor generation'),
    (r'torch\.rand\(', 'PyTorch random tensor generation'),
    (r'torch\.randint\(', 'PyTorch random integer generation'),
    (r'random\.', 'Python random module usage'),
    (r'_create_sample_data', 'Sample data generation methods'),
    (r'DummyTotalsModel', 'Dummy model fallbacks'),
    (r'placeholder.*=.*torch\.', 'Placeholder tensor creation'),
]

# Non-critical patterns that should be moved to config
NON_CRITICAL_PATTERNS = [
    (r'random_state\s*=\s*\d+', 'Random state configuration'),
    (r'seed\s*=\s*\d+', 'Seed configuration'),
    (r'np\.random\.seed\(', 'Seed setting (OK if for reproducibility)'),
    (r'torch\.manual_seed\(', 'PyTorch seed setting'),
]

# File patterns to exclude from audit
EXCLUDE_PATTERNS = [
    r'.*__pycache__.*',
    r'.*\.pyc$',
    r'.*\.md$',
    r'.*\.txt$',
    r'.*\.log$',
    r'.*\.png$',
    r'.*\.ckpt$',
    r'.*test.*\.py$',
    r'.*demo.*\.py$',
    r'.*validate.*\.py$',
    r'.*audit.*\.py$',
    r'.*check.*\.py$',
]

class RandomDataAuditor:
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.critical_issues = []
        self.non_critical_issues = []
        self.files_scanned = 0
        self.production_models = [
            'step_01_player_points_model',
            'step_02_game_totals_model',
            'step_03_moneyline_model',
            'step_04_rebounds_model',
            'step_05_spread_model',
            'step_06_assists_model',
            'step_07_threes_model',
            'step_08_steals_blocks_model'
        ]
    
    def should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from audit."""
        file_str = str(file_path)
        return any(re.search(pattern, file_str) for pattern in EXCLUDE_PATTERNS)
    
    def audit_file(self, file_path: Path) -> Tuple[List[Dict], List[Dict]]:
        """Audit a single file for random data patterns."""
        critical_issues = []
        non_critical_issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # Check critical patterns
                for pattern, description in CRITICAL_PATTERNS:
                    if re.search(pattern, line):
                        critical_issues.append({
                            'file': str(file_path),
                            'line': line_num,
                            'pattern': pattern,
                            'description': description,
                            'content': line.strip(),
                            'severity': 'CRITICAL'
                        })
                
                # Check non-critical patterns
                for pattern, description in NON_CRITICAL_PATTERNS:
                    if re.search(pattern, line):
                        non_critical_issues.append({
                            'file': str(file_path),
                            'line': line_num,
                            'pattern': pattern,
                            'description': description,
                            'content': line.strip(),
                            'severity': 'WARNING'
                        })
        
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
        
        return critical_issues, non_critical_issues
    
    def audit_workspace(self) -> Dict:
        """Audit entire workspace for random data patterns."""
        print(f"Auditing workspace: {self.workspace_path}")
        print("=" * 60)
        
        # Scan all Python files
        for py_file in self.workspace_path.rglob('*.py'):
            if self.should_exclude_file(py_file):
                continue
            
            critical, non_critical = self.audit_file(py_file)
            self.critical_issues.extend(critical)
            self.non_critical_issues.extend(non_critical)
            self.files_scanned += 1
        
        # Organize by model
        model_issues = {}
        for model in self.production_models:
            model_issues[model] = {
                'critical': [issue for issue in self.critical_issues if model in issue['file']],
                'non_critical': [issue for issue in self.non_critical_issues if model in issue['file']]
            }
        
        return {
            'total_files_scanned': self.files_scanned,
            'total_critical_issues': len(self.critical_issues),
            'total_non_critical_issues': len(self.non_critical_issues),
            'model_issues': model_issues,
            'all_critical_issues': self.critical_issues,
            'all_non_critical_issues': self.non_critical_issues
        }
    
    def generate_report(self, results: Dict) -> str:
        """Generate comprehensive audit report."""
        report = []
        report.append("HMNV WNBA PIPELINE - FINAL RANDOM DATA AUDIT REPORT")
        report.append("=" * 60)
        report.append(f"Files Scanned: {results['total_files_scanned']}")
        report.append(f"Critical Issues: {results['total_critical_issues']}")
        report.append(f"Non-Critical Issues: {results['total_non_critical_issues']}")
        report.append("")
        
        # Critical issues summary
        if results['total_critical_issues'] > 0:
            report.append("CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION:")
            report.append("-" * 50)
            
            for model, issues in results['model_issues'].items():
                if issues['critical']:
                    report.append(f"\n{model.upper()}:")
                    for issue in issues['critical']:
                        relative_path = str(Path(issue['file']).relative_to(self.workspace_path))
                        report.append(f"  ⚠️  {relative_path}:{issue['line']}")
                        report.append(f"     Pattern: {issue['description']}")
                        report.append(f"     Code: {issue['content']}")
                        report.append("")
        
        # Non-critical issues summary
        if results['total_non_critical_issues'] > 0:
            report.append("\nNON-CRITICAL ISSUES (Configuration Recommendations):")
            report.append("-" * 50)
            
            for model, issues in results['model_issues'].items():
                if issues['non_critical']:
                    report.append(f"\n{model.upper()}:")
                    for issue in issues['non_critical']:
                        relative_path = str(Path(issue['file']).relative_to(self.workspace_path))
                        report.append(f"  ℹ️  {relative_path}:{issue['line']}")
                        report.append(f"     Pattern: {issue['description']}")
                        report.append(f"     Code: {issue['content']}")
                        report.append("")
        
        # Remediation recommendations
        report.append("\nREMEDIATION RECOMMENDATIONS:")
        report.append("-" * 30)
        
        if results['total_critical_issues'] > 0:
            report.append("\n1. IMMEDIATE ACTIONS (Critical Issues):")
            report.append("   - Remove all _create_sample_data() methods")
            report.append("   - Replace np.random.* with real data or proper imputation")
            report.append("   - Remove torch.randn/rand placeholder tensors")
            report.append("   - Eliminate all DummyTotalsModel fallbacks")
            report.append("   - Ensure FileNotFoundError when unified data is missing")
        
        if results['total_non_critical_issues'] > 0:
            report.append("\n2. CONFIGURATION IMPROVEMENTS (Non-Critical):")
            report.append("   - Move random_state settings to config files")
            report.append("   - Centralize seed management for reproducibility")
            report.append("   - Document random seed usage for testing")
        
        report.append("\n3. VALIDATION STEPS:")
        report.append("   - Run unified data validation scripts")
        report.append("   - Test all models with real consolidated_wnba data")
        report.append("   - Verify no synthetic data in production pipelines")
        report.append("   - Set up monitoring for data quality issues")
        
        return "\n".join(report)
    
    def save_detailed_report(self, results: Dict, filename: str = "final_random_audit_detailed.md"):
        """Save detailed audit report with all findings."""
        detailed_report = []
        detailed_report.append("# HMNV WNBA Pipeline - Detailed Random Data Audit")
        detailed_report.append("")
        detailed_report.append(f"**Audit Date**: {Path(__file__).stat().st_mtime}")
        detailed_report.append(f"**Files Scanned**: {results['total_files_scanned']}")
        detailed_report.append(f"**Critical Issues**: {results['total_critical_issues']}")
        detailed_report.append(f"**Non-Critical Issues**: {results['total_non_critical_issues']}")
        detailed_report.append("")
        
        # All critical issues
        if results['all_critical_issues']:
            detailed_report.append("## Critical Issues")
            detailed_report.append("")
            for issue in results['all_critical_issues']:
                relative_path = str(Path(issue['file']).relative_to(self.workspace_path))
                detailed_report.append(f"### {relative_path}:{issue['line']}")
                detailed_report.append(f"**Pattern**: {issue['description']}")
                detailed_report.append(f"**Code**: `{issue['content']}`")
                detailed_report.append("")
        
        # All non-critical issues
        if results['all_non_critical_issues']:
            detailed_report.append("## Non-Critical Issues")
            detailed_report.append("")
            for issue in results['all_non_critical_issues']:
                relative_path = str(Path(issue['file']).relative_to(self.workspace_path))
                detailed_report.append(f"### {relative_path}:{issue['line']}")
                detailed_report.append(f"**Pattern**: {issue['description']}")
                detailed_report.append(f"**Code**: `{issue['content']}`")
                detailed_report.append("")
        
        # Save report
        report_path = self.workspace_path / filename
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(detailed_report))
        
        print(f"Detailed report saved to: {report_path}")

def main():
    """Main audit execution."""
    workspace_path = Path(__file__).parent
    auditor = RandomDataAuditor(str(workspace_path))
    
    print("Starting final random data audit...")
    print("This may take a moment to scan all files...")
    
    results = auditor.audit_workspace()
    
    # Generate and display report
    report = auditor.generate_report(results)
    print(report)
    
    # Save detailed report
    auditor.save_detailed_report(results)
    
    # Return exit code based on critical issues
    if results['total_critical_issues'] > 0:
        print(f"\n❌ AUDIT FAILED: {results['total_critical_issues']} critical issues found")
        return 1
    else:
        print(f"\n✅ AUDIT PASSED: No critical random data issues found")
        return 0

if __name__ == "__main__":
    exit(main())
