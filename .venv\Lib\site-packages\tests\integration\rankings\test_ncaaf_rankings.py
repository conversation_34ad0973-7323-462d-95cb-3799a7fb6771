import mock
import pytest
from flexmock import flexmock
from os.path import join, dirname
from sportsreference import utils
from sportsreference.ncaaf.rankings import CFPRankings, Rankings


YEAR = 2017


def read_file(filename):
    filepath = join(dirname(__file__), 'ncaaf', filename)
    return open(filepath, 'r', encoding='utf8').read()


def mock_pyquery(url):
    class MockPQ:
        def __init__(self, html_contents, status_code=200):
            self.url = url
            self.reason = 'Bad URL'  # Used when throwing HTTPErrors
            self.headers = {}  # Used when throwing HTTPErrors
            self.status_code = status_code
            self.html_contents = html_contents
            self.text = html_contents

        def __call__(self, div):
            return read_file()

    if 'BAD' in url:
        return MockPQ('', 404)
    html_contents = read_file('%s-polls.html' % YEAR)
    return MockPQ(html_contents)


def mock_request(url):
    class MockRequest:
        def __init__(self, html_contents, status_code=200):
            self.status_code = status_code
            self.html_contents = html_contents
            self.text = html_contents

    if str(YEAR) in url:
        return MockRequest('good')
    else:
        return MockRequest('bad', status_code=404)


class TestNCAAFRankings:
    def setup_method(self):
        results_extended = [
            {
                'abbreviation': 'alabama',
                'name': 'Alabama',
                'rank': 1,
                'week': 16,
                'date': 'Final',
                'previous': '4',
                'change': 3
            },
            {
                'abbreviation': 'georgia',
                'name': 'Georgia',
                'rank': 2,
                'week': 16,
                'date': 'Final',
                'previous': '3',
                'change': 1
            },
            {
                'abbreviation': 'oklahoma',
                'name': 'Oklahoma',
                'rank': 3,
                'week': 16,
                'date': 'Final',
                'previous': '2',
                'change': -1
            },
            {
                'abbreviation': 'clemson',
                'name': 'Clemson',
                'rank': 4,
                'week': 16,
                'date': 'Final',
                'previous': '1',
                'change': -3
            },
            {
                'abbreviation': 'ohio-state',
                'name': 'Ohio State',
                'rank': 5,
                'week': 16,
                'date': 'Final',
                'previous': '5',
                'change': 0
            },
            {
                'abbreviation': 'central-florida',
                'name': 'UCF',
                'rank': 6,
                'week': 16,
                'date': 'Final',
                'previous': '10',
                'change': 4
            },
            {
                'abbreviation': 'wisconsin',
                'name': 'Wisconsin',
                'rank': 7,
                'week': 16,
                'date': 'Final',
                'previous': '6',
                'change': -1
            },
            {
                'abbreviation': 'penn-state',
                'name': 'Penn State',
                'rank': 8,
                'week': 16,
                'date': 'Final',
                'previous': '9',
                'change': 1
            },
            {
                'abbreviation': 'texas-christian',
                'name': 'Texas Christian',
                'rank': 9,
                'week': 16,
                'date': 'Final',
                'previous': '13',
                'change': 4
            },
            {
                'abbreviation': 'auburn',
                'name': 'Auburn',
                'rank': 10,
                'week': 16,
                'date': 'Final',
                'previous': '7',
                'change': -3
            },
            {
                'abbreviation': 'notre-dame',
                'name': 'Notre Dame',
                'rank': 11,
                'week': 16,
                'date': 'Final',
                'previous': '14',
                'change': 3
            },
            {
                'abbreviation': 'southern-california',
                'name': 'USC',
                'rank': 12,
                'week': 16,
                'date': 'Final',
                'previous': '8',
                'change': -4
            },
            {
                'abbreviation': 'miami-fl',
                'name': 'Miami (FL)',
                'rank': 13,
                'week': 16,
                'date': 'Final',
                'previous': '11',
                'change': -2
            },
            {
                'abbreviation': 'oklahoma-state',
                'name': 'Oklahoma State',
                'rank': 14,
                'week': 16,
                'date': 'Final',
                'previous': '17',
                'change': 3
            },
            {
                'abbreviation': 'michigan-state',
                'name': 'Michigan State',
                'rank': 15,
                'week': 16,
                'date': 'Final',
                'previous': '18',
                'change': 3
            },
            {
                'abbreviation': 'washington',
                'name': 'Washington',
                'rank': 16,
                'week': 16,
                'date': 'Final',
                'previous': '12',
                'change': -4
            },
            {
                'abbreviation': 'northwestern',
                'name': 'Northwestern',
                'rank': 17,
                'week': 16,
                'date': 'Final',
                'previous': '20',
                'change': 3
            },
            {
                'abbreviation': 'louisiana-state',
                'name': 'LSU',
                'rank': 18,
                'week': 16,
                'date': 'Final',
                'previous': '16',
                'change': -2
            },
            {
                'abbreviation': 'mississippi-state',
                'name': 'Mississippi State',
                'rank': 19,
                'week': 16,
                'date': 'Final',
                'previous': '24',
                'change': 5
            },
            {
                'abbreviation': 'stanford',
                'name': 'Stanford',
                'rank': 20,
                'week': 16,
                'date': 'Final',
                'previous': '15',
                'change': -5
            },
            {
                'abbreviation': 'south-florida',
                'name': 'South Florida',
                'rank': 21,
                'week': 16,
                'date': 'Final',
                'previous': '23',
                'change': 2
            },
            {
                'abbreviation': 'boise-state',
                'name': 'Boise State',
                'rank': 22,
                'week': 16,
                'date': 'Final',
                'previous': '25',
                'change': 3
            },
            {
                'abbreviation': 'north-carolina-state',
                'name': 'North Carolina State',
                'rank': 23,
                'week': 16,
                'date': 'Final',
                'previous': '',
                'change': 0
            },
            {
                'abbreviation': 'virginia-tech',
                'name': 'Virginia Tech',
                'rank': 24,
                'week': 16,
                'date': 'Final',
                'previous': '22',
                'change': -2
            },
            {
                'abbreviation': 'memphis',
                'name': 'Memphis',
                'rank': 25,
                'week': 16,
                'date': 'Final',
                'previous': '19',
                'change': -6
            }
        ]
        results = {
            'alabama': 1,
            'georgia': 2,
            'oklahoma': 3,
            'clemson': 4,
            'ohio-state': 5,
            'central-florida': 6,
            'wisconsin': 7,
            'penn-state': 8,
            'texas-christian': 9,
            'auburn': 10,
            'notre-dame': 11,
            'southern-california': 12,
            'miami-fl': 13,
            'oklahoma-state': 14,
            'michigan-state': 15,
            'washington': 16,
            'northwestern': 17,
            'louisiana-state': 18,
            'mississippi-state': 19,
            'stanford': 20,
            'south-florida': 21,
            'boise-state': 22,
            'north-carolina-state': 23,
            'virginia-tech': 24,
            'memphis': 25
        }
        results_complete = {16: [
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 1,
             'week': 16,
             'date': 'Final',
             'previous': '4',
             'change': 3},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 2,
             'week': 16,
             'date': 'Final',
             'previous': '3',
             'change': 1},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 3,
             'week': 16,
             'date': 'Final',
             'previous': '2',
             'change': -1},
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 4,
             'week': 16,
             'date': 'Final',
             'previous': '1',
             'change': -3},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 5,
             'week': 16,
             'date': 'Final',
             'previous': '5',
             'change': 0},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 6,
             'week': 16,
             'date': 'Final',
             'previous': '10',
             'change': 4},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 7,
             'week': 16,
             'date': 'Final',
             'previous': '6',
             'change': -1},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 8,
             'week': 16,
             'date': 'Final',
             'previous': '9',
             'change': 1},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 9,
             'week': 16,
             'date': 'Final',
             'previous': '13',
             'change': 4},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 10,
             'week': 16,
             'date': 'Final',
             'previous': '7',
             'change': -3},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 11,
             'week': 16,
             'date': 'Final',
             'previous': '14',
             'change': 3},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 12,
             'week': 16,
             'date': 'Final',
             'previous': '8',
             'change': -4},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 13,
             'week': 16,
             'date': 'Final',
             'previous': '11',
             'change': -2},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 14,
             'week': 16,
             'date': 'Final',
             'previous': '17',
             'change': 3},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 15,
             'week': 16,
             'date': 'Final',
             'previous': '18',
             'change': 3},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 16,
             'week': 16,
             'date': 'Final',
             'previous': '12',
             'change': -4},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 17,
             'week': 16,
             'date': 'Final',
             'previous': '20',
             'change': 3},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 18,
             'week': 16,
             'date': 'Final',
             'previous': '16',
             'change': -2},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 19,
             'week': 16,
             'date': 'Final',
             'previous': '24',
             'change': 5},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 20,
             'week': 16,
             'date': 'Final',
             'previous': '15',
             'change': -5},
            {'abbreviation': 'south-florida',
             'name': 'South Florida',
             'rank': 21,
             'week': 16,
             'date': 'Final',
             'previous': '23',
             'change': 2},
            {'abbreviation': 'boise-state',
             'name': 'Boise State',
             'rank': 22,
             'week': 16,
             'date': 'Final',
             'previous': '25',
             'change': 3},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 23,
             'week': 16,
             'date': 'Final',
             'previous': '',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 24,
             'week': 16,
             'date': 'Final',
             'previous': '22',
             'change': -2},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 25,
             'week': 16,
             'date': 'Final',
             'previous': '19',
             'change': -6}
            ],
            15: [
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 1,
             'week': 15,
             'date': '2017-12-03',
             'previous': '1',
             'change': 0},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 2,
             'week': 15,
             'date': '2017-12-03',
             'previous': '2',
             'change': 0},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 3,
             'week': 15,
             'date': '2017-12-03',
             'previous': '6',
             'change': 3},
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 4,
             'week': 15,
             'date': '2017-12-03',
             'previous': '5',
             'change': 1},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 5,
             'week': 15,
             'date': '2017-12-03',
             'previous': '8',
             'change': 3},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 6,
             'week': 15,
             'date': '2017-12-03',
             'previous': '3',
             'change': -3},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 7,
             'week': 15,
             'date': '2017-12-03',
             'previous': '4',
             'change': -3},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 8,
             'week': 15,
             'date': '2017-12-03',
             'previous': '11',
             'change': 3},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 9,
             'week': 15,
             'date': '2017-12-03',
             'previous': '9',
             'change': 0},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 10,
             'week': 15,
             'date': '2017-12-03',
             'previous': '12',
             'change': 2},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 11,
             'week': 15,
             'date': '2017-12-03',
             'previous': '7',
             'change': -4},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 12,
             'week': 15,
             'date': '2017-12-03',
             'previous': '13',
             'change': 1},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 13,
             'week': 15,
             'date': '2017-12-03',
             'previous': '10',
             'change': -3},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 14,
             'week': 15,
             'date': '2017-12-03',
             'previous': '15',
             'change': 1},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 15,
             'week': 15,
             'date': '2017-12-03',
             'previous': '14',
             'change': -1},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 16,
             'week': 15,
             'date': '2017-12-03',
             'previous': '17',
             'change': 1},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 17,
             'week': 15,
             'date': '2017-12-03',
             'previous': '18',
             'change': 1},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 18,
             'week': 15,
             'date': '2017-12-03',
             'previous': '19',
             'change': 1},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 19,
             'week': 15,
             'date': '2017-12-03',
             'previous': '16',
             'change': -3},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 20,
             'week': 15,
             'date': '2017-12-03',
             'previous': '20',
             'change': 0},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 21,
             'week': 15,
             'date': '2017-12-03',
             'previous': '21',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 22,
             'week': 15,
             'date': '2017-12-03',
             'previous': '22',
             'change': 0},
            {'abbreviation': 'south-florida',
             'name': 'South Florida',
             'rank': 23,
             'week': 15,
             'date': '2017-12-03',
             'previous': '23',
             'change': 0},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 24,
             'week': 15,
             'date': '2017-12-03',
             'previous': '24',
             'change': 0},
            {'abbreviation': 'boise-state',
             'name': 'Boise State',
             'rank': 25,
             'week': 15,
             'date': '2017-12-03',
             'previous': '',
             'change': 0}
            ]}
        self.results_extended = results_extended
        self.results = results
        self.results_complete = results_complete

    @mock.patch('requests.get', side_effect=mock_pyquery)
    def test_rankings_integration(self, *args, **kwargs):
        flexmock(utils) \
            .should_receive('_find_year_for_season') \
            .and_return(YEAR)

        rankings = Rankings()

        assert rankings.current_extended == self.results_extended
        assert rankings.current == self.results
        assert rankings.complete == self.results_complete

    @mock.patch('requests.get', side_effect=mock_pyquery)
    def test_rankings_integration_bad_url(self, *args, **kwargs):
        with pytest.raises(ValueError):
            rankings = Rankings('BAD')

    @mock.patch('requests.get', side_effect=mock_pyquery)
    @mock.patch('requests.head', side_effect=mock_request)
    def test_invalid_default_year_reverts_to_previous_year(self,
                                                           *args,
                                                           **kwargs):
        flexmock(utils) \
            .should_receive('_find_year_for_season') \
            .and_return(2018)

        rankings = Rankings()

        assert rankings.current_extended == self.results_extended
        assert rankings.current == self.results
        assert rankings.complete == self.results_complete


class TestCFPNCAAFRankings:
    def setup_method(self):
        results_extended = [
            {
                'abbreviation': 'clemson',
                'name': 'Clemson',
                'rank': 1,
                'week': 6,
                'date': '2017-12-03',
                'previous': '1',
                'change': 0
            },
            {
                'abbreviation': 'oklahoma',
                'name': 'Oklahoma',
                'rank': 2,
                'week': 6,
                'date': '2017-12-03',
                'previous': '3',
                'change': 1
            },
            {
                'abbreviation': 'georgia',
                'name': 'Georgia',
                'rank': 3,
                'week': 6,
                'date': '2017-12-03',
                'previous': '6',
                'change': 3
            },
            {
                'abbreviation': 'alabama',
                'name': 'Alabama',
                'rank': 4,
                'week': 6,
                'date': '2017-12-03',
                'previous': '5',
                'change': 1
            },
            {
                'abbreviation': 'ohio-state',
                'name': 'Ohio State',
                'rank': 5,
                'week': 6,
                'date': '2017-12-03',
                'previous': '8',
                'change': 3
            },
            {
                'abbreviation': 'wisconsin',
                'name': 'Wisconsin',
                'rank': 6,
                'week': 6,
                'date': '2017-12-03',
                'previous': '4',
                'change': -2
            },
            {
                'abbreviation': 'auburn',
                'name': 'Auburn',
                'rank': 7,
                'week': 6,
                'date': '2017-12-03',
                'previous': '2',
                'change': -5
            },
            {
                'abbreviation': 'southern-california',
                'name': 'USC',
                'rank': 8,
                'week': 6,
                'date': '2017-12-03',
                'previous': '10',
                'change': 2
            },
            {
                'abbreviation': 'penn-state',
                'name': 'Penn State',
                'rank': 9,
                'week': 6,
                'date': '2017-12-03',
                'previous': '9',
                'change': 0
            },
            {
                'abbreviation': 'miami-fl',
                'name': 'Miami (FL)',
                'rank': 10,
                'week': 6,
                'date': '2017-12-03',
                'previous': '7',
                'change': -3
            },
            {
                'abbreviation': 'washington',
                'name': 'Washington',
                'rank': 11,
                'week': 6,
                'date': '2017-12-03',
                'previous': '13',
                'change': 2
            },
            {
                'abbreviation': 'central-florida',
                'name': 'UCF',
                'rank': 12,
                'week': 6,
                'date': '2017-12-03',
                'previous': '14',
                'change': 2
            },
            {
                'abbreviation': 'stanford',
                'name': 'Stanford',
                'rank': 13,
                'week': 6,
                'date': '2017-12-03',
                'previous': '12',
                'change': -1
            },
            {
                'abbreviation': 'notre-dame',
                'name': 'Notre Dame',
                'rank': 14,
                'week': 6,
                'date': '2017-12-03',
                'previous': '15',
                'change': 1
            },
            {
                'abbreviation': 'texas-christian',
                'name': 'Texas Christian',
                'rank': 15,
                'week': 6,
                'date': '2017-12-03',
                'previous': '11',
                'change': -4
            },
            {
                'abbreviation': 'michigan-state',
                'name': 'Michigan State',
                'rank': 16,
                'week': 6,
                'date': '2017-12-03',
                'previous': '16',
                'change': 0
            },
            {
                'abbreviation': 'louisiana-state',
                'name': 'LSU',
                'rank': 17,
                'week': 6,
                'date': '2017-12-03',
                'previous': '17',
                'change': 0
            },
            {
                'abbreviation': 'washington-state',
                'name': 'Washington State',
                'rank': 18,
                'week': 6,
                'date': '2017-12-03',
                'previous': '18',
                'change': 0
            },
            {
                'abbreviation': 'oklahoma-state',
                'name': 'Oklahoma State',
                'rank': 19,
                'week': 6,
                'date': '2017-12-03',
                'previous': '19',
                'change': 0
            },
            {
                'abbreviation': 'memphis',
                'name': 'Memphis',
                'rank': 20,
                'week': 6,
                'date': '2017-12-03',
                'previous': '20',
                'change': 0
            },
            {
                'abbreviation': 'northwestern',
                'name': 'Northwestern',
                'rank': 21,
                'week': 6,
                'date': '2017-12-03',
                'previous': '21',
                'change': 0
            },
            {
                'abbreviation': 'virginia-tech',
                'name': 'Virginia Tech',
                'rank': 22,
                'week': 6,
                'date': '2017-12-03',
                'previous': '22',
                'change': 0
            },
            {
                'abbreviation': 'mississippi-state',
                'name': 'Mississippi State',
                'rank': 23,
                'week': 6,
                'date': '2017-12-03',
                'previous': '23',
                'change': 0
            },
            {
                'abbreviation': 'north-carolina-state',
                'name': 'North Carolina State',
                'rank': 24,
                'week': 6,
                'date': '2017-12-03',
                'previous': '24',
                'change': 0
            },
            {
                'abbreviation': 'boise-state',
                'name': 'Boise State',
                'rank': 25,
                'week': 6,
                'date': '2017-12-03',
                'previous': '',
                'change': 0
            }
        ]
        results = {
            'clemson': 1,
            'oklahoma': 2,
            'georgia': 3,
            'alabama': 4,
            'ohio-state': 5,
            'wisconsin': 6,
            'auburn': 7,
            'southern-california': 8,
            'penn-state': 9,
            'miami-fl': 10,
            'washington': 11,
            'central-florida': 12,
            'stanford': 13,
            'notre-dame': 14,
            'texas-christian': 15,
            'michigan-state': 16,
            'louisiana-state': 17,
            'washington-state': 18,
            'oklahoma-state': 19,
            'memphis': 20,
            'northwestern': 21,
            'virginia-tech': 22,
            'mississippi-state': 23,
            'north-carolina-state': 24,
            'boise-state': 25
        }
        results_complete = {6: [
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 1,
             'week': 6,
             'date': '2017-12-03',
             'previous': '1',
             'change': 0},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 2,
             'week': 6,
             'date': '2017-12-03',
             'previous': '3',
             'change': 1},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 3,
             'week': 6,
             'date': '2017-12-03',
             'previous': '6',
             'change': 3},
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 4,
             'week': 6,
             'date': '2017-12-03',
             'previous': '5',
             'change': 1},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 5,
             'week': 6,
             'date': '2017-12-03',
             'previous': '8',
             'change': 3},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 6,
             'week': 6,
             'date': '2017-12-03',
             'previous': '4',
             'change': -2},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 7,
             'week': 6,
             'date': '2017-12-03',
             'previous': '2',
             'change': -5},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 8,
             'week': 6,
             'date': '2017-12-03',
             'previous': '10',
             'change': 2},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 9,
             'week': 6,
             'date': '2017-12-03',
             'previous': '9',
             'change': 0},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 10,
             'week': 6,
             'date': '2017-12-03',
             'previous': '7',
             'change': -3},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 11,
             'week': 6,
             'date': '2017-12-03',
             'previous': '13',
             'change': 2},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 12,
             'week': 6,
             'date': '2017-12-03',
             'previous': '14',
             'change': 2},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 13,
             'week': 6,
             'date': '2017-12-03',
             'previous': '12',
             'change': -1},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 14,
             'week': 6,
             'date': '2017-12-03',
             'previous': '15',
             'change': 1},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 15,
             'week': 6,
             'date': '2017-12-03',
             'previous': '11',
             'change': -4},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 16,
             'week': 6,
             'date': '2017-12-03',
             'previous': '16',
             'change': 0},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 17,
             'week': 6,
             'date': '2017-12-03',
             'previous': '17',
             'change': 0},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 18,
             'week': 6,
             'date': '2017-12-03',
             'previous': '18',
             'change': 0},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 19,
             'week': 6,
             'date': '2017-12-03',
             'previous': '19',
             'change': 0},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 20,
             'week': 6,
             'date': '2017-12-03',
             'previous': '20',
             'change': 0},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 21,
             'week': 6,
             'date': '2017-12-03',
             'previous': '21',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 22,
             'week': 6,
             'date': '2017-12-03',
             'previous': '22',
             'change': 0},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 23,
             'week': 6,
             'date': '2017-12-03',
             'previous': '23',
             'change': 0},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 24,
             'week': 6,
             'date': '2017-12-03',
             'previous': '24',
             'change': 0},
            {'abbreviation': 'boise-state',
             'name': 'Boise State',
             'rank': 25,
             'week': 6,
             'date': '2017-12-03',
             'previous': '',
             'change': 0}],
            5: [
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 1,
             'week': 5,
             'date': '2017-11-28',
             'previous': '3',
             'change': 2},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 2,
             'week': 5,
             'date': '2017-11-28',
             'previous': '6',
             'change': 4},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 3,
             'week': 5,
             'date': '2017-11-28',
             'previous': '4',
             'change': 1},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 4,
             'week': 5,
             'date': '2017-11-28',
             'previous': '5',
             'change': 1},
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 5,
             'week': 5,
             'date': '2017-11-28',
             'previous': '1',
             'change': -4},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 6,
             'week': 5,
             'date': '2017-11-28',
             'previous': '7',
             'change': 1},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 7,
             'week': 5,
             'date': '2017-11-28',
             'previous': '2',
             'change': -5},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 8,
             'week': 5,
             'date': '2017-11-28',
             'previous': '9',
             'change': 1},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 9,
             'week': 5,
             'date': '2017-11-28',
             'previous': '10',
             'change': 1},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 10,
             'week': 5,
             'date': '2017-11-28',
             'previous': '11',
             'change': 1},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 11,
             'week': 5,
             'date': '2017-11-28',
             'previous': '12',
             'change': 1},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 12,
             'week': 5,
             'date': '2017-11-28',
             'previous': '21',
             'change': 9},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 13,
             'week': 5,
             'date': '2017-11-28',
             'previous': '17',
             'change': 4},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 14,
             'week': 5,
             'date': '2017-11-28',
             'previous': '15',
             'change': 1},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 15,
             'week': 5,
             'date': '2017-11-28',
             'previous': '8',
             'change': -7},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 16,
             'week': 5,
             'date': '2017-11-28',
             'previous': '16',
             'change': 0},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 17,
             'week': 5,
             'date': '2017-11-28',
             'previous': '18',
             'change': 1},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 18,
             'week': 5,
             'date': '2017-11-28',
             'previous': '13',
             'change': -5},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 19,
             'week': 5,
             'date': '2017-11-28',
             'previous': '19',
             'change': 0},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 20,
             'week': 5,
             'date': '2017-11-28',
             'previous': '20',
             'change': 0},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 21,
             'week': 5,
             'date': '2017-11-28',
             'previous': '22',
             'change': 1},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 22,
             'week': 5,
             'date': '2017-11-28',
             'previous': '25',
             'change': 3},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 23,
             'week': 5,
             'date': '2017-11-28',
             'previous': '14',
             'change': -9},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 24,
             'week': 5,
             'date': '2017-11-28',
             'previous': '',
             'change': 0},
            {'abbreviation': 'fresno-state',
             'name': 'Fresno State',
             'rank': 25,
             'week': 5,
             'date': '2017-11-28',
             'previous': '',
             'change': 0}],
            4: [
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 1,
             'week': 4,
             'date': '2017-11-21',
             'previous': '1',
             'change': 0},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 2,
             'week': 4,
             'date': '2017-11-21',
             'previous': '3',
             'change': 1},
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 3,
             'week': 4,
             'date': '2017-11-21',
             'previous': '2',
             'change': -1},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 4,
             'week': 4,
             'date': '2017-11-21',
             'previous': '4',
             'change': 0},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 5,
             'week': 4,
             'date': '2017-11-21',
             'previous': '5',
             'change': 0},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 6,
             'week': 4,
             'date': '2017-11-21',
             'previous': '6',
             'change': 0},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 7,
             'week': 4,
             'date': '2017-11-21',
             'previous': '7',
             'change': 0},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 8,
             'week': 4,
             'date': '2017-11-21',
             'previous': '8',
             'change': 0},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 9,
             'week': 4,
             'date': '2017-11-21',
             'previous': '9',
             'change': 0},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 10,
             'week': 4,
             'date': '2017-11-21',
             'previous': '10',
             'change': 0},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 11,
             'week': 4,
             'date': '2017-11-21',
             'previous': '11',
             'change': 0},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 12,
             'week': 4,
             'date': '2017-11-21',
             'previous': '12',
             'change': 0},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 13,
             'week': 4,
             'date': '2017-11-21',
             'previous': '14',
             'change': 1},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 14,
             'week': 4,
             'date': '2017-11-21',
             'previous': '16',
             'change': 2},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 15,
             'week': 4,
             'date': '2017-11-21',
             'previous': '15',
             'change': 0},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 16,
             'week': 4,
             'date': '2017-11-21',
             'previous': '17',
             'change': 1},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 17,
             'week': 4,
             'date': '2017-11-21',
             'previous': '18',
             'change': 1},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 18,
             'week': 4,
             'date': '2017-11-21',
             'previous': '20',
             'change': 2},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 19,
             'week': 4,
             'date': '2017-11-21',
             'previous': '13',
             'change': -6},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 20,
             'week': 4,
             'date': '2017-11-21',
             'previous': '21',
             'change': 1},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 21,
             'week': 4,
             'date': '2017-11-21',
             'previous': '22',
             'change': 1},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 22,
             'week': 4,
             'date': '2017-11-21',
             'previous': '23',
             'change': 1},
            {'abbreviation': 'boise-state',
             'name': 'Boise State',
             'rank': 23,
             'week': 4,
             'date': '2017-11-21',
             'previous': '25',
             'change': 2},
            {'abbreviation': 'south-carolina',
             'name': 'South Carolina',
             'rank': 24,
             'week': 4,
             'date': '2017-11-21',
             'previous': '',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 25,
             'week': 4,
             'date': '2017-11-21',
             'previous': '',
             'change': 0}],
            3: [
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 1,
             'week': 3,
             'date': '2017-11-14',
             'previous': '2',
             'change': 1},
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 2,
             'week': 3,
             'date': '2017-11-14',
             'previous': '4',
             'change': 2},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 3,
             'week': 3,
             'date': '2017-11-14',
             'previous': '10',
             'change': 7},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 4,
             'week': 3,
             'date': '2017-11-14',
             'previous': '5',
             'change': 1},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 5,
             'week': 3,
             'date': '2017-11-14',
             'previous': '9',
             'change': 4},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 6,
             'week': 3,
             'date': '2017-11-14',
             'previous': '14',
             'change': 8},
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 7,
             'week': 3,
             'date': '2017-11-14',
             'previous': '1',
             'change': -6},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 8,
             'week': 3,
             'date': '2017-11-14',
             'previous': '3',
             'change': -5},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 9,
             'week': 3,
             'date': '2017-11-14',
             'previous': '6',
             'change': -3},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 10,
             'week': 3,
             'date': '2017-11-14',
             'previous': '7',
             'change': -3},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 11,
             'week': 3,
             'date': '2017-11-14',
             'previous': '17',
             'change': 6},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 12,
             'week': 3,
             'date': '2017-11-14',
             'previous': '8',
             'change': -4},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 13,
             'week': 3,
             'date': '2017-11-14',
             'previous': '11',
             'change': -2},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 14,
             'week': 3,
             'date': '2017-11-14',
             'previous': '25',
             'change': 11},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 15,
             'week': 3,
             'date': '2017-11-14',
             'previous': '18',
             'change': 3},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 16,
             'week': 3,
             'date': '2017-11-14',
             'previous': '16',
             'change': 0},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 17,
             'week': 3,
             'date': '2017-11-14',
             'previous': '24',
             'change': 7},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 18,
             'week': 3,
             'date': '2017-11-14',
             'previous': '12',
             'change': -6},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 19,
             'week': 3,
             'date': '2017-11-14',
             'previous': '20',
             'change': 1},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 20,
             'week': 3,
             'date': '2017-11-14',
             'previous': '19',
             'change': -1},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 21,
             'week': 3,
             'date': '2017-11-14',
             'previous': '23',
             'change': 2},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 22,
             'week': 3,
             'date': '2017-11-14',
             'previous': '21',
             'change': -1},
            {'abbreviation': 'northwestern',
             'name': 'Northwestern',
             'rank': 23,
             'week': 3,
             'date': '2017-11-14',
             'previous': '',
             'change': 0},
            {'abbreviation': 'michigan',
             'name': 'Michigan',
             'rank': 24,
             'week': 3,
             'date': '2017-11-14',
             'previous': '',
             'change': 0},
            {'abbreviation': 'boise-state',
             'name': 'Boise State',
             'rank': 25,
             'week': 3,
             'date': '2017-11-14',
             'previous': '',
             'change': 0}],
            2: [
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 1,
             'week': 2,
             'date': '2017-10-31',
             'previous': '1',
             'change': 0},
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 2,
             'week': 2,
             'date': '2017-10-31',
             'previous': '2',
             'change': 0},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 3,
             'week': 2,
             'date': '2017-10-31',
             'previous': '3',
             'change': 0},
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 4,
             'week': 2,
             'date': '2017-10-31',
             'previous': '4',
             'change': 0},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 5,
             'week': 2,
             'date': '2017-10-31',
             'previous': '5',
             'change': 0},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 6,
             'week': 2,
             'date': '2017-10-31',
             'previous': '6',
             'change': 0},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 7,
             'week': 2,
             'date': '2017-10-31',
             'previous': '7',
             'change': 0},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 8,
             'week': 2,
             'date': '2017-10-31',
             'previous': '8',
             'change': 0},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 9,
             'week': 2,
             'date': '2017-10-31',
             'previous': '9',
             'change': 0},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 10,
             'week': 2,
             'date': '2017-10-31',
             'previous': '10',
             'change': 0},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 11,
             'week': 2,
             'date': '2017-10-31',
             'previous': '11',
             'change': 0},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 12,
             'week': 2,
             'date': '2017-10-31',
             'previous': '12',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 13,
             'week': 2,
             'date': '2017-10-31',
             'previous': '13',
             'change': 0},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 14,
             'week': 2,
             'date': '2017-10-31',
             'previous': '14',
             'change': 0},
            {'abbreviation': 'iowa-state',
             'name': 'Iowa State',
             'rank': 15,
             'week': 2,
             'date': '2017-10-31',
             'previous': '15',
             'change': 0},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 16,
             'week': 2,
             'date': '2017-10-31',
             'previous': '16',
             'change': 0},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 17,
             'week': 2,
             'date': '2017-10-31',
             'previous': '17',
             'change': 0},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 18,
             'week': 2,
             'date': '2017-10-31',
             'previous': '18',
             'change': 0},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 19,
             'week': 2,
             'date': '2017-10-31',
             'previous': '19',
             'change': 0},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 20,
             'week': 2,
             'date': '2017-10-31',
             'previous': '20',
             'change': 0},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 21,
             'week': 2,
             'date': '2017-10-31',
             'previous': '21',
             'change': 0},
            {'abbreviation': 'arizona',
             'name': 'Arizona',
             'rank': 22,
             'week': 2,
             'date': '2017-10-31',
             'previous': '22',
             'change': 0},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 23,
             'week': 2,
             'date': '2017-10-31',
             'previous': '23',
             'change': 0},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 24,
             'week': 2,
             'date': '2017-10-31',
             'previous': '24',
             'change': 0},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 25,
             'week': 2,
             'date': '2017-10-31',
             'previous': '25',
             'change': 0}],
            1: [
            {'abbreviation': 'georgia',
             'name': 'Georgia',
             'rank': 1,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'alabama',
             'name': 'Alabama',
             'rank': 2,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'notre-dame',
             'name': 'Notre Dame',
             'rank': 3,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'clemson',
             'name': 'Clemson',
             'rank': 4,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'oklahoma',
             'name': 'Oklahoma',
             'rank': 5,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'ohio-state',
             'name': 'Ohio State',
             'rank': 6,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'penn-state',
             'name': 'Penn State',
             'rank': 7,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'texas-christian',
             'name': 'Texas Christian',
             'rank': 8,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'wisconsin',
             'name': 'Wisconsin',
             'rank': 9,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'miami-fl',
             'name': 'Miami (FL)',
             'rank': 10,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'oklahoma-state',
             'name': 'Oklahoma State',
             'rank': 11,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'washington',
             'name': 'Washington',
             'rank': 12,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'virginia-tech',
             'name': 'Virginia Tech',
             'rank': 13,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'auburn',
             'name': 'Auburn',
             'rank': 14,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'iowa-state',
             'name': 'Iowa State',
             'rank': 15,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'mississippi-state',
             'name': 'Mississippi State',
             'rank': 16,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'southern-california',
             'name': 'USC',
             'rank': 17,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'central-florida',
             'name': 'UCF',
             'rank': 18,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'louisiana-state',
             'name': 'LSU',
             'rank': 19,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'north-carolina-state',
             'name': 'North Carolina State',
             'rank': 20,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'stanford',
             'name': 'Stanford',
             'rank': 21,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'arizona',
             'name': 'Arizona',
             'rank': 22,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'memphis',
             'name': 'Memphis',
             'rank': 23,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'michigan-state',
             'name': 'Michigan State',
             'rank': 24,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0},
            {'abbreviation': 'washington-state',
             'name': 'Washington State',
             'rank': 25,
             'week': 1,
             'date': '2017-10-31',
             'previous': 'NA',
             'change': 0}]
        }
        self.results_extended = results_extended
        self.results = results
        self.results_complete = results_complete

    @mock.patch('requests.get', side_effect=mock_pyquery)
    def test_rankings_integration(self, *args, **kwargs):
        flexmock(utils) \
            .should_receive('_find_year_for_season') \
            .and_return(YEAR)

        rankings = CFPRankings()

        assert rankings.current_extended == self.results_extended
        assert rankings.current == self.results
        assert rankings.complete == self.results_complete

    @mock.patch('requests.get', side_effect=mock_pyquery)
    def test_rankings_integration_bad_url(self, *args, **kwargs):
        with pytest.raises(ValueError):
            rankings = CFPRankings('BAD')

    @mock.patch('requests.get', side_effect=mock_pyquery)
    @mock.patch('requests.head', side_effect=mock_request)
    def test_invalid_default_year_reverts_to_previous_year(self,
                                                           *args,
                                                           **kwargs):
        flexmock(utils) \
            .should_receive('_find_year_for_season') \
            .and_return(2018)

        rankings = CFPRankings()

        assert rankings.current_extended == self.results_extended
        assert rankings.current == self.results
        assert rankings.complete == self.results_complete
