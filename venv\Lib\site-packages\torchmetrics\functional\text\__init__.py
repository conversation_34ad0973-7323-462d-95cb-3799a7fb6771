# Copyright The Lightning team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from torchmetrics.functional.text.bleu import bleu_score
from torchmetrics.functional.text.cer import char_error_rate
from torchmetrics.functional.text.chrf import chrf_score
from torchmetrics.functional.text.edit import edit_distance
from torchmetrics.functional.text.eed import extended_edit_distance
from torchmetrics.functional.text.mer import match_error_rate
from torchmetrics.functional.text.perplexity import perplexity
from torchmetrics.functional.text.rouge import rouge_score
from torchmetrics.functional.text.sacre_bleu import sacre_bleu_score
from torchmetrics.functional.text.squad import squad
from torchmetrics.functional.text.ter import translation_edit_rate
from torchmetrics.functional.text.wer import word_error_rate
from torchmetrics.functional.text.wil import word_information_lost
from torchmetrics.functional.text.wip import word_information_preserved
from torchmetrics.utilities.imports import _TRANSFORMERS_GREATER_EQUAL_4_4

__all__ = [
    "bleu_score",
    "char_error_rate",
    "chrf_score",
    "edit_distance",
    "extended_edit_distance",
    "match_error_rate",
    "perplexity",
    "rouge_score",
    "sacre_bleu_score",
    "squad",
    "translation_edit_rate",
    "word_error_rate",
    "word_information_lost",
    "word_information_preserved",
]


if _TRANSFORMERS_GREATER_EQUAL_4_4:
    from torchmetrics.functional.text.bert import bert_score
    from torchmetrics.functional.text.infolm import infolm

    __all__ += ["bert_score", "infolm"]
