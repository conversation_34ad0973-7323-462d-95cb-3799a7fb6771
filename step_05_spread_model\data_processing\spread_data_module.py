"""
Spread Data Module (Step 5 of 9) - Margin-Sensitive Feature Engineering
=======================================================================

This module implements the data processing pipeline for the Spread Model:
- Margin-specific feature engineering
- Integration with existing model predictions
- Market indicator processing
- Spread-specific validation

Key Components:
- SpreadDataModule: Main PyTorch Lightning data module
- MarginFeatureEngineering: Spread-specific feature creation
- MarketIndicatorProcessor: Market signal processing
- SpreadValidator: Consistency validation utilities
"""

import torch
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader, TensorDataset
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MarginFeatureEngineering:
    """
    Margin-specific feature engineering for spread predictions.
    
    Creates features that capture team differentials, situational factors,
    and market indicators relevant to spread betting.
    """
    
    def __init__(self):
        self.scalers = {}
        self.fitted = False
        
    def create_team_differentials(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create team differential features"""
        logger.info("Creating team differential features...")
        
        # Basic team differentials
        data['net_rating_diff'] = data['home_net_rating'] - data['away_net_rating']
        data['point_differential_last5'] = data['home_point_diff_l5'] - data['away_point_diff_l5']
        data['clutch_performance_diff'] = data['home_clutch_rating'] - data['away_clutch_rating']
        data['offensive_efficiency_diff'] = data['home_off_rating'] - data['away_off_rating']
        data['defensive_efficiency_diff'] = data['away_def_rating'] - data['home_def_rating']  # Lower is better
        
        # Advanced differentials
        data['pace_mismatch_index'] = np.abs(data['home_pace'] - data['away_pace']) / data['league_avg_pace']
        data['turnover_creation_diff'] = (data['home_to_force_rate'] - data['away_to_force_rate']) + \
                                       (data['away_to_rate'] - data['home_to_rate'])
        data['rebounding_advantage_diff'] = data['home_reb_rate'] - data['away_reb_rate']
        
        return data
    
    def create_injury_impacts(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create injury impact features"""
        logger.info("Creating injury impact features...")
        
        # Star player impact (higher is better for home team)
        data['star_player_impact_diff'] = data['home_star_impact'] - data['away_star_impact']
        
        # Rotation depth
        data['rotation_depth_diff'] = data['home_rotation_depth'] - data['away_rotation_depth']
        
        # Key player minutes
        data['key_player_minutes_diff'] = data['home_key_minutes'] - data['away_key_minutes']
        
        return data
    
    def create_situational_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create situational features"""
        logger.info("Creating situational features...")
        
        # Rest advantage
        data['rest_advantage_diff'] = data['home_rest_days'] - data['away_rest_days']
        
        # Travel fatigue
        data['travel_fatigue_diff'] = data['away_travel_distance'] - data['home_travel_distance']
        
        # Altitude impact (relevant for certain venues)
        data['altitude_impact_diff'] = data.get('home_altitude', 0) - data.get('away_altitude', 0)
        
        # Home court advantage
        data['home_court_advantage'] = data['home_win_pct_home'] - data['away_win_pct_away']
        
        return data
    
    def create_market_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create market indicator features"""
        logger.info("Creating market indicator features...")
        
        # Line movement volatility
        data['line_movement_volatility'] = data.get('line_movement_std', 0)
        
        # Sharp money indicator
        data['sharp_money_indicator'] = data.get('sharp_money_pct', 0.5) - 0.5
        
        # Public betting percentage
        data['public_betting_percentage'] = data.get('public_betting_pct', 0.5) - 0.5
        
        # Reverse line movement
        data['reverse_line_movement'] = ((data.get('line_movement', 0) > 0) & 
                                       (data.get('public_betting_pct', 0.5) < 0.5)).astype(int)
        
        return data
    
    def create_defensive_scheme_compatibility(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create defensive scheme compatibility features"""
        logger.info("Creating defensive scheme compatibility features...")
        
        # Defensive scheme matchup
        data['defensive_scheme_compatibility'] = (
            data.get('home_def_scheme_rating', 0) - 
            data.get('away_off_scheme_rating', 0) +
            data.get('away_def_scheme_rating', 0) -
            data.get('home_off_scheme_rating', 0)
        ) / 2
        
        return data
    
    def fit_transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Fit and transform data with margin-specific features"""
        logger.info("Starting margin feature engineering...")
        
        # Create all feature categories
        data = self.create_team_differentials(data)
        data = self.create_injury_impacts(data)
        data = self.create_situational_features(data)
        data = self.create_market_indicators(data)
        data = self.create_defensive_scheme_compatibility(data)
        
        # Define feature columns
        from ..models.spread_model import SPREAD_FEATURES
        
        # Fill missing values
        for feature in SPREAD_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Fit scalers
        if not self.fitted:
            self.scalers['margin'] = RobustScaler()
            self.scalers['margin'].fit(data[SPREAD_FEATURES])
            self.fitted = True
        
        # Transform features
        data[SPREAD_FEATURES] = self.scalers['margin'].transform(data[SPREAD_FEATURES])
        
        logger.info(f"Margin feature engineering complete. Features: {len(SPREAD_FEATURES)}")
        
        return data
    
    def transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform data with fitted scalers"""
        if not self.fitted:
            raise ValueError("Must fit scalers first")
        
        # Create features
        data = self.create_team_differentials(data)
        data = self.create_injury_impacts(data)
        data = self.create_situational_features(data)
        data = self.create_market_indicators(data)
        data = self.create_defensive_scheme_compatibility(data)
        
        # Fill missing values
        from ..models.spread_model import SPREAD_FEATURES
        for feature in SPREAD_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Transform features
        data[SPREAD_FEATURES] = self.scalers['margin'].transform(data[SPREAD_FEATURES])
        
        return data


class SpreadDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning data module for spread prediction.
    
    Handles data loading, feature engineering, and batch creation
    for the margin-sensitive spread model.
    """
    
    def __init__(
        self,
        data_path: str = "data/spread_data.parquet",
        batch_size: int = 32,
        test_size: float = 0.2,
        val_size: float = 0.2,
        num_workers: int = 4,
        pin_memory: bool = True,
        totals_model: Optional[Any] = None,
        moneyline_model: Optional[Any] = None
    ):
        super().__init__()
        self.data_path = Path(data_path)
        self.batch_size = batch_size
        self.test_size = test_size
        self.val_size = val_size
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        self.totals_model = totals_model
        self.moneyline_model = moneyline_model
        
        # Data components
        self.data = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        
        # Feature engineering
        self.feature_engineer = MarginFeatureEngineering()
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
    def prepare_data(self):
        """Prepare data (download, process, etc.)"""
        logger.info("Preparing spread data...")
        
        # Create sample data if file doesn't exist
        if not self.data_path.exists():
            logger.warning("No data file found, creating sample data...")
            self.create_sample_data()
        
    def create_sample_data(self):
        """Create sample data for demonstration"""
        logger.info("Creating sample spread data...")
        
        np.random.seed(42)
        n_samples = 1000
        
        # Create sample game data
        data = {
            # Team ratings
            'home_net_rating': np.random.normal(0, 5, n_samples),
            'away_net_rating': np.random.normal(0, 5, n_samples),
            'home_point_diff_l5': np.random.normal(0, 8, n_samples),
            'away_point_diff_l5': np.random.normal(0, 8, n_samples),
            'home_clutch_rating': np.random.normal(0, 3, n_samples),
            'away_clutch_rating': np.random.normal(0, 3, n_samples),
            'home_off_rating': np.random.normal(105, 8, n_samples),
            'away_off_rating': np.random.normal(105, 8, n_samples),
            'home_def_rating': np.random.normal(105, 8, n_samples),
            'away_def_rating': np.random.normal(105, 8, n_samples),
            
            # Pace and style
            'home_pace': np.random.normal(75, 5, n_samples),
            'away_pace': np.random.normal(75, 5, n_samples),
            'league_avg_pace': np.full(n_samples, 75),
            'home_to_force_rate': np.random.normal(0.15, 0.03, n_samples),
            'away_to_force_rate': np.random.normal(0.15, 0.03, n_samples),
            'home_to_rate': np.random.normal(0.15, 0.03, n_samples),
            'away_to_rate': np.random.normal(0.15, 0.03, n_samples),
            'home_reb_rate': np.random.normal(0.5, 0.05, n_samples),
            'away_reb_rate': np.random.normal(0.5, 0.05, n_samples),
            
            # Injury and depth
            'home_star_impact': np.random.normal(0, 3, n_samples),
            'away_star_impact': np.random.normal(0, 3, n_samples),
            'home_rotation_depth': np.random.normal(8, 1, n_samples),
            'away_rotation_depth': np.random.normal(8, 1, n_samples),
            'home_key_minutes': np.random.normal(120, 15, n_samples),
            'away_key_minutes': np.random.normal(120, 15, n_samples),
            
            # Situational
            'home_rest_days': np.random.randint(0, 4, n_samples),
            'away_rest_days': np.random.randint(0, 4, n_samples),
            'home_travel_distance': np.random.exponential(200, n_samples),
            'away_travel_distance': np.random.exponential(400, n_samples),
            'home_win_pct_home': np.random.uniform(0.3, 0.8, n_samples),
            'away_win_pct_away': np.random.uniform(0.3, 0.8, n_samples),
            
            # Market indicators
            'line_movement_std': np.random.exponential(1, n_samples),
            'sharp_money_pct': np.random.uniform(0.3, 0.7, n_samples),
            'public_betting_pct': np.random.uniform(0.2, 0.8, n_samples),
            'line_movement': np.random.normal(0, 1, n_samples),
        }
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Create target variable (spread)
        net_rating_diff = df['home_net_rating'] - df['away_net_rating']
        situational_adj = (df['home_rest_days'] - df['away_rest_days']) * 0.5
        home_court_adj = 3.0  # Home court advantage
        
        df['spread'] = net_rating_diff * 0.8 + situational_adj + home_court_adj + \
                      np.random.normal(0, 2, n_samples)
        
        # Save data
        self.data_path.parent.mkdir(parents=True, exist_ok=True)
        df.to_parquet(self.data_path)
        
        logger.info(f"Sample spread data created: {self.data_path}")
        
    def setup(self, stage: Optional[str] = None):
        """Setup data for training, validation, and testing"""
        logger.info("Setting up spread data module...")
        
        # Load data
        self.data = pd.read_parquet(self.data_path)
        logger.info(f"Loaded data shape: {self.data.shape}")
        
        # Feature engineering
        self.data = self.feature_engineer.fit_transform(self.data)
        
        # Split data
        train_val_data, self.test_data = train_test_split(
            self.data, test_size=self.test_size, random_state=42
        )
        
        self.train_data, self.val_data = train_test_split(
            train_val_data, test_size=self.val_size, random_state=42
        )
        
        logger.info(f"Data splits - Train: {len(self.train_data)}, "
                   f"Val: {len(self.val_data)}, Test: {len(self.test_data)}")
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = self._create_dataset(self.train_data)
            self.val_dataset = self._create_dataset(self.val_data)
            
        if stage == "test" or stage is None:
            self.test_dataset = self._create_dataset(self.test_data)
    
    def _create_dataset(self, data: pd.DataFrame) -> TensorDataset:
        """Create tensor dataset from DataFrame"""
        from ..models.spread_model import SPREAD_FEATURES
        
        # Get features
        margin_features = torch.tensor(
            data[SPREAD_FEATURES].values, 
            dtype=torch.float32
        )
        
        # Create feature dictionary
        game_features = {
            'margin_metrics': margin_features
        }
        
        # Add base model features if available
        if self.totals_model is not None:
            # Create dummy totals features for demo
            totals_features = torch.randn(len(data), 20)  # Placeholder
            game_features['totals_features'] = totals_features
        
        if self.moneyline_model is not None:
            # Create dummy moneyline features for demo
            moneyline_features = torch.randn(len(data), 18)  # Placeholder
            game_features['moneyline_features'] = moneyline_features
        
        # Target
        target = torch.tensor(data['spread'].values, dtype=torch.float32)
        
        return TensorDataset(margin_features, target)
    
    def _collate_fn(self, batch):
        """Custom collate function for handling feature dictionaries"""
        # Extract features and targets
        features = [item[0] for item in batch]
        targets = [item[1] for item in batch]
        
        # Stack tensors
        margin_features = torch.stack(features)
        targets = torch.stack(targets)
        
        # Create game features dictionary
        game_features = {
            'margin_metrics': margin_features
        }
        
        # Add base model features if available
        if self.totals_model is not None:
            batch_size = len(batch)
            totals_features = torch.randn(batch_size, 20)  # Placeholder
            game_features['totals_features'] = totals_features
        
        if self.moneyline_model is not None:
            batch_size = len(batch)
            moneyline_features = torch.randn(batch_size, 18)  # Placeholder
            game_features['moneyline_features'] = moneyline_features
        
        return game_features, targets
    
    def train_dataloader(self) -> DataLoader:
        """Create training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def val_dataloader(self) -> DataLoader:
        """Create validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def test_dataloader(self) -> DataLoader:
        """Create test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def predict_dataloader(self) -> DataLoader:
        """Create prediction dataloader"""
        return self.test_dataloader()
