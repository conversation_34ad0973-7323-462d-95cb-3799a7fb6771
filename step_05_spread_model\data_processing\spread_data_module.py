"""
Spread Data Module (Step 5 of 9) - Margin-Sensitive Feature Engineering
=======================================================================

This module implements the data processing pipeline for the Spread Model:
- Integration with unified data from consolidated_wnba structure
- Margin-specific feature engineering
- Integration with existing model predictions from steps 1-3
- Market indicator processing
- Spread-specific validation

Key Components:
- SpreadDataModule: Main PyTorch Lightning data module
- MarginFeatureEngineering: Spread-specific feature creation
- Unified data integration with steps 1-3
"""

import torch
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader, TensorDataset
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

# Add parent directories to path for imports
import sys
step_01_path = str(Path(__file__).parent.parent.parent / "step_01_player_points_model")
step_02_path = str(Path(__file__).parent.parent.parent / "step_02_game_totals_model")
sys.path.insert(0, step_01_path)
sys.path.insert(0, step_02_path)

# Define spread-specific features
SPREAD_FEATURES = [
    # Team differential metrics
    'net_rating_diff',
    'point_differential_last5',
    'clutch_performance_diff',
    'off_rating_diff',
    'def_rating_diff',
    
    # Injury impacts
    'star_player_impact_diff',
    'rotation_depth_diff',
    'key_minutes_diff',
    
    # Matchup-specific
    'pace_mismatch_index',
    'turnover_creation_diff',
    'rebound_rate_diff',
    'to_force_rate_diff',
    
    # Situational
    'rest_advantage_diff',
    'travel_fatigue_diff',
    'home_court_advantage',
    'home_win_pct_diff',
    
    # Market indicators
    'line_movement_volatility',
    'sharp_money_indicator',
    'public_betting_pct',
    'line_movement'
]


class MarginFeatureEngineering:
    """
    Margin-specific feature engineering for spread predictions.
    
    Creates features that capture team differentials, situational factors,
    and market indicators relevant to spread betting.
    """
    
    def __init__(self):
        self.scalers = {}
        self.fitted = False
        
    def create_team_differentials(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create team differential features"""
        logger.info("Creating team differential features...")
        
        # Basic team differentials
        data['net_rating_diff'] = data['home_net_rating'] - data['away_net_rating']
        data['point_differential_last5'] = data['home_point_diff_l5'] - data['away_point_diff_l5']
        data['clutch_performance_diff'] = data['home_clutch_rating'] - data['away_clutch_rating']
        data['off_rating_diff'] = data['home_off_rating'] - data['away_off_rating']
        data['def_rating_diff'] = data['away_def_rating'] - data['home_def_rating']  # Lower is better
        
        # Pace and style differentials
        data['pace_mismatch_index'] = abs(data['home_pace'] - data['away_pace'])
        data['turnover_creation_diff'] = data['home_to_force_rate'] - data['away_to_force_rate']
        data['rebound_rate_diff'] = data['home_reb_rate'] - data['away_reb_rate']
        data['to_force_rate_diff'] = data['home_to_force_rate'] - data['away_to_force_rate']
        
        return data
        
        # Advanced differentials
        data['pace_mismatch_index'] = np.abs(data['home_pace'] - data['away_pace']) / data['league_avg_pace']
        data['turnover_creation_diff'] = (data['home_to_force_rate'] - data['away_to_force_rate']) + \
                                       (data['away_to_rate'] - data['home_to_rate'])
        data['rebounding_advantage_diff'] = data['home_reb_rate'] - data['away_reb_rate']
        
        return data
    
    def create_injury_impacts(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create injury impact features"""
        logger.info("Creating injury impact features...")
        
        # Star player impact (higher is better for home team)
        data['star_player_impact_diff'] = data['home_star_impact'] - data['away_star_impact']
        
        # Rotation depth
        data['rotation_depth_diff'] = data['home_rotation_depth'] - data['away_rotation_depth']
        
        # Key player minutes
        data['key_player_minutes_diff'] = data['home_key_minutes'] - data['away_key_minutes']
        
        return data
    
    def create_situational_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create situational features"""
        logger.info("Creating situational features...")
        
        # Rest advantage
        data['rest_advantage_diff'] = data['home_rest_days'] - data['away_rest_days']
        
        # Travel fatigue
        data['travel_fatigue_diff'] = data['away_travel_distance'] - data['home_travel_distance']
        
        # Altitude impact (relevant for certain venues)
        data['altitude_impact_diff'] = data.get('home_altitude', 0) - data.get('away_altitude', 0)
        
        # Home court advantage
        data['home_court_advantage'] = data['home_win_pct_home'] - data['away_win_pct_away']
        
        return data
    
    def create_market_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create market indicator features"""
        logger.info("Creating market indicator features...")
        
        # Line movement volatility
        data['line_movement_volatility'] = data.get('line_movement_std', 0)
        
        # Sharp money indicator
        data['sharp_money_indicator'] = data.get('sharp_money_pct', 0.5) - 0.5
        
        # Public betting percentage
        data['public_betting_percentage'] = data.get('public_betting_pct', 0.5) - 0.5
        
        # Reverse line movement
        data['reverse_line_movement'] = ((data.get('line_movement', 0) > 0) & 
                                       (data.get('public_betting_pct', 0.5) < 0.5)).astype(int)
        
        return data
    
    def create_defensive_scheme_compatibility(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create defensive scheme compatibility features"""
        logger.info("Creating defensive scheme compatibility features...")
        
        # Defensive scheme matchup
        data['defensive_scheme_compatibility'] = (
            data.get('home_def_scheme_rating', 0) - 
            data.get('away_off_scheme_rating', 0) +
            data.get('away_def_scheme_rating', 0) -
            data.get('home_off_scheme_rating', 0)
        ) / 2
        
        return data
    
    def fit_transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Fit and transform data with margin-specific features"""
        logger.info("Starting margin feature engineering...")
        
        # Create all feature categories
        data = self.create_team_differentials(data)
        data = self.create_injury_impacts(data)
        data = self.create_situational_features(data)
        data = self.create_market_indicators(data)
        data = self.create_defensive_scheme_compatibility(data)
        
        # Fill missing values
        for feature in SPREAD_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Fit scalers
        if not self.fitted:
            self.scalers['margin'] = RobustScaler()
            self.scalers['margin'].fit(data[SPREAD_FEATURES])
            self.fitted = True
        
        # Transform features
        data[SPREAD_FEATURES] = self.scalers['margin'].transform(data[SPREAD_FEATURES])
        
        logger.info(f"Margin feature engineering complete. Features: {len(SPREAD_FEATURES)}")
        
        return data
    
    def transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform data with fitted scalers"""
        if not self.fitted:
            raise ValueError("Must fit scalers first")
        
        # Create features
        data = self.create_team_differentials(data)
        data = self.create_injury_impacts(data)
        data = self.create_situational_features(data)
        data = self.create_market_indicators(data)
        data = self.create_defensive_scheme_compatibility(data)
        
        # Fill missing values
        from ..models.spread_model import SPREAD_FEATURES
        for feature in SPREAD_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Transform features
        data[SPREAD_FEATURES] = self.scalers['margin'].transform(data[SPREAD_FEATURES])
        
        return data


class SpreadDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning data module for spread prediction.
    
    Integrates with unified data from consolidated_wnba structure
    and creates margin-specific features for spread prediction.
    """
    
    def __init__(
        self,
        data_dir: str = None,
        batch_size: int = 32,
        num_workers: int = 0,
        val_split_date: str = '2024-01-01',
        test_split_date: str = '2025-01-01',
        use_unified_data: bool = True,
        totals_model: Optional[Any] = None,
        moneyline_model: Optional[Any] = None,
        seed: int = 42
    ):
        super().__init__()
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.val_split_date = val_split_date
        self.test_split_date = test_split_date
        self.use_unified_data = use_unified_data
        self.totals_model = totals_model
        self.moneyline_model = moneyline_model
        self.seed = seed
        
        # Set up unified data paths
        if use_unified_data:
            base_path = Path(__file__).parent.parent.parent / "consolidated_wnba"
            self.game_data_path = base_path / "03_game_data" / "schedules"
            self.team_data_path = base_path / "02_team_data" / "basic_stats"
            self.player_data_path = base_path / "01_player_data" / "basic_stats"
            self.training_data_path = base_path / "04_training_data" / "game_outcomes"
            logger.info(f"🔗 Using unified data from: {base_path}")
        else:
            self.data_dir = data_dir
            logger.info(f"📊 Using custom data dir: {data_dir}")
        
        # Data components
        self.data = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        
        # Feature engineering
        self.feature_engineer = MarginFeatureEngineering()
        self.scaler = StandardScaler()
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
    def prepare_data(self):
        """Prepare data (download, process, etc.)"""
        logger.info("Preparing spread data from unified structure...")
        
        if self.use_unified_data:
            # Use unified data structure
            self._prepare_unified_data()
        else:
            # Create demo data for testing
            self._prepare_demo_data()
            
    def _prepare_unified_data(self):
        """Load and prepare data from unified consolidated_wnba structure"""
        logger.info("Loading unified data for spread prediction...")
        
        try:
            # Load game outcomes and schedules
            game_files = list(self.training_data_path.glob("*.csv"))
            schedule_files = list(self.game_data_path.glob("*.csv"))
            
            if not game_files:
                logger.warning("No game outcome files found, creating demo data")
                self._prepare_demo_data()
                return
                
            # Load game data
            game_data = []
            for file in game_files:
                df = pd.read_csv(file)
                game_data.append(df)
            
            if game_data:
                self.data = pd.concat(game_data, ignore_index=True)
                logger.info(f"Loaded {len(self.data)} games from unified data")
            else:
                logger.warning("No valid game data found, creating demo data")
                self._prepare_demo_data()
                
        except Exception as e:
            logger.error(f"Error loading unified data: {e}")
            logger.info("Falling back to demo data")
            self._prepare_demo_data()
            
    # Demo data generation removed - using real unified data only
        
        # Check if unified data exists
        if not self.data_path.exists():
            raise FileNotFoundError(
                f"Unified data file not found: {self.data_path}. "
                f"Please ensure unified data is properly structured."
            )

    # Sample data creation method removed - using real unified data only
        
    # Demo data creation method removed - using real unified data only
        
    def setup(self, stage: Optional[str] = None):
        """Setup data for training, validation, and testing"""
        logger.info("Setting up spread data module...")
        
        # Feature engineering
        self.data = self.feature_engineer.fit_transform(self.data)
        
        # Time-based splits consistent with steps 1-3
        if 'date' in self.data.columns:
            self.data['date'] = pd.to_datetime(self.data['date'])
            
            # Split by date
            train_mask = self.data['date'] < pd.to_datetime(self.val_split_date)
            val_mask = (self.data['date'] >= pd.to_datetime(self.val_split_date)) & \
                      (self.data['date'] < pd.to_datetime(self.test_split_date))
            test_mask = self.data['date'] >= pd.to_datetime(self.test_split_date)
            
            self.train_data = self.data[train_mask].reset_index(drop=True)
            self.val_data = self.data[val_mask].reset_index(drop=True)
            self.test_data = self.data[test_mask].reset_index(drop=True)
        else:
            # Fallback to random splits
            train_val_data, self.test_data = train_test_split(
                self.data, test_size=0.2, random_state=self.seed
            )
            self.train_data, self.val_data = train_test_split(
                train_val_data, test_size=0.2, random_state=self.seed
            )
        
        logger.info(f"Data splits - Train: {len(self.train_data)}, "
                   f"Val: {len(self.val_data)}, Test: {len(self.test_data)}")
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = self._create_dataset(self.train_data)
            self.val_dataset = self._create_dataset(self.val_data)
            
        if stage == "test" or stage is None:
            self.test_dataset = self._create_dataset(self.test_data)
    
    def _create_dataset(self, data: pd.DataFrame) -> TensorDataset:
        """Create tensor dataset from DataFrame"""
        # Get features
        margin_features = torch.tensor(
            data[SPREAD_FEATURES].values, 
            dtype=torch.float32
        )
        
        # Create feature dictionary
        game_features = {
            'margin_metrics': margin_features
        }
        
        # Add base model features if available
        if self.totals_model is not None:
            # Create dummy totals features for demo
            totals_features = torch.randn(len(data), 20)  # Placeholder
            game_features['totals_features'] = totals_features
        
        if self.moneyline_model is not None:
            # Create dummy moneyline features for demo
            moneyline_features = torch.randn(len(data), 18)  # Placeholder
            game_features['moneyline_features'] = moneyline_features
        
        # Target
        target = torch.tensor(data['spread'].values, dtype=torch.float32)
        
        return TensorDataset(margin_features, target)
    
    def _collate_fn(self, batch):
        """Custom collate function for handling feature dictionaries"""
        # Extract features and targets
        features = [item[0] for item in batch]
        targets = [item[1] for item in batch]
        
        # Stack tensors
        margin_features = torch.stack(features)
        targets = torch.stack(targets)
        
        # Create game features dictionary
        game_features = {
            'margin_metrics': margin_features
        }
        
        # Add base model features if available
        if self.totals_model is not None:
            batch_size = len(batch)
            totals_features = torch.randn(batch_size, 20)  # Placeholder
            game_features['totals_features'] = totals_features
        
        if self.moneyline_model is not None:
            batch_size = len(batch)
            moneyline_features = torch.randn(batch_size, 18)  # Placeholder
            game_features['moneyline_features'] = moneyline_features
        
        return game_features, targets
    
    def train_dataloader(self) -> DataLoader:
        """Create training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def val_dataloader(self) -> DataLoader:
        """Create validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def test_dataloader(self) -> DataLoader:
        """Create test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def predict_dataloader(self) -> DataLoader:
        """Create prediction dataloader"""
        return self.test_dataloader()
    
    def get_input_dim(self) -> int:
        """Get input dimension for the model"""
        return len(SPREAD_FEATURES)
        
    def get_feature_names(self) -> List[str]:
        """Get feature names"""
        return SPREAD_FEATURES
