"""
🔗 Test Hierarchical Integration between Step 1 and Step 2

This script tests the hierarchical architecture where Step 2 (Game Totals Model) 
uses Step 1's (Player Points Model) predictions as additional features.
"""

import os
import sys
import torch
import pandas as pd
from pathlib import Path

# Add the current directory to the path to import Step 2 modules
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import Step 2 components
sys.path.insert(0, str(current_dir / "data_processing"))
sys.path.insert(0, str(current_dir / "models"))

from game_totals_data_module import GameTotalsDataModule
from game_totals_model import GameTotalsModel

def test_hierarchical_integration():
    """Test the hierarchical integration between Step 1 and Step 2"""
    
    print("🧪 Testing Hierarchical Integration")
    print("=" * 50)
    
    # Paths
    current_dir = Path(__file__).parent
    workspace_dir = current_dir.parent
    
    # Game data path
    game_data_path = current_dir / "data" / "comprehensive_wnba_game_totals.csv"
    
    # Player data path (for hierarchical features)
    player_data_path = workspace_dir / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features_with_metadata.csv"
    
    # Step 1 model checkpoint (use the best one)
    step1_checkpoint = workspace_dir / "step_01_player_points_model" / "checkpoints" / "standard-player-points-epoch=18-val_loss=0.43.ckpt"
    
    print(f"📂 Game data path: {game_data_path}")
    print(f"📂 Player data path: {player_data_path}")
    print(f"📂 Step 1 checkpoint: {step1_checkpoint}")
    
    # Check if files exist
    if not game_data_path.exists():
        print(f"❌ Game data not found: {game_data_path}")
        return False
    
    if not player_data_path.exists():
        print(f"❌ Player data not found: {player_data_path}")
        return False
    
    if not step1_checkpoint.exists():
        print(f"❌ Step 1 checkpoint not found: {step1_checkpoint}")
        return False
    
    print("✅ All required files found")
    
    # Test 1: Standard (non-hierarchical) mode
    print("\n🔧 Test 1: Standard Mode (No Hierarchical Features)")
    try:
        standard_data_module = GameTotalsDataModule(
            data_path=str(game_data_path),
            enable_hierarchical=False,
            batch_size=16
        )
        standard_data_module.setup()
        
        # Get feature count
        standard_feature_count = len(standard_data_module.feature_columns)
        print(f"✅ Standard mode: {standard_feature_count} features")
        
        # Test dataloader
        train_loader = standard_data_module.train_dataloader()
        batch = next(iter(train_loader))
        print(f"✅ Standard batch shape: {batch[0].shape}")
        
    except Exception as e:
        print(f"❌ Standard mode failed: {e}")
        return False
    
    # Test 2: Hierarchical mode (with synthetic features)
    print("\n🔗 Test 2: Hierarchical Mode (Synthetic Features)")
    try:
        hierarchical_data_module = GameTotalsDataModule(
            data_path=str(game_data_path),
            enable_hierarchical=True,
            player_model_path=str(step1_checkpoint),
            player_data_path=str(player_data_path),
            batch_size=16
        )
        hierarchical_data_module.setup()
        
        # Get feature count
        hierarchical_feature_count = len(hierarchical_data_module.feature_columns)
        print(f"✅ Hierarchical mode: {hierarchical_feature_count} features")
        
        # Test dataloader
        train_loader = hierarchical_data_module.train_dataloader()
        batch = next(iter(train_loader))
        print(f"✅ Hierarchical batch shape: {batch[0].shape}")
        
        # Check that hierarchical mode has more features
        if hierarchical_feature_count > standard_feature_count:
            print(f"✅ Hierarchical mode added {hierarchical_feature_count - standard_feature_count} features")
        else:
            print(f"⚠️  Hierarchical mode didn't add features ({hierarchical_feature_count} vs {standard_feature_count})")
        
    except Exception as e:
        print(f"❌ Hierarchical mode failed: {e}")
        return False
    
    # Test 3: Model compatibility
    print("\n🏗️  Test 3: Model Compatibility")
    try:
        # Create models for both modes
        standard_model = GameTotalsModel(input_dim=standard_feature_count)
        hierarchical_model = GameTotalsModel(input_dim=hierarchical_feature_count)
        
        # Test forward pass
        standard_output = standard_model(batch[0][:, :standard_feature_count])
        hierarchical_output = hierarchical_model(batch[0])
        
        print(f"✅ Standard model output shape: {standard_output.shape}")
        print(f"✅ Hierarchical model output shape: {hierarchical_output.shape}")
        
    except Exception as e:
        print(f"❌ Model compatibility test failed: {e}")
        return False
    
    # Test 4: Feature analysis
    print("\n📊 Test 4: Feature Analysis")
    try:
        print("Standard features:")
        for i, feature in enumerate(standard_data_module.feature_columns[:10]):
            print(f"  {i+1}. {feature}")
        if len(standard_data_module.feature_columns) > 10:
            print(f"  ... and {len(standard_data_module.feature_columns) - 10} more")
        
        print("\nHierarchical features (additional):")
        hierarchical_only = [f for f in hierarchical_data_module.feature_columns 
                           if f not in standard_data_module.feature_columns]
        for i, feature in enumerate(hierarchical_only):
            print(f"  {i+1}. {feature}")
        
    except Exception as e:
        print(f"❌ Feature analysis failed: {e}")
        return False
    
    print("\n🎉 All tests passed!")
    print("=" * 50)
    print("✅ Hierarchical integration is working correctly")
    print(f"✅ Standard mode: {standard_feature_count} features")
    print(f"✅ Hierarchical mode: {hierarchical_feature_count} features")
    print(f"✅ Added hierarchical features: {hierarchical_feature_count - standard_feature_count}")
    
    return True

if __name__ == "__main__":
    success = test_hierarchical_integration()
    sys.exit(0 if success else 1)
