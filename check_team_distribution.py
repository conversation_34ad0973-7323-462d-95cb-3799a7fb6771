"""
🔍 Team Distribution Checker for WNBA Models

This script checks that all 13 WNBA teams are properly represented
across the time-based splits in both Step 1 and Step 2 models.
"""

import sys
import os
from pathlib import Path
import pandas as pd

def check_step1_teams():
    """Check team distribution in Step 1 Player Points model"""
    
    print("🏀 Step 1 - Player Points Model Team Distribution")
    print("=" * 60)
    
    step1_path = Path(__file__).parent.parent / "step_01_player_points_model"
    sys.path.append(str(step1_path / "data_processing"))
    
    try:
        from wnba_data_module import WNBADataModule
        
        data_path = step1_path.parent / "consolidated_wnba" / "01_player_data" / "basic_stats" / "complete_real_wnba_features_with_metadata.csv"
        
        if not data_path.exists():
            print(f"❌ Data file not found: {data_path}")
            return False
        
        # Load data module
        dm = WNBADataModule(str(data_path))
        dm.prepare_data()
        dm.setup()
        
        print(f"📊 Total teams in dataset: {dm.data['team_abbreviation'].nunique()}")
        print(f"🏆 All teams: {sorted(dm.data['team_abbreviation'].unique())}")
        print()
        
        # Check splits
        train_data, val_data, test_data = dm._create_time_splits()
        
        all_teams = set(dm.data['team_abbreviation'].unique())
        train_teams = set(train_data['team_abbreviation'].unique())
        val_teams = set(val_data['team_abbreviation'].unique())
        test_teams = set(test_data['team_abbreviation'].unique())
        
        print("🔍 Team Coverage Analysis:")
        print(f"  Total unique teams: {len(all_teams)}")
        print(f"  Train teams: {len(train_teams)}")
        print(f"  Val teams: {len(val_teams)}")
        print(f"  Test teams: {len(test_teams)}")
        print()
        
        # Check for missing teams
        missing_train = all_teams - train_teams
        missing_val = all_teams - val_teams
        missing_test = all_teams - test_teams
        
        if missing_train:
            print(f"❌ Missing from train: {sorted(missing_train)}")
        if missing_val:
            print(f"❌ Missing from val: {sorted(missing_val)}")
        if missing_test:
            print(f"❌ Missing from test: {sorted(missing_test)}")
        
        if not (missing_train or missing_val or missing_test):
            print("✅ All teams present in all splits!")
            return True
        else:
            print("❌ Some teams missing from splits")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Step 1: {e}")
        return False

def check_step2_teams():
    """Check team distribution in Step 2 Game Totals model"""
    
    print("\n🎯 Step 2 - Game Totals Model Team Distribution")
    print("=" * 60)
    
    step2_path = Path(__file__).parent.parent / "step_02_game_totals_model"
    sys.path.append(str(step2_path / "data_processing"))
    
    try:
        from game_totals_data_module import GameTotalsDataModule
        
        # Use real WNBA data instead of sample data
        real_data_path = step2_path / "data" / "real_wnba_game_totals.csv"
        
        if not real_data_path.exists():
            print("📝 Creating real WNBA game data from boxscores...")
            sys.path.append(str(step2_path / "utils"))
            from create_real_game_data import process_all_wnba_games
            os.makedirs(real_data_path.parent, exist_ok=True)
            consolidated_path = Path(__file__).parent / "consolidated_wnba"
            process_all_wnba_games(consolidated_path, str(real_data_path))
        
        # Load data module
        dm = GameTotalsDataModule(str(real_data_path))
        dm.prepare_data()
        dm.setup()
        
        print(f"📊 Total teams in dataset: {dm.data['team_id'].nunique()}")
        print(f"🏆 Team IDs: {sorted(dm.data['team_id'].unique())}")
        print()
        
        # Check splits
        train_data, val_data, test_data = dm._create_time_splits()
        
        all_teams = set(dm.data['team_id'].unique())
        train_teams = set(train_data['team_id'].unique())
        val_teams = set(val_data['team_id'].unique())
        test_teams = set(test_data['team_id'].unique())
        
        print("🔍 Team Coverage Analysis:")
        print(f"  Total unique teams: {len(all_teams)}")
        print(f"  Train teams: {len(train_teams)}")
        print(f"  Val teams: {len(val_teams)}")
        print(f"  Test teams: {len(test_teams)}")
        print()
        
        # Check for missing teams
        missing_train = all_teams - train_teams
        missing_val = all_teams - val_teams
        missing_test = all_teams - test_teams
        
        if missing_train:
            print(f"❌ Missing from train: {sorted(missing_train)}")
        if missing_val:
            print(f"❌ Missing from val: {sorted(missing_val)}")
        if missing_test:
            print(f"❌ Missing from test: {sorted(missing_test)}")
        
        if not (missing_train or missing_val or missing_test):
            print("✅ All teams present in all splits!")
            return True
        else:
            print("❌ Some teams missing from splits")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Step 2: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to check both models"""
    
    print("🔍 WNBA Team Distribution Analysis")
    print("=" * 80)
    print("Checking that all 13 WNBA teams are represented in both models...")
    print()
    
    # Check Step 1
    step1_ok = check_step1_teams()
    
    # Check Step 2
    step2_ok = check_step2_teams()
    
    # Summary
    print("\n📋 Summary")
    print("=" * 30)
    if step1_ok and step2_ok:
        print("✅ All models have complete team coverage!")
        print("🎯 Both Step 1 and Step 2 models are ready for hierarchical integration.")
    else:
        print("❌ Some models have incomplete team coverage.")
        print("🔧 Please review the time splits and ensure all teams are represented.")
    
    print("\n📝 WNBA Teams (2020-2025):")
    expected_teams = [
        'ATL', 'CHI', 'CONN', 'DAL', 'IND', 'LAS', 'LV', 'MIN',
        'NY', 'PHX', 'SEA', 'WAS', 'GS'  # Golden State added as 13th team
    ]
    print(f"Expected 13 teams: {expected_teams}")

if __name__ == "__main__":
    main()
