"""
🏀 WNBA Game Totals Model - Step 2 of Hierarchical Pipeline

This model predicts total points scored in WNBA games by aggregating individual player
predictions from the Player Points Model (Step 1) and combining them with team-level features.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, Any, Optional
import numpy as np


class GameTotalsModel(pl.LightningModule):
    """
    🏀 Game Totals Prediction Model - Hierarchical Architecture
    
    Combines:
    1. Player-level predictions from Step 1 model (frozen)
    2. Team-level efficiency metrics  
    3. Game context features (rest, travel, pace)
    4. Market calibration layer
    """
    
    def __init__(
        self,
        input_dim: int = 28,
        hidden_dims: list = [256, 128, 64],
        dropout: float = 0.2,
        learning_rate: float = 1e-3,
        player_points_model: Optional[pl.LightningModule] = None
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['player_points_model'])
        
        # Store the trained player model (frozen for inference)
        self.player_model = player_points_model
        if self.player_model is not None:
            self.player_model.freeze()
            print("🔒 Player Points Model frozen for hierarchical inference")
        
        # Build neural network layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, 1))
        
        self.team_net = nn.Sequential(*layers)
        
        # Market calibration layer for real-world adjustment
        self.market_calibration = nn.Sequential(
            nn.Linear(3, 8),  # [model_pred, pace_factor, efficiency_diff]
            nn.Tanh(),
            nn.Linear(8, 1)
        )
        
        # Robust loss function for game-level variance
        self.loss_fn = nn.HuberLoss(delta=2.0)
        
    def forward(self, team_features: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for game totals prediction
        
        Args:
            team_features: Team-level features [batch_size, input_dim]
            
        Returns:
            Predicted game total points [batch_size]
        """
        # Main team network prediction
        raw_prediction = self.team_net(team_features).squeeze()
        
        # Market calibration (if we have sufficient features)
        if team_features.size(1) >= 3:
            # Extract calibration features from team data
            pace_factor = team_features[:, -2:-1] if team_features.size(1) > 2 else torch.ones_like(raw_prediction).unsqueeze(1)
            efficiency_diff = team_features[:, -1:] if team_features.size(1) > 1 else torch.zeros_like(raw_prediction).unsqueeze(1)
            
            calibration_input = torch.cat([
                raw_prediction.unsqueeze(1),
                pace_factor,
                efficiency_diff
            ], dim=1)
            
            calibration_adjustment = self.market_calibration(calibration_input).squeeze()
            final_prediction = raw_prediction + calibration_adjustment
        else:
            final_prediction = raw_prediction
        
        return final_prediction
    
    def training_step(self, batch, batch_idx):
        """Training step with hierarchical loss"""
        features, targets, _, _, _ = batch
        predictions = self(features)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_mae', mae, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        features, targets, _, _, _ = batch
        predictions = self(features)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_mae', mae, prog_bar=True)
        
        return {'val_loss': loss, 'val_mae': mae}
    
    def test_step(self, batch, batch_idx):
        """Test step with comprehensive metrics"""
        features, targets, _, _, _ = batch
        predictions = self(features)
        
        mae = F.l1_loss(predictions, targets)
        mse = F.mse_loss(predictions, targets)
        rmse = torch.sqrt(mse)
        
        # Additional game totals metrics
        within_5 = (torch.abs(predictions - targets) <= 5).float().mean()
        within_10 = (torch.abs(predictions - targets) <= 10).float().mean()
        
        self.log('test_mae', mae)
        self.log('test_rmse', rmse)
        self.log('test_within_5pts', within_5)
        self.log('test_within_10pts', within_10)
        
        return {
            'test_mae': mae, 
            'test_rmse': rmse,
            'within_5': within_5,
            'within_10': within_10
        }
    
    def configure_optimizers(self):
        """Advanced optimizer configuration"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.7,
            patience=8,
            min_lr=1e-6
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }


class MarketCalibration(nn.Module):
    """
    🎯 Market Calibration Layer for Real-World Adjustment
    """
    
    def __init__(self, input_dim=3):
        super().__init__()
        self.adjustment = nn.Sequential(
            nn.Linear(input_dim, 16),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(16, 8),
            nn.Tanh(),
            nn.Linear(8, 1)
        )
    
    def forward(self, model_pred, market_data):
        """Apply market calibration to model predictions"""
        combined_input = torch.cat([model_pred.unsqueeze(1), market_data], dim=1)
        adjustment = self.adjustment(combined_input)
        return model_pred + adjustment.squeeze()
