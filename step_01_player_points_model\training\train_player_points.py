"""
🎯 Training Script for WNBA Player Points Model

This script implements the complete training pipeline using real WNBA data
from the consolidated folder with proper validation and monitoring.
"""

import os
import sys
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, Model<PERSON>heckpoint, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
import argparse
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from models.player_points_model import PlayerPointsModel, PoissonPlayerPointsModel
from data_processing.wnba_data_module import WNBADataModule
from validation.consistency_checks import validate_player_consistency, plot_validation_results


def train_player_points_model(
    data_path: str,
    model_type: str = 'standard',
    max_epochs: int = 200,
    batch_size: int = 64,
    learning_rate: float = 1e-3,
    dropout: float = 0.3,
    early_stopping_patience: int = 15,
    checkpoint_dir: str = 'checkpoints',
    log_dir: str = 'logs'
):
    """
    Train the WNBA Player Points Model following the exact build plan
    
    Performance Benchmarks:
    - Target MAE: < 2.5 points for rotation players (min. 15 MPG)
    - Poisson NLL: < 1.8 for low-minute players
    - Feature importance alignment with basketball knowledge
    
    Args:
        data_path: Path to the WNBA data file
        model_type: 'standard' or 'poisson'
        max_epochs: Maximum training epochs (build plan: 200)
        batch_size: Training batch size
        learning_rate: Learning rate
        dropout: Dropout rate
        early_stopping_patience: Early stopping patience (build plan: 15)
        checkpoint_dir: Directory to save model checkpoints
        log_dir: Directory for tensorboard logs
    """
    
    # Set up directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    print("🏀 WNBA Player Points Model Training")
    print("=" * 50)
    print(f"Data path: {data_path}")
    print(f"Model type: {model_type}")
    print(f"Max epochs: {max_epochs}")
    print(f"Batch size: {batch_size}")
    print(f"Learning rate: {learning_rate}")
    print(f"Dropout: {dropout}")
    
    # Initialize data module
    print("\n📊 Setting up data module...")
    data_module = WNBADataModule(
        data_path=data_path,
        batch_size=batch_size,
        num_workers=0,  # Set to 0 for Windows compatibility
        seq_len=15
    )
    
    # Setup data
    data_module.setup()
    
    # Get input dimension from data
    sample_batch = next(iter(data_module.train_dataloader()))
    input_dim = sample_batch[0].shape[1]
    print(f"Input dimension: {input_dim}")
    
    # Initialize model
    print(f"\n🤖 Initializing {model_type} model...")
    if model_type == 'standard':
        model = PlayerPointsModel(
            input_dim=input_dim,
            dropout=dropout,
            learning_rate=learning_rate
        )
    elif model_type == 'poisson':
        model = PoissonPlayerPointsModel(
            input_dim=input_dim,
            dropout=dropout,
            learning_rate=learning_rate
        )
    else:
        raise ValueError(f"Unknown model type: {model_type}")
    
    # Set up callbacks
    callbacks = [
        # Early stopping
        EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            mode='min',
            verbose=True
        ),
        
        # Model checkpointing
        ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename=f'{model_type}-player-points-{{epoch:02d}}-{{val_loss:.2f}}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            verbose=True
        ),
        
        # Learning rate monitoring
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # Set up logger
    logger = TensorBoardLogger(log_dir, name=f'{model_type}_player_points')
    
    # Initialize trainer following exact build plan specifications
    trainer = pl.Trainer(
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        precision=16 if torch.cuda.is_available() else 32,  # Build plan: precision=16
        callbacks=callbacks,
        max_epochs=max_epochs,  # Build plan: max_epochs=200
        gradient_clip_val=0.5,  # Build plan: gradient_clip_val=0.5
        logger=logger,
        deterministic=True,
        enable_progress_bar=True,
        log_every_n_steps=5  # Adjusted for small datasets
    )
    
    print(f"\n🚀 Starting training...")
    print(f"Device: {trainer.accelerator}")
    
    # Train the model
    trainer.fit(model, data_module)
    
    # Load best model for testing
    best_model_path = trainer.checkpoint_callback.best_model_path
    print(f"\n📈 Loading best model from: {best_model_path}")
    
    if model_type == 'standard':
        best_model = PlayerPointsModel.load_from_checkpoint(best_model_path)
    else:
        best_model = PoissonPlayerPointsModel.load_from_checkpoint(best_model_path)
    
    # Test the model
    print("\n🧪 Running final test...")
    test_results = trainer.test(best_model, data_module)
    
    # Run validation checks
    print("\n🔍 Running validation checks...")
    validation_results = validate_player_consistency(
        best_model, 
        data_module.val_dataloader(),
        device=trainer.accelerator if trainer.accelerator != 'cpu' else 'cpu'
    )
    
    # Print validation report
    print("\n" + validation_results['validation_report'])
    
    # Save validation plots
    plot_path = os.path.join(checkpoint_dir, f'{model_type}_validation_plots.png')
    plot_validation_results(validation_results, save_path=plot_path)
    print(f"\n📊 Validation plots saved to: {plot_path}")
    
    # Save final metrics
    final_metrics = {
        'test_results': test_results,
        'validation_results': validation_results,
        'best_model_path': best_model_path,
        'model_type': model_type,
        'hyperparameters': {
            'input_dim': input_dim,
            'dropout': dropout,
            'learning_rate': learning_rate,
            'batch_size': batch_size,
            'max_epochs': max_epochs
        }
    }
    
    return final_metrics


def aggregate_to_team(predictions_df):
    """
    Sum player points to team totals for integration with other models
    
    Args:
        predictions_df: DataFrame with columns ['game_id', 'team_id', 'pred_points']
    
    Returns:
        DataFrame with team-level predictions
    """
    team_preds = predictions_df.groupby(['game_id', 'team_id'])['pred_points'].sum()
    return team_preds.reset_index()


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train WNBA Player Points Model')
    
    parser.add_argument('--data_path', type=str, required=True,
                       help='Path to WNBA data file')
    parser.add_argument('--model_type', type=str, default='standard',
                       choices=['standard', 'poisson'],
                       help='Type of model to train')
    parser.add_argument('--max_epochs', type=int, default=200,
                       help='Maximum training epochs')
    parser.add_argument('--batch_size', type=int, default=64,
                       help='Training batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--dropout', type=float, default=0.3,
                       help='Dropout rate')
    parser.add_argument('--early_stopping_patience', type=int, default=15,
                       help='Early stopping patience')
    parser.add_argument('--checkpoint_dir', type=str, default='checkpoints',
                       help='Directory to save checkpoints')
    parser.add_argument('--log_dir', type=str, default='logs',
                       help='Directory for logs')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    pl.seed_everything(42)
    
    # Train the model
    results = train_player_points_model(
        data_path=args.data_path,
        model_type=args.model_type,
        max_epochs=args.max_epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        dropout=args.dropout,
        early_stopping_patience=args.early_stopping_patience,
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir
    )
    
    print("\n✅ Training completed successfully!")
    print(f"Best model saved at: {results['best_model_path']}")
    
    # Print final performance summary
    test_mae = results['test_results'][0].get('test_mae', 'N/A')
    val_mae = results['validation_results']['player_metrics']['player_mae']
    team_mae = results['validation_results']['team_metrics']['team_mae_mean']
    
    print(f"\n📊 Final Performance Summary:")
    print(f"Test MAE: {test_mae}")
    print(f"Validation Player MAE: {val_mae:.2f}")
    print(f"Validation Team MAE: {team_mae:.2f}")
    
    # Check if model meets benchmarks
    meets_benchmarks = val_mae < 2.5 and team_mae < 8.0
    print(f"\nBenchmark Status: {'✅ PASS' if meets_benchmarks else '❌ FAIL'}")


if __name__ == '__main__':
    main()
