"""
Assists Model Training (Step 6 of 9) - Playmaking Model Training
================================================================

This module provides training utilities for the assists prediction model:
- Training configuration and setup
- Model training with PyTorch Lightning
- Checkpoint management
- Training metrics tracking

Key Components:
- AssistsTrainer: Main training class
- AssistsTrainingConfig: Training configuration
- train_assists_model: Main training function
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import logging
from pathlib import Path
import argparse
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Import project modules
import sys
sys.path.append(str(Path(__file__).parent.parent))

from models.assists_model import AssistsModel
from data_processing.assists_data_module import AssistsDataModule
from utils.assists_metrics import evaluate_assists_model
from validation.assists_validation import validate_assists_model

logger = logging.getLogger(__name__)


@dataclass
class AssistsTrainingConfig:
    """Configuration for assists model training"""
    
    # Model parameters
    input_dim: int = 64
    hidden_dim: int = 128
    teammate_dim: int = 16
    dropout_rate: float = 0.2
    
    # Training parameters
    learning_rate: float = 0.001
    batch_size: int = 32
    max_epochs: int = 100
    patience: int = 15
    
    # Data parameters
    train_split: float = 0.7
    val_split: float = 0.15
    test_split: float = 0.15
    
    # Paths
    data_path: Optional[str] = None
    checkpoint_dir: str = "step_06_checkpoints"
    log_dir: str = "step_06_logs"
    
    # Hardware
    accelerator: str = "auto"
    devices: int = 1
    
    # Validation
    val_check_interval: float = 1.0
    monitor_metric: str = "val_loss"
    mode: str = "min"


class AssistsTrainer:
    """
    Main training class for assists model.
    
    Handles training setup, execution, and evaluation.
    """
    
    def __init__(self, config: AssistsTrainingConfig):
        self.config = config
        self.model = None
        self.data_module = None
        self.trainer = None
        
    def setup_model(self) -> AssistsModel:
        """Setup the assists model"""
        
        logger.info("Setting up assists model...")
        
        self.model = AssistsModel(
            input_dim=self.config.input_dim,
            hidden_dim=self.config.hidden_dim,
            teammate_dim=self.config.teammate_dim,
            dropout=self.config.dropout_rate,
            learning_rate=self.config.learning_rate
        )
        
        return self.model
    
    def setup_data_module(self) -> AssistsDataModule:
        """Setup the data module"""
        
        logger.info("Setting up data module...")
        
        self.data_module = AssistsDataModule(
            data_path=self.config.data_path,
            batch_size=self.config.batch_size,
            test_size=self.config.test_split,
            val_size=self.config.val_split
        )
        
        return self.data_module
    
    def setup_trainer(self) -> Trainer:
        """Setup PyTorch Lightning trainer"""
        
        logger.info("Setting up trainer...")
        
        # Create directories
        checkpoint_dir = Path(self.config.checkpoint_dir)
        checkpoint_dir.mkdir(exist_ok=True)
        
        log_dir = Path(self.config.log_dir)
        log_dir.mkdir(exist_ok=True)
        
        # Callbacks
        callbacks = [
            ModelCheckpoint(
                dirpath=checkpoint_dir,
                filename="assists-model-{epoch:02d}-{val_loss:.2f}",
                monitor=self.config.monitor_metric,
                mode=self.config.mode,
                save_top_k=3,
                save_last=True
            ),
            EarlyStopping(
                monitor=self.config.monitor_metric,
                patience=self.config.patience,
                mode=self.config.mode,
                verbose=True
            )
        ]
        
        # Logger
        logger_tb = TensorBoardLogger(
            save_dir=log_dir,
            name="assists_model"
        )
        
        # Trainer
        self.trainer = Trainer(
            max_epochs=self.config.max_epochs,
            callbacks=callbacks,
            logger=logger_tb,
            accelerator=self.config.accelerator,
            devices=self.config.devices,
            val_check_interval=self.config.val_check_interval,
            log_every_n_steps=10
        )
        
        return self.trainer
    
    def train(self) -> Dict[str, Any]:
        """Train the assists model"""
        
        logger.info("Starting assists model training...")
        
        # Setup components
        if self.model is None:
            self.setup_model()
        if self.data_module is None:
            self.setup_data_module()
        if self.trainer is None:
            self.setup_trainer()
        
        # Prepare data
        self.data_module.prepare_data()
        self.data_module.setup()
        
        # Train model
        self.trainer.fit(self.model, self.data_module)
        
        # Training results
        training_results = {
            'best_val_loss': self.trainer.callback_metrics.get('val_loss', float('inf')),
            'training_complete': True,
            'checkpoint_path': self.trainer.checkpoint_callback.best_model_path
        }
        
        logger.info(f"Training completed. Best validation loss: {training_results['best_val_loss']:.4f}")
        
        return training_results
    
    def evaluate(self) -> Dict[str, Any]:
        """Evaluate the trained model"""
        
        logger.info("Evaluating trained model...")
        
        if self.model is None or self.data_module is None:
            raise ValueError("Model and data module must be set up before evaluation")
        
        # Test the model
        test_results = self.trainer.test(self.model, self.data_module)
        
        # Get predictions for detailed evaluation
        self.model.eval()
        with torch.no_grad():
            test_loader = self.data_module.test_dataloader()
            all_predictions = []
            all_targets = []
            all_features = []
            
            for batch in test_loader:
                if len(batch) == 2:
                    x, y = batch
                    features_dict = {}
                else:
                    x, y, features_dict = batch
                
                predictions = self.model(x)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(y.cpu().numpy())
                
                # Extract features for evaluation
                if isinstance(features_dict, dict):
                    for key, value in features_dict.items():
                        if key not in all_features:
                            all_features.append({key: []})
                        all_features[-1][key].extend(value.cpu().numpy())
        
        # Convert to numpy arrays
        predictions = np.array(all_predictions)
        targets = np.array(all_targets)
        
        # Combine features
        features = {}
        if all_features:
            for key in all_features[0].keys():
                features[key] = np.concatenate([batch[key] for batch in all_features])
        
        # Comprehensive evaluation
        evaluation_results = evaluate_assists_model(targets, predictions, features)
        
        # Validation
        validation_results = validate_assists_model(predictions, features)
        
        # Combine results
        final_results = {
            'test_results': test_results,
            'evaluation_results': evaluation_results,
            'validation_results': validation_results,
            'model_path': self.trainer.checkpoint_callback.best_model_path
        }
        
        logger.info("Evaluation completed.")
        
        return final_results
    
    def load_best_model(self) -> AssistsModel:
        """Load the best model from checkpoint"""
        
        checkpoint_path = self.trainer.checkpoint_callback.best_model_path
        
        if checkpoint_path and Path(checkpoint_path).exists():
            logger.info(f"Loading best model from {checkpoint_path}")
            self.model = AssistsModel.load_from_checkpoint(checkpoint_path)
        else:
            logger.warning("No checkpoint found, using current model")
        
        return self.model


def train_assists_model(config: AssistsTrainingConfig) -> Dict[str, Any]:
    """
    Main training function for assists model.
    
    Args:
        config: Training configuration
        
    Returns:
        Dictionary containing training and evaluation results
    """
    
    # Initialize trainer
    trainer = AssistsTrainer(config)
    
    # Train model
    training_results = trainer.train()
    
    # Evaluate model
    evaluation_results = trainer.evaluate()
    
    # Combine results
    results = {
        'training_results': training_results,
        'evaluation_results': evaluation_results,
        'config': config
    }
    
    return results


def main():
    """Main training script"""
    
    parser = argparse.ArgumentParser(description="Train Assists Model")
    
    # Model parameters
    parser.add_argument('--input_dim', type=int, default=64, help='Input dimension')
    parser.add_argument('--hidden_dim', type=int, default=128, help='Hidden dimension')
    parser.add_argument('--teammate_dim', type=int, default=16, help='Teammate dimension')
    parser.add_argument('--dropout_rate', type=float, default=0.2, help='Dropout rate')
    
    # Training parameters
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--max_epochs', type=int, default=100, help='Maximum epochs')
    parser.add_argument('--patience', type=int, default=15, help='Early stopping patience')
    
    # Data parameters
    parser.add_argument('--data_path', type=str, default=None, help='Data path')
    parser.add_argument('--train_split', type=float, default=0.7, help='Training split')
    parser.add_argument('--val_split', type=float, default=0.15, help='Validation split')
    parser.add_argument('--test_split', type=float, default=0.15, help='Test split')
    
    # Paths
    parser.add_argument('--checkpoint_dir', type=str, default='step_06_checkpoints', help='Checkpoint directory')
    parser.add_argument('--log_dir', type=str, default='step_06_logs', help='Log directory')
    
    args = parser.parse_args()
    
    # Create configuration
    config = AssistsTrainingConfig(
        input_dim=args.input_dim,
        hidden_dim=args.hidden_dim,
        teammate_dim=args.teammate_dim,
        dropout_rate=args.dropout_rate,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        max_epochs=args.max_epochs,
        patience=args.patience,
        data_path=args.data_path,
        train_split=args.train_split,
        val_split=args.val_split,
        test_split=args.test_split,
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir
    )
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Train model
    results = train_assists_model(config)
    
    # Print results
    print("\\n=== Assists Model Training Results ===")
    print(f"Training completed: {results['training_results']['training_complete']}")
    print(f"Best validation loss: {results['training_results']['best_val_loss']:.4f}")
    print(f"Model checkpoint: {results['training_results']['checkpoint_path']}")
    
    # Print evaluation metrics
    eval_results = results['evaluation_results']['evaluation_results']
    if 'core_metrics' in eval_results:
        core_metrics = eval_results['core_metrics']
        print(f"\\nCore Metrics:")
        print(f"  RMSE: {core_metrics['rmse']:.4f}")
        print(f"  MAE: {core_metrics['mae']:.4f}")
        print(f"  R²: {core_metrics['r2']:.4f}")
        print(f"  Assists Accuracy: {core_metrics['assists_accuracy']:.4f}")
    
    # Print validation results
    val_results = results['evaluation_results']['validation_results']
    if 'core_validation' in val_results:
        core_val = val_results['core_validation']
        print(f"\\nValidation Results:")
        print(f"  Overall Score: {core_val['overall_score']:.4f}")
        print(f"  Range Compliance: {core_val['range_validation']['range_compliance']:.4f}")


if __name__ == "__main__":
    main()
