"""
🏀 WNBA Moneyline Model

This module implements the hierarchical moneyline prediction model that integrates
with the Game Totals Model to predict game winners with market-calibrated probabilities.
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl
from sklearn.linear_model import LogisticRegression
from sklearn.calibration import calibration_curve
from sklearn.metrics import log_loss, brier_score_loss, roc_auc_score
import numpy as np
from typing import Dict, Tuple, Optional, List
import pandas as pd


class MoneylineModel(pl.LightningModule):
    """
    🎯 Hierarchical Moneyline Prediction Model
    
    Architecture:
    - Leverages frozen Game Totals Model predictions
    - Separate offensive/defensive feature extraction
    - Market-calibrated probability outputs
    - Dynamic home court advantage weighting
    """
    
    def __init__(
        self,
        totals_model: pl.LightningModule,
        input_dim: int = 32,
        hidden_dim: int = 192,
        learning_rate: float = 1e-3,
        home_advantage_weight: float = 1.2,
        dropout_rate: float = 0.25
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['totals_model'])
        
        # Frozen totals model for hierarchical features
        self.totals_model = totals_model
        self.totals_model.freeze()
        
        # Feature extraction branches
        self.offense_net = nn.Sequential(
            nn.Linear(12, 64),
            nn.ELU(),
            nn.LayerNorm(64),
            nn.Dropout(dropout_rate)
        )
        
        self.defense_net = nn.Sequential(
            nn.Linear(12, 64),
            nn.ELU(),
            nn.LayerNorm(64),
            nn.Dropout(dropout_rate)
        )
        
        # Main prediction network
        self.net = nn.Sequential(
            nn.Linear(input_dim + 128, hidden_dim),  # 128 from feature branches
            nn.ELU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ELU(),
            nn.LayerNorm(hidden_dim//2),
            nn.Dropout(dropout_rate/2),
            
            nn.Linear(hidden_dim//2, 1)
        )
        
        # Market calibration parameters
        self.calibration_layer = nn.Parameter(torch.tensor([1.0, 0.0]))  # Scale and bias
        
        # Loss function with home advantage weighting
        self.loss_fn = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([home_advantage_weight]))
        
        # Metrics tracking
        self.train_acc = []
        self.val_acc = []
        self.train_losses = []
        self.val_losses = []

    def forward(self, team_features: Dict[str, torch.Tensor], player_features: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the moneyline model
        
        Args:
            team_features: Dictionary containing team-level features
            player_features: Player-level features for totals model
            
        Returns:
            Market-calibrated probability of home team winning
        """
        # Get game total prediction from hierarchical model
        with torch.no_grad():
            total_pred = self.totals_model(team_features.get('totals_features', team_features), player_features)
            if total_pred.dim() == 1:
                total_pred = total_pred.unsqueeze(1)
        
        # Offense/defense feature extraction
        home_off = self.offense_net(team_features['home_offense'])
        away_off = self.offense_net(team_features['away_offense'])
        home_def = self.defense_net(team_features['home_defense'])
        away_def = self.defense_net(team_features['away_defense'])
        
        # Create matchup comparison features
        off_advantage = home_off - away_def
        def_advantage = home_def - away_off
        momentum_gap = team_features['home_momentum'] - team_features['away_momentum']
        
        # Concatenate all features
        main_features = torch.cat([
            total_pred,
            off_advantage, 
            def_advantage,
            momentum_gap,
            team_features['contextual']
        ], dim=1)
        
        # Raw logit output
        raw_logit = self.net(main_features)
        
        # Market-calibrated output
        return raw_logit * self.calibration_layer[0] + self.calibration_layer[1]

    def training_step(self, batch, batch_idx):
        """Training step"""
        team_features, player_features, target = batch
        
        # Forward pass
        logits = self(team_features, player_features)
        loss = self.loss_fn(logits.squeeze(), target.float())
        
        # Calculate accuracy
        probs = torch.sigmoid(logits.squeeze())
        predictions = (probs > 0.5).float()
        accuracy = (predictions == target).float().mean()
        
        # Logging
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_acc', accuracy, on_step=True, on_epoch=True, prog_bar=True)
        
        self.train_losses.append(loss.item())
        self.train_acc.append(accuracy.item())
        
        return loss

    def validation_step(self, batch, batch_idx):
        """Validation step"""
        team_features, player_features, target = batch
        
        # Forward pass
        logits = self(team_features, player_features)
        loss = self.loss_fn(logits.squeeze(), target.float())
        
        # Calculate metrics
        probs = torch.sigmoid(logits.squeeze())
        predictions = (probs > 0.5).float()
        accuracy = (predictions == target).float().mean()
        
        # Logging
        self.log('val_loss', loss, on_epoch=True, prog_bar=True)
        self.log('val_acc', accuracy, on_epoch=True, prog_bar=True)
        
        self.val_losses.append(loss.item())
        self.val_acc.append(accuracy.item())
        
        return {
            'val_loss': loss,
            'val_acc': accuracy,
            'predictions': probs,
            'targets': target
        }

    def test_step(self, batch, batch_idx):
        """Test step"""
        team_features, player_features, target = batch
        
        # Forward pass
        logits = self(team_features, player_features)
        probs = torch.sigmoid(logits.squeeze())
        predictions = (probs > 0.5).float()
        
        # Calculate metrics
        accuracy = (predictions == target).float().mean()
        
        return {
            'test_acc': accuracy,
            'predictions': probs,
            'targets': target,
            'logits': logits.squeeze()
        }

    def configure_optimizers(self):
        """Configure optimizer"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=1e-5
        )
        
        # Learning rate scheduler
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }

    def calibrate_probabilities(self, val_loader, use_time_decay: bool = True):
        """
        Calibrate model probabilities using Platt scaling with optional time decay
        
        Args:
            val_loader: Validation data loader
            use_time_decay: Whether to weight recent games more heavily
        """
        logits, labels, dates = [], [], []
        self.eval()
        
        with torch.no_grad():
            for batch in val_loader:
                team_features, player_features, target = batch
                output = self(team_features, player_features)
                logits.append(output.squeeze())
                labels.append(target)
                
                # Extract dates if available
                if 'dates' in team_features:
                    dates.append(team_features['dates'])
                else:
                    dates.append(torch.zeros_like(target))  # Placeholder
        
        logits = torch.cat(logits).cpu().numpy()
        labels = torch.cat(labels).cpu().numpy()
        dates = torch.cat(dates).cpu().numpy()
        
        # Time-decayed calibration weights
        if use_time_decay and dates.max() > 0:
            # Exponential decay with recent games weighted higher
            weights = np.exp(-0.02 * (2024 - dates))
            weights = weights / weights.sum()  # Normalize
        else:
            weights = None
        
        # Fit calibration model
        lr = LogisticRegression()
        lr.fit(logits.reshape(-1, 1), labels, sample_weight=weights)
        
        # Update calibration parameters
        self.calibration_layer.data[0] = torch.tensor(lr.coef_[0][0], dtype=torch.float32)
        self.calibration_layer.data[1] = torch.tensor(lr.intercept_[0], dtype=torch.float32)
        
        print(f"✅ Calibration updated: scale={lr.coef_[0][0]:.3f}, bias={lr.intercept_[0]:.3f}")


class MomentumEnsemble(nn.Module):
    """
    🚀 Momentum-Weighted Ensemble Model
    
    Combines full-season metrics with recent form for adaptive predictions
    """
    
    def __init__(self, full_season_model: nn.Module, recent_form_model: nn.Module):
        super().__init__()
        self.full_model = full_season_model
        self.recent_model = recent_form_model
        
        # Learnable ensemble weighting
        self.alpha = nn.Parameter(torch.tensor(0.7))  # Start with 70% full season, 30% recent
        
    def forward(self, x):
        full_out = self.full_model(x)
        recent_out = self.recent_model(x)
        
        # Weighted ensemble
        alpha_clamped = torch.clamp(self.alpha, 0.0, 1.0)
        return alpha_clamped * full_out + (1 - alpha_clamped) * recent_out


class ClutchPerformanceModule(nn.Module):
    """
    🎯 Clutch Performance Analysis Module
    
    Evaluates team performance in close game situations
    """
    
    def __init__(self, input_dim: int = 8):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, 16),
            nn.ELU(),
            nn.Dropout(0.2),
            nn.Linear(16, 4),
            nn.ELU(),
            nn.Linear(4, 1)
        )
        
    def forward(self, clutch_stats: torch.Tensor):
        """
        Args:
            clutch_stats: [last5_minutes_eff, clutch_to_rate, ft_clutch_pct, 
                          close_game_wins, late_game_comebacks, pressure_fg_pct,
                          clutch_assists, clutch_rebounds]
        """
        return self.net(clutch_stats)


def calculate_hca_factor(team_id: str, season: int, home_games: pd.DataFrame, away_games: pd.DataFrame) -> float:
    """
    Calculate dynamic home court advantage factor for a specific team
    
    Args:
        team_id: Team identifier
        season: Season year
        home_games: Home game statistics
        away_games: Away game statistics
        
    Returns:
        Home court advantage factor (0-1 scale)
    """
    # Calculate win percentage difference
    home_win_pct = home_games['win'].mean() if len(home_games) > 0 else 0.5
    away_win_pct = away_games['win'].mean() if len(away_games) > 0 else 0.5
    
    # Scale the advantage
    hca = (home_win_pct - away_win_pct) * 0.5
    
    # Apply sigmoid to constrain to 0-1 range
    return torch.sigmoid(torch.tensor(hca)).item()


def generate_moneyline_report(y_true: np.ndarray, y_pred: np.ndarray, market_odds: Optional[np.ndarray] = None) -> Dict:
    """
    Generate comprehensive moneyline model validation report
    
    Args:
        y_true: True binary outcomes (1 for home win, 0 for away win)
        y_pred: Predicted probabilities
        market_odds: Market odds for comparison (optional)
        
    Returns:
        Dictionary containing validation metrics
    """
    # Core metrics
    metrics = {
        'Log Loss': log_loss(y_true, y_pred),
        'Brier Score': brier_score_loss(y_true, y_pred),
        'AUC': roc_auc_score(y_true, y_pred),
        'Accuracy': np.mean((y_pred > 0.5) == y_true),
        'Calibration Error': 0.0,  # Will be calculated below
        'Win Rate by Decile': []
    }
    
    # Calibration error
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y_true, y_pred, n_bins=10, strategy='uniform'
    )
    calibration_error = np.abs(fraction_of_positives - mean_predicted_value).mean()
    metrics['Calibration Error'] = calibration_error
    
    # Market edge (if market odds provided)
    if market_odds is not None:
        market_probs = 1 / market_odds  # Convert odds to implied probabilities
        market_edge = np.mean(np.where(y_true == 1, market_probs, 1 - market_probs) - y_pred)
        metrics['Market Edge'] = market_edge
    
    # Decile analysis
    deciles = np.percentile(y_pred, np.arange(0, 101, 10))
    for i in range(10):
        mask = (y_pred >= deciles[i]) & (y_pred < deciles[i+1])
        if mask.sum() > 0:
            actual_win_rate = y_true[mask].mean()
            predicted_win_rate = y_pred[mask].mean()
            metrics['Win Rate by Decile'].append({
                'range': f"{deciles[i]:.2f}-{deciles[i+1]:.2f}",
                'actual': actual_win_rate,
                'predicted': predicted_win_rate,
                'count': mask.sum()
            })
    
    return metrics


# Moneyline feature configuration
MONEYLINE_FEATURES = {
    # Offensive metrics (12 features)
    'home_offense': [
        'ortg_last5', 'efg_pct_last10', 'to_rate_ema10',
        'ft_rate_ema5', 'assist_rate_rolling', 'second_chance_eff',
        'clutch_ortg', 'transition_eff', 'paint_pts_pct',
        'three_rate', 'off_reb_rate', 'pace_adjusted_eff'
    ],
    
    # Defensive metrics (12 features)
    'home_defense': [
        'drtg_last5', 'opp_efg_pct_last10', 'steal_rate_ema10',
        'block_rate_ema5', 'def_reb_rate', 'rim_protection_eff',
        'clutch_drtg', 'transition_def', 'foul_rate',
        'opp_three_rate', 'deflections_last3', 'defensive_load'
    ],
    
    # Contextual features (8 features)
    'contextual': [
        'home_win_pct', 'away_win_pct', 'rest_advantage',
        'travel_disadvantage', 'altitude_factor', 'b2b_penalty',
        'coaching_win_pct_diff', 'roster_consistency'
    ]
}
