"""
🚀 Demo Training Script for WNBA Game Totals Model

This script demonstrates how to train the Game Totals model using the same
time-based splits as the Player Points model for hierarchical consistency.
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from training.train_game_totals import train_game_totals_model, create_sample_game_data


def quick_data_check(data_path: str):
    """Quick check of the game data structure"""
    
    print("🔍 Quick Game Data Structure Check")
    print("=" * 40)
    
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        return False
    
    # Load and examine data
    df = pd.read_csv(data_path)
    
    print(f"📊 Data Shape: {df.shape}")
    print(f"📋 Columns ({len(df.columns)}):")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i:2d}. {col}")
    
    # Check target variable
    target_col = 'team_total_points'
    if target_col in df.columns:
        print(f"🎯 Target Variable ({target_col}):")
        print(f"  Mean: {df[target_col].mean():.2f}")
        print(f"  Std: {df[target_col].std():.2f}")
        print(f"  Min: {df[target_col].min():.2f}")
        print(f"  Max: {df[target_col].max():.2f}")
        print(f"  Missing: {df[target_col].isnull().sum()}")
    else:
        print(f"❌ Target variable '{target_col}' not found")
        return False
    
    # Show sample data
    print(f"📈 Sample Data:")
    print(df.head())
    
    # Show data types
    print(f"🔢 Data Types:")
    print(df.dtypes.value_counts())
    
    return True


def run_demo_training():
    """Run a demo training session with the Game Totals data"""
    
    print("🏀 WNBA Game Totals Model - Demo Training")
    print("=" * 60)
    
    # Use unified game data first, fall back to comprehensive data
    unified_data_dir = Path(__file__).parent.parent / "unified_data"
    unified_data_path = unified_data_dir / "unified_game_data.csv"
    
    if unified_data_path.exists():
        data_path = unified_data_path
        print(f"📁 Using unified WNBA game data: {data_path}")
    else:
        print("⚠️  Unified game data not found, using comprehensive data")
        # Use comprehensive WNBA data from all games files
        base_path = Path(__file__).parent
        data_dir = base_path / "data"
        data_path = data_dir / "comprehensive_wnba_game_totals.csv"
        
        # Create comprehensive WNBA data if it doesn't exist
        if not data_path.exists():
            print("📝 Creating comprehensive WNBA game data...")
            os.makedirs(data_dir, exist_ok=True)
            
            # Import and run the comprehensive data creation script
            sys.path.append(str(base_path / "utils"))
            from create_comprehensive_game_data import main as create_comprehensive_data
            
            create_comprehensive_data()
        
        print(f"📁 Using comprehensive WNBA data file: {data_path}")
    
    # Quick data check
    if not quick_data_check(str(data_path)):
        print("❌ Data check failed. Exiting.")
        return
    
    # Find the best Step 1 model checkpoint for hierarchical features
    step1_checkpoint_dir = Path(__file__).parent.parent / "step_01_player_points_model" / "checkpoints"
    best_player_model = None
    
    if step1_checkpoint_dir.exists():
        # Find the checkpoint with the lowest validation loss
        checkpoints = list(step1_checkpoint_dir.glob("*.ckpt"))
        if checkpoints:
            # Sort by validation loss (extract from filename)
            def extract_val_loss(ckpt_path):
                try:
                    name = ckpt_path.stem
                    if "val_loss=" in name:
                        loss_str = name.split("val_loss=")[1].split("-")[0]
                        return float(loss_str)
                    return float('inf')
                except:
                    return float('inf')
            
            best_checkpoint = min(checkpoints, key=extract_val_loss)
            best_player_model = str(best_checkpoint)
            print(f"🔗 Found best Step 1 model: {best_checkpoint.name}")
            print(f"   Validation loss: {extract_val_loss(best_checkpoint):.4f}")
    
    # Set up training parameters
    training_params = {
        'model_type': 'hierarchical' if best_player_model else 'standard',
        'max_epochs': 30,  # Reduced for demo
        'batch_size': 16,  # Smaller batch size for demo
        'learning_rate': 1e-3,
        'dropout': 0.3,
        'hidden_dims': [128, 64, 32],  # Smaller network for demo
        'early_stopping_patience': 8,
        'checkpoint_dir': 'step_02_checkpoints',
        'log_dir': 'step_02_logs',
        'player_model_path': best_player_model
    }
    
    print(f"\n🎯 Training Parameters:")
    for key, value in training_params.items():
        print(f"  {key}: {value}")
    
    print(f"\n🚀 Starting Game Totals Model Training...")
    print("=" * 60)
    
    # Train the model
    try:
        model, trainer, results = train_game_totals_model(
            data_path=str(data_path),
            **training_params
        )
        
        print("\n✅ Demo Training Completed Successfully!")
        print("=" * 60)
        print(f"📊 Final Test Results:")
        for key, value in results[0].items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
        
        print(f"\n💾 Model saved to: {trainer.checkpoint_callback.best_model_path}")
        print(f"📈 TensorBoard logs: {training_params['log_dir']}")
        
        # Show next steps
        print(f"\n🔄 Next Steps:")
        print(f"  1. Check TensorBoard logs: tensorboard --logdir {training_params['log_dir']}")
        print(f"  2. Use saved model for predictions")
        print(f"  3. Integrate with Player Points model for hierarchical predictions")
        print(f"  4. Tune hyperparameters based on validation results")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🎉 Demo Complete!")


def show_model_comparison():
    """Show comparison between standard and hierarchical models"""
    
    print("\n🔬 Model Architecture Comparison")
    print("=" * 50)
    print("📊 Standard Game Totals Model:")
    print("  - Uses only game-level features")
    print("  - Team context (embeddings)")
    print("  - Opponent matchup analysis")
    print("  - Rolling team performance metrics")
    print("  - Best for: Independent game predictions")
    
    print("\n🔗 Hierarchical Game Totals Model:")
    print("  - Includes all standard features")
    print("  - Integrates player predictions from Step 1")
    print("  - Enhanced prediction accuracy")
    print("  - Consistent cross-level modeling")
    print("  - Best for: Complete pipeline predictions")
    
    print("\n📈 Expected Benefits of Hierarchical Approach:")
    print("  - Better accuracy through player-level information")
    print("  - Consistent splits across modeling levels")
    print("  - Enhanced interpretability")
    print("  - Robust predictions for new scenarios")


if __name__ == "__main__":
    # Show model comparison
    show_model_comparison()
    
    # Run demo training
    run_demo_training()
