"""
🚀 Demo Training Script for WNBA Game Totals Model

This script demonstrates how to train the Game Totals model using the same
time-based splits as the Player Points model for hierarchical consistency.
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from training.train_game_totals import train_game_totals_model, create_sample_game_data


def quick_data_check(data_path: str):
    """Quick check of the game data structure"""
    
    print("🔍 Quick Game Data Structure Check")
    print("=" * 40)
    
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        return False
    
    # Load and examine data
    df = pd.read_csv(data_path)
    
    print(f"📊 Data Shape: {df.shape}")
    print(f"📋 Columns ({len(df.columns)}):")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i:2d}. {col}")
    
    # Check target variable
    target_col = 'team_total_points'
    if target_col in df.columns:
        print(f"🎯 Target Variable ({target_col}):")
        print(f"  Mean: {df[target_col].mean():.2f}")
        print(f"  Std: {df[target_col].std():.2f}")
        print(f"  Min: {df[target_col].min():.2f}")
        print(f"  Max: {df[target_col].max():.2f}")
        print(f"  Missing: {df[target_col].isnull().sum()}")
    else:
        print(f"❌ Target variable '{target_col}' not found")
        return False
    
    # Show sample data
    print(f"📈 Sample Data:")
    print(df.head())
    
    # Show data types
    print(f"🔢 Data Types:")
    print(df.dtypes.value_counts())
    
    return True


def run_demo_training():
    """Run a demo training session with the Game Totals data"""
    
    print("🏀 WNBA Game Totals Model - Demo Training")
    print("=" * 60)
    
    # Path for sample data
    base_path = Path(__file__).parent
    sample_data_dir = base_path / "sample_data"
    data_path = sample_data_dir / "game_totals_sample.csv"
    
    # Create sample data if it doesn't exist
    if not data_path.exists():
        print("📝 Creating sample game data...")
        os.makedirs(sample_data_dir, exist_ok=True)
        create_sample_game_data(str(data_path))
    
    print(f"📁 Using data file: {data_path}")
    
    # Quick data check
    if not quick_data_check(str(data_path)):
        print("❌ Data check failed. Exiting.")
        return
    
    # Set up training parameters
    training_params = {
        'model_type': 'standard',  # Start with standard model
        'max_epochs': 30,  # Reduced for demo
        'batch_size': 16,  # Smaller batch size for demo
        'learning_rate': 1e-3,
        'dropout': 0.3,
        'hidden_dims': [128, 64, 32],  # Smaller network for demo
        'early_stopping_patience': 8,
        'checkpoint_dir': 'step_02_checkpoints',
        'log_dir': 'step_02_logs'
    }
    
    print(f"\n🎯 Training Parameters:")
    for key, value in training_params.items():
        print(f"  {key}: {value}")
    
    print(f"\n🚀 Starting Game Totals Model Training...")
    print("=" * 60)
    
    # Train the model
    try:
        model, trainer, results = train_game_totals_model(
            data_path=str(data_path),
            **training_params
        )
        
        print("\n✅ Demo Training Completed Successfully!")
        print("=" * 60)
        print(f"📊 Final Test Results:")
        for key, value in results[0].items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
        
        print(f"\n💾 Model saved to: {trainer.checkpoint_callback.best_model_path}")
        print(f"📈 TensorBoard logs: {training_params['log_dir']}")
        
        # Show next steps
        print(f"\n🔄 Next Steps:")
        print(f"  1. Check TensorBoard logs: tensorboard --logdir {training_params['log_dir']}")
        print(f"  2. Use saved model for predictions")
        print(f"  3. Integrate with Player Points model for hierarchical predictions")
        print(f"  4. Tune hyperparameters based on validation results")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🎉 Demo Complete!")


def show_model_comparison():
    """Show comparison between standard and hierarchical models"""
    
    print("\n🔬 Model Architecture Comparison")
    print("=" * 50)
    print("📊 Standard Game Totals Model:")
    print("  - Uses only game-level features")
    print("  - Team context (embeddings)")
    print("  - Opponent matchup analysis")
    print("  - Rolling team performance metrics")
    print("  - Best for: Independent game predictions")
    
    print("\n🔗 Hierarchical Game Totals Model:")
    print("  - Includes all standard features")
    print("  - Integrates player predictions from Step 1")
    print("  - Enhanced prediction accuracy")
    print("  - Consistent cross-level modeling")
    print("  - Best for: Complete pipeline predictions")
    
    print("\n📈 Expected Benefits of Hierarchical Approach:")
    print("  - Better accuracy through player-level information")
    print("  - Consistent splits across modeling levels")
    print("  - Enhanced interpretability")
    print("  - Robust predictions for new scenarios")


if __name__ == "__main__":
    # Show model comparison
    show_model_comparison()
    
    # Run demo training
    run_demo_training()
