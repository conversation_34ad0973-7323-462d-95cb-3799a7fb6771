"""
Player Rebounds Model (Step 4 of 9)
====================================

Position-sensitive player rebounds model with Zero-Inflated Poisson output.

This module provides:
- Position-aware rebound prediction
- Zero-inflated Poisson modeling
- Opponent interaction features
- Comprehensive evaluation metrics
"""

from .models.player_rebounds_model import (
    PlayerReboundsModel,
    ZeroInflatedPoissonLayer,
    PositionEmbedding,
    BoxoutImpact
)

from .data_processing.rebound_data_module import (
    ReboundDataModule,
    ReboundFeatureEngineering,
    OpponentReboundFeatures
)

from .utils.rebound_metrics import (
    ReboundMetrics,
    ZeroInflatedEvaluator,
    PositionWeightedEvaluator,
    ReboundBenchmarks
)

__version__ = "1.0.0"
__author__ = "HMNV Analytics"

__all__ = [
    # Models
    "PlayerReboundsModel",
    "ZeroInflatedPoissonLayer", 
    "PositionEmbedding",
    "BoxoutImpact",
    
    # Data processing
    "ReboundDataModule",
    "ReboundFeatureEngineering",
    "OpponentReboundFeatures",
    
    # Evaluation
    "ReboundMetrics",
    "ZeroInflatedEvaluator",
    "PositionWeightedEvaluator",
    "ReboundBenchmarks"
]
