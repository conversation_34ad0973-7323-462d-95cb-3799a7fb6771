"""
🚀 Training Script for WNBA Game Totals Model

This script trains the Game Totals model using the same time-based splits
as the Player Points model for hierarchical consistency.
"""

import os
import sys
from pathlib import Path
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
import torch

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from data_processing.game_totals_data_module import GameTotalsDataModule
from models.game_totals_model import GameTotalsModel, HierarchicalGameTotalsModel


def train_game_totals_model(
    data_path: str,
    model_type: str = 'standard',
    max_epochs: int = 100,
    batch_size: int = 32,
    learning_rate: float = 1e-3,
    dropout: float = 0.3,
    hidden_dims: list = [256, 128, 64],
    early_stopping_patience: int = 15,
    checkpoint_dir: str = 'step_02_checkpoints',
    log_dir: str = 'step_02_logs',
    player_model_path: str = None
):
    """
    Train the WNBA Game Totals model
    
    Args:
        data_path: Path to the game-level data
        model_type: 'standard' or 'hierarchical'
        max_epochs: Maximum training epochs
        batch_size: Training batch size
        learning_rate: Learning rate
        dropout: Dropout rate
        hidden_dims: List of hidden layer dimensions
        early_stopping_patience: Early stopping patience
        checkpoint_dir: Directory to save model checkpoints
        log_dir: Directory for TensorBoard logs
        player_model_path: Path to trained player model (for hierarchical)
    """
    
    print(f"🏀 Training WNBA Game Totals Model ({model_type})")
    print("=" * 60)
    
    # Create directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Set up data module
    print("📊 Setting up data module...")
    data_module = GameTotalsDataModule(
        data_path=data_path,
        batch_size=batch_size,
        player_model_path=player_model_path
    )
    
    # Setup data to get feature dimensions
    data_module.setup()
    input_dim = len(data_module.feature_columns)
    
    print(f"Input dimension: {input_dim}")
    print(f"Feature columns: {data_module.feature_columns}")
    
    # Initialize model
    print(f"🧠 Initializing {model_type} model...")
    if model_type == 'hierarchical':
        model = HierarchicalGameTotalsModel(
            input_dim=input_dim,
            hidden_dims=hidden_dims,
            dropout=dropout,
            learning_rate=learning_rate,
            player_pred_dim=12  # Assuming 12 player predictions per team
        )
    else:
        model = GameTotalsModel(
            input_dim=input_dim,
            hidden_dims=hidden_dims,
            dropout=dropout,
            learning_rate=learning_rate
        )
    
    # Set up callbacks
    callbacks = [
        ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename=f'game_totals_{model_type}_' + '{epoch:02d}-{val_loss:.4f}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            save_last=True
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            mode='min',
            verbose=True
        ),
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # Set up logger
    logger = TensorBoardLogger(
        save_dir=log_dir,
        name=f'game_totals_{model_type}',
        version=None
    )
    
    # Initialize trainer
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        callbacks=callbacks,
        logger=logger,
        devices=1 if torch.cuda.is_available() else 'auto',
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        precision=16 if torch.cuda.is_available() else 32,
        gradient_clip_val=1.0,
        deterministic=True,
        enable_progress_bar=True,
        log_every_n_steps=10
    )
    
    # Train the model
    print("🚀 Starting training...")
    trainer.fit(model, data_module)
    
    # Test the model
    print("🧪 Testing model...")
    test_results = trainer.test(model, data_module)
    
    # Print results
    print("\n📊 Training Complete!")
    print("=" * 60)
    print(f"Best model checkpoint: {trainer.checkpoint_callback.best_model_path}")
    print(f"Test Results: {test_results[0]}")
    
    return model, trainer, test_results


def main():
    """Main function for demo training"""
    
    # Configuration
    config = {
        'model_type': 'standard',  # or 'hierarchical'
        'max_epochs': 50,
        'batch_size': 32,
        'learning_rate': 1e-3,
        'dropout': 0.3,
        'hidden_dims': [256, 128, 64],
        'early_stopping_patience': 10,
        'checkpoint_dir': 'step_02_checkpoints',
        'log_dir': 'step_02_logs'
    }
    
    # Path to game data (you'll need to create or specify this)
    base_path = Path(__file__).parent.parent.parent
    data_path = base_path / "consolidated_wnba" / "03_game_data" / "game_totals_sample.csv"
    
    # For now, let's create a sample data file if it doesn't exist
    if not data_path.exists():
        print("📝 Creating sample game data for training...")
        create_sample_game_data(str(data_path))
    
    print(f"📁 Using data file: {data_path}")
    
    # Train the model
    try:
        model, trainer, results = train_game_totals_model(
            data_path=str(data_path),
            **config
        )
        
        print("✅ Training completed successfully!")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        raise


def create_sample_game_data(output_path: str):
    """Create sample game data for training demonstration"""
    import pandas as pd
    import numpy as np
    
    # Create sample game data
    np.random.seed(42)
    
    # Generate sample games
    n_games = 500
    n_teams = 13  # Updated to include all 13 WNBA teams
    
    data = []
    for i in range(n_games):
        # Random team and opponent
        team_id = np.random.randint(0, n_teams)
        opponent_id = np.random.randint(0, n_teams)
        while opponent_id == team_id:
            opponent_id = np.random.randint(0, n_teams)
        
        # Generate realistic game stats
        base_score = np.random.normal(80, 12)
        team_total_points = max(60, min(120, int(base_score)))
        
        # Team stats that correlate with scoring
        field_goals_made = int(np.random.normal(30, 5))
        field_goals_attempted = int(field_goals_made / np.random.uniform(0.35, 0.55))
        three_pointers_made = int(np.random.normal(8, 3))
        three_pointers_attempted = int(three_pointers_made / np.random.uniform(0.25, 0.45))
        free_throws_made = int(np.random.normal(15, 4))
        free_throws_attempted = int(free_throws_made / np.random.uniform(0.7, 0.9))
        
        rebounds = int(np.random.normal(35, 6))
        assists = int(np.random.normal(20, 5))
        steals = int(np.random.normal(8, 3))
        blocks = int(np.random.normal(4, 2))
        turnovers = int(np.random.normal(14, 4))
        
        # Calculate percentages
        field_goal_percentage = field_goals_made / max(field_goals_attempted, 1)
        three_point_percentage = three_pointers_made / max(three_pointers_attempted, 1)
        free_throw_percentage = free_throws_made / max(free_throws_attempted, 1)
        
        # Generate date (spread across 2020-2025)
        year = np.random.choice([2020, 2021, 2022, 2023, 2024, 2025], 
                               p=[0.15, 0.15, 0.15, 0.15, 0.2, 0.2])
        month = np.random.randint(1, 13)
        day = np.random.randint(1, 29)
        date = f"{year}-{month:02d}-{day:02d}"
        
        game_data = {
            'game_id': i,
            'date': date,
            'team_id': team_id,
            'opponent_id': opponent_id,
            'team_total_points': team_total_points,
            'field_goals_made': field_goals_made,
            'field_goals_attempted': field_goals_attempted,
            'three_pointers_made': three_pointers_made,
            'three_pointers_attempted': three_pointers_attempted,
            'free_throws_made': free_throws_made,
            'free_throws_attempted': free_throws_attempted,
            'field_goal_percentage': field_goal_percentage,
            'three_point_percentage': three_point_percentage,
            'free_throw_percentage': free_throw_percentage,
            'rebounds': rebounds,
            'assists': assists,
            'steals': steals,
            'blocks': blocks,
            'turnovers': turnovers
        }
        
        data.append(game_data)
    
    # Create DataFrame and save
    df = pd.DataFrame(data)
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save to CSV
    df.to_csv(output_path, index=False)
    print(f"📊 Sample game data created: {len(df)} games saved to {output_path}")


if __name__ == "__main__":
    main()
