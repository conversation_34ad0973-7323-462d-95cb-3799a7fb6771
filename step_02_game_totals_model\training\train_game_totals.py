"""
🚀 Training Script for WNBA Game Totals Model

This script trains the Game Totals model using the same time-based splits
as the Player Points model for hierarchical consistency.
"""

import os
import sys
from pathlib import Path
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
import torch

# Add current directory to path for imports
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

# Import models and data modules
import importlib.util
spec = importlib.util.spec_from_file_location("game_totals_model", current_dir / "models" / "game_totals_model.py")
game_totals_model_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(game_totals_model_module)
GameTotalsModel = game_totals_model_module.GameTotalsModel

spec = importlib.util.spec_from_file_location("game_totals_data_module", current_dir / "data_processing" / "game_totals_data_module.py")
data_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(data_module)
GameTotalsDataModule = data_module.GameTotalsDataModule


def train_game_totals_model(
    data_path: str = None,  # Default to None to use unified data
    model_type: str = 'standard',
    max_epochs: int = 100,
    batch_size: int = 32,
    learning_rate: float = 1e-3,
    dropout: float = 0.3,
    hidden_dims: list = [256, 128, 64],
    early_stopping_patience: int = 15,
    checkpoint_dir: str = 'step_02_checkpoints',
    log_dir: str = 'step_02_logs',
    player_model_path: str = None,
    use_unified_data: bool = True  # Use unified data by default
):
    """
    Train the WNBA Game Totals model
    
    Args:
        data_path: Path to the game-level data
        model_type: 'standard' or 'hierarchical'
        max_epochs: Maximum training epochs
        batch_size: Training batch size
        learning_rate: Learning rate
        dropout: Dropout rate
        hidden_dims: List of hidden layer dimensions
        early_stopping_patience: Early stopping patience
        checkpoint_dir: Directory to save model checkpoints
        log_dir: Directory for TensorBoard logs
        player_model_path: Path to trained player model (for hierarchical)
    """
    
    print(f"🏀 Training WNBA Game Totals Model ({model_type})")
    print("=" * 60)
    
    # Create directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Determine data path
    if use_unified_data and data_path is None:
        # Use unified data by default
        from pathlib import Path
        unified_data_dir = Path(__file__).parent.parent.parent / "unified_data"
        data_path = str(unified_data_dir / "unified_game_data.csv")
        print(f"📊 Using unified data: {data_path}")
    elif data_path is None:
        raise ValueError("data_path must be provided if use_unified_data=False")

    # Set up data module
    print("📊 Setting up data module...")
    data_module = GameTotalsDataModule(
        data_path=data_path,
        batch_size=batch_size,
        player_model_path=player_model_path
    )
    
    # Setup data to get feature dimensions
    data_module.setup()
    input_dim = len(data_module.feature_columns)
    
    print(f"Input dimension: {input_dim}")
    print(f"Feature columns: {data_module.feature_columns}")
    
    # Initialize model
    print(f"🧠 Initializing {model_type} model...")
    model = GameTotalsModel(
        player_points_model=None,  # No player model for standard training
        input_dim=input_dim,
        dropout=dropout,
        learning_rate=learning_rate
    )
    
    # Set up callbacks
    callbacks = [
        ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename=f'game_totals_{model_type}_' + '{epoch:02d}-{val_loss:.4f}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            save_last=True
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            mode='min'
        ),
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # Set up logger
    logger = TensorBoardLogger(
        save_dir=log_dir,
        name=f'game_totals_{model_type}',
        version=None
    )
    
    # Initialize trainer
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        callbacks=callbacks,
        logger=logger,
        devices=1 if torch.cuda.is_available() else 'auto',
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        precision=16 if torch.cuda.is_available() else 32,
        gradient_clip_val=1.0,
        deterministic=True,
        enable_progress_bar=True,
        log_every_n_steps=10
    )
    
    # Train the model
    print("🚀 Starting training...")
    trainer.fit(model, data_module)
    
    # Test the model
    print("🧪 Testing model...")
    test_results = trainer.test(model, data_module)
    
    # Print results
    print("\n📊 Training Complete!")
    print("=" * 60)
    print(f"Best model checkpoint: {trainer.checkpoint_callback.best_model_path}")
    print(f"Test Results: {test_results[0]}")
    
    return model, trainer, test_results


def main():
    """Main function for demo training"""
    
    # Configuration
    config = {
        'model_type': 'standard',  # or 'hierarchical'
        'max_epochs': 50,
        'batch_size': 32,
        'learning_rate': 1e-3,
        'dropout': 0.3,
        'hidden_dims': [256, 128, 64],
        'early_stopping_patience': 10,
        'checkpoint_dir': 'step_02_checkpoints',
        'log_dir': 'step_02_logs'
    }
    
    # Path to comprehensive WNBA game data 
    base_path = Path(__file__).parent.parent.parent
    data_path = base_path / "step_02_game_totals_model" / "data" / "comprehensive_wnba_game_totals.csv"
    
    # Create comprehensive WNBA data if it doesn't exist
    if not data_path.exists():
        print("📝 Creating comprehensive WNBA game data...")
        os.makedirs(data_path.parent, exist_ok=True)
        
        # Import the comprehensive data creation function
        sys.path.append(str(base_path / "step_02_game_totals_model" / "utils"))
        from create_comprehensive_game_data import main as create_comprehensive_data
        
        create_comprehensive_data()
    
    print(f"📁 Using data file: {data_path}")
    
    # Train the model
    try:
        model, trainer, results = train_game_totals_model(
            data_path=str(data_path),
            **config
        )
        
        print("✅ Training completed successfully!")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        raise


def create_sample_game_data(output_path: str):
    """Load real WNBA game data - NO SYNTHETIC DATA ALLOWED"""
    import pandas as pd
    import os
    
    # Try to load real game data
    data_paths = [
        "../../consolidated_wnba/03_game_data/boxscores/team_totals.csv",
        "../../consolidated_wnba/02_team_data/basic_stats/team_game_stats.csv",
        "../sample_data/real_game_data.csv"
    ]
    
    for path in data_paths:
        if os.path.exists(path):
            print(f"📊 Loading real game data from: {path}")
            df = pd.read_csv(path)
            
            # Ensure required columns exist
            required_columns = [
                'team_id', 'opponent_id', 'team_total_points',
                'field_goals_made', 'field_goals_attempted',
                'three_pointers_made', 'three_pointers_attempted',
                'free_throws_made', 'free_throws_attempted',
                'rebounds', 'assists', 'steals', 'blocks', 'turnovers'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"⚠️  Missing columns in {path}: {missing_columns}")
                continue
            
            print(f"✅ Successfully loaded {len(df)} real game records")
            
            # Save to output path
            df.to_csv(output_path, index=False)
            print(f"💾 Saved real game data to: {output_path}")
            return
    
    # If no real data found, raise error - NO SYNTHETIC FALLBACK
    raise FileNotFoundError(
        "❌ CRITICAL: No real WNBA game data found. "
        "Synthetic data generation is DISABLED. "
        "Please ensure real game data is available in consolidated_wnba/. "
        f"Searched paths: {data_paths}"
    )


if __name__ == "__main__":
    # Define output path for real data
    output_path = "sample_data/real_game_data.csv"
    
    # Create sample game data (actually loads real data)
    try:
        create_sample_game_data(output_path)
        print("✅ Real game data setup complete!")
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("🔧 Please ensure real WNBA data is available in consolidated_wnba/")
        exit(1)


if __name__ == "__main__":
    main()
