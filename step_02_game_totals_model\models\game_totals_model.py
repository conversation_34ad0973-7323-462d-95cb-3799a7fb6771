"""
🏀 Game Totals Model Implementation (Step 2 of 9)
🔧 Core Architecture & Integration Flow

This module implements the exact hierarchical architecture specified for Step 2,
integrating frozen player model predictions with team-level features.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, Any, Optional
import numpy as np


class GameTotalsModel(pl.LightningModule):
    """
    🏀 Game Totals Model - Hierarchical Architecture
    
    Exact implementation following the specification:
    - Frozen player model integration
    - Team aggregation with outlier clipping
    - Hierarchical feature combination
    - Market calibration layer
    """
    
    def __init__(self, player_points_model, input_dim=28, dropout=0.2):
        super().__init__()
        self.save_hyperparameters(ignore=['player_points_model'])
        
        # Frozen player model from Step 1
        self.player_model = player_points_model
        self.player_model.freeze()
        
        # Team-level network (exact specification)
        self.team_net = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Dropout(dropout),
            
            nn.<PERSON>(256, 128),
            nn.<PERSON>ch<PERSON>orm1d(128),
            nn.<PERSON>(),
            
            nn.Linear(128, 64),
            nn.<PERSON><PERSON>(),
            
            nn.Linear(64, 1)
        )
        
        # Larger delta for game-level variance
        self.loss_fn = nn.HuberLoss(delta=2.0)

    def forward(self, team_features, player_features_dict):
        """
        Hierarchical forward pass combining player and team predictions
        
        Args:
            team_features: Team-level features [batch_size, team_feature_dim]
            player_features_dict: Dict with 'home' and 'away' player features
        
        Returns:
            Game total predictions [batch_size]
        """
        # Player-level predictions
        player_preds = {}
        for team_id, features in player_features_dict.items():
            player_preds[team_id] = self.player_model(features)
        
        # Team aggregation (sum with outlier clipping)
        team_aggs = {}
        for team_id, preds in player_preds.items():
            # Clip extreme individual predictions
            clipped = torch.clamp(preds, min=0, max=30)
            # Sum to get team total [batch_size, 1]
            team_aggs[team_id] = clipped.sum(dim=1).unsqueeze(1)
        
        # Combine with team-level features
        home_agg = team_aggs['home']
        away_agg = team_aggs['away']
        
        # Create hierarchical features
        enhanced_features = torch.cat([
            team_features,
            home_agg,
            away_agg,
            home_agg - away_agg,  # Differential feature
            (home_agg + away_agg) / 2  # Average feature
        ], dim=1)
        
        return self.team_net(enhanced_features).squeeze()

    def training_step(self, batch, batch_idx):
        """Training step with hierarchical data"""
        team_features, player_features_dict, targets = batch
        predictions = self(team_features, player_features_dict)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_mae', mae, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step with hierarchical data"""
        team_features, player_features_dict, targets = batch
        predictions = self(team_features, player_features_dict)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_mae', mae, prog_bar=True)
        
        return {'val_loss': loss, 'val_mae': mae}
    
    def test_step(self, batch, batch_idx):
        """Test step with hierarchical data"""
        team_features, player_features_dict, targets = batch
        predictions = self(team_features, player_features_dict)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        rmse = torch.sqrt(F.mse_loss(predictions, targets))
        
        # Game totals specific metrics
        within_5 = (torch.abs(predictions - targets) <= 5).float().mean()
        within_10 = (torch.abs(predictions - targets) <= 10).float().mean()
        
        self.log('test_loss', loss)
        self.log('test_mae', mae)
        self.log('test_rmse', rmse)
        self.log('test_within_5pts', within_5)
        self.log('test_within_10pts', within_10)
        
        return {
            'test_loss': loss,
            'test_mae': mae,
            'test_rmse': rmse,
            'within_5': within_5,
            'within_10': within_10
        }

    def configure_optimizers(self):
        """Optimizer configuration"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.get('learning_rate', 1e-3),
            weight_decay=1e-4
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.7,
            patience=8,
            min_lr=1e-6
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }


class MarketCalibration(nn.Module):
    """
    🎯 Market Calibration Layer for Real-World Adjustment
    
    Adjusts model predictions based on market indicators:
    - Opening lines
    - Line movement
    - Market efficiency signals
    """
    
    def __init__(self, input_dim=3):
        super().__init__()
        self.adjustment = nn.Sequential(
            nn.Linear(input_dim, 8),  # [model_pred, opening_line, line_movement]
            nn.Tanh(),
            nn.Linear(8, 1)
        )
    
    def forward(self, model_pred, market_data):
        """
        Apply market calibration to model predictions
        
        Args:
            model_pred: Raw model predictions [batch_size, 1]
            market_data: Market indicators [batch_size, 2] (opening_line, movement)
            
        Returns:
            Calibrated predictions [batch_size, 1]
        """
        combined_input = torch.cat([model_pred, market_data], dim=1)
        adjustment = self.adjustment(combined_input)
        return model_pred + adjustment


def calculate_impact_weights(player_preds):
    """
    Weight predictions by expected minutes and reliability
    
    Args:
        player_preds: Dict containing player predictions and metadata
        
    Returns:
        Impact weights for each player
    """
    # Weight by expected minutes (sigmoid normalization)
    weights = torch.sigmoid(player_preds['expected_minutes'] / 40)  # [0-1]
    
    # Adjust by prediction reliability (inverse variance)
    reliability = 1 / (1 + player_preds['pred_variance'])
    
    return weights * reliability


def validate_hierarchical_consistency(player_model, totals_model, dataloader, device='cpu'):
    """
    🧪 Validate consistency between player predictions and game totals
    
    Ensures hierarchical coherence within acceptable tolerance thresholds.
    
    Args:
        player_model: Trained player points model
        totals_model: Trained game totals model
        dataloader: Validation data loader
        device: Device for computation
        
    Returns:
        Dict with consistency metrics
    """
    player_model.eval()
    totals_model.eval()
    
    total_mae = 0
    consistency_errors = 0
    total_samples = 0
    
    with torch.no_grad():
        for team_feats, player_feats, targets in dataloader:
            team_feats = team_feats.to(device)
            targets = targets.to(device)
            
            # Move player features to device
            player_feats_device = {}
            for team_id, feats in player_feats.items():
                player_feats_device[team_id] = feats.to(device)
            
            # Get player predictions
            home_preds = player_model(player_feats_device['home'])
            away_preds = player_model(player_feats_device['away'])
            
            # Get game prediction
            game_pred = totals_model(team_feats, player_feats_device)
            
            # Calculate direct player sum
            direct_sum = home_preds.sum(dim=1) + away_preds.sum(dim=1)
            
            # Check consistency (threshold: 5 points)
            diff = (direct_sum - game_pred).abs()
            consistency_errors += (diff > 5).sum().item()
            
            # Calculate MAE
            total_mae += (game_pred - targets).abs().sum().item()
            total_samples += len(targets)
    
    avg_mae = total_mae / total_samples
    consistency_rate = 1 - (consistency_errors / total_samples)
    
    print(f"Game Total MAE: {avg_mae:.2f}")
    print(f"Consistency Rate: {consistency_rate:.1%}")
    print(f"Consistency Violations: {consistency_errors}/{total_samples}")
    
    return {
        'game_total_mae': avg_mae,
        'consistency_rate': consistency_rate,
        'consistency_violations': consistency_errors,
        'total_samples': total_samples
    }
