"""
🏀 WNBA Game Totals Model - Step 2 of Hierarchical Pipeline

This model predicts total points scored in WNBA games by aggregating individual player
predictions from the Player Points Model (Step 1) and combining them with team-level features.

Architecture:
- Input: Team-level features + aggregated player predictions
- Output: Game total points (combined home + away scores)
- Hierarchical integration with Player Points Model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, Any, Optional
import numpy as np


class GameTotalsModel(pl.LightningModule):
    """
    � Game Totals Prediction Model - Hierarchical Architecture
    
    Combines:
    1. Player-level predictions from Step 1 model (frozen)
    2. Team-level efficiency metrics  
    3. Game context features (rest, travel, pace)
    4. Market calibration layer
    """
    
    def __init__(
        self,
        player_points_model: Optional[pl.LightningModule] = None,
        input_dim: int = 28,
        hidden_dim: int = 256,
        dropout: float = 0.2,
        learning_rate: float = 1e-3
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['player_points_model'])
        
        # Store the trained player model (frozen for inference)
        self.player_model = player_points_model
        if self.player_model is not None:
            self.player_model.freeze()
            print("🔒 Player Points Model frozen for hierarchical inference")
        
        # Enhanced team-level neural network with hierarchical features
        self.team_net = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ELU(),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(128, 64),
            nn.ELU(),
            
            nn.Linear(64, 1)
        )
        
        # Market calibration layer for real-world adjustment
        self.market_calibration = nn.Sequential(
            nn.Linear(3, 8),  # [model_pred, pace_factor, efficiency_diff]
            nn.Tanh(),
            nn.Linear(8, 1)
        )
        
        # Robust loss function for game-level variance
        self.loss_fn = nn.HuberLoss(delta=2.0)  # Larger delta for game totals
        
        # Performance tracking
        self.train_mae = []
        self.val_mae = []
        self.use_player_predictions = use_player_predictions
        self.player_pred_dim = player_pred_dim
        
        # Calculate effective input dimension
        effective_input_dim = input_dim + (player_pred_dim if use_player_predictions else 0)
        
        # Input processing
        self.input_norm = nn.BatchNorm1d(effective_input_dim)
        self.input_dropout = nn.Dropout(dropout * 0.5)  # Lighter dropout on input
        
        # Build hidden layers
        self.hidden_layers = nn.ModuleList()
        self.hidden_norms = nn.ModuleList()
        self.hidden_dropouts = nn.ModuleList()
        
        prev_dim = effective_input_dim
        for hidden_dim in hidden_dims:
            self.hidden_layers.append(nn.Linear(prev_dim, hidden_dim))
            self.hidden_norms.append(nn.BatchNorm1d(hidden_dim))
            self.hidden_dropouts.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # Skip connection layers (for better gradient flow)
        self.skip_layers = nn.ModuleList()
        for i, hidden_dim in enumerate(hidden_dims[1:], 1):
            # Skip connection from previous hidden layer to current
            prev_hidden_dim = hidden_dims[i-1]
            self.skip_layers.append(nn.Linear(prev_hidden_dim, hidden_dim))
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # Team embedding (if we want to learn team-specific patterns)
        self.team_embedding = nn.Embedding(13, 16)  # 13 WNBA teams (including expansion)
        self.opponent_embedding = nn.Embedding(13, 16)  # Opponent embedding
        
        # Additional context processing
        self.context_processor = nn.Sequential(
            nn.Linear(32, 16),  # team + opponent embeddings
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
        
        # Final combination layer
        self.final_layer = nn.Linear(hidden_dims[-1] + 16, 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
    
    def forward(
        self, 
        x: torch.Tensor, 
        team_ids: Optional[torch.Tensor] = None,
        opponent_ids: Optional[torch.Tensor] = None,
        player_predictions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Forward pass
        
        Args:
            x: Input features [batch_size, input_dim]
            team_ids: Team IDs [batch_size]
            opponent_ids: Opponent IDs [batch_size]
            player_predictions: Optional player predictions [batch_size, player_pred_dim]
        
        Returns:
            Predicted team total points [batch_size, 1]
        """
        
        # Combine input features with player predictions if available
        if self.use_player_predictions and player_predictions is not None:
            x = torch.cat([x, player_predictions], dim=1)
        
        # Input processing
        x = self.input_norm(x)
        x = self.input_dropout(x)
        input_features = x
        
        # Process through hidden layers with skip connections
        prev_hidden = None
        for i, (hidden_layer, hidden_norm, hidden_dropout) in enumerate(
            zip(self.hidden_layers, self.hidden_norms, self.hidden_dropouts)
        ):
            # Main path
            x = hidden_layer(x)
            x = hidden_norm(x)
            x = F.relu(x)
            
            # Add skip connection from previous hidden layer
            if i > 0 and prev_hidden is not None:
                skip_output = self.skip_layers[i-1](prev_hidden)
                x = x + skip_output
            
            x = hidden_dropout(x)
            prev_hidden = x
        
        # Team context processing
        context_features = None
        if team_ids is not None and opponent_ids is not None:
            team_emb = self.team_embedding(team_ids)
            opponent_emb = self.opponent_embedding(opponent_ids)
            context_input = torch.cat([team_emb, opponent_emb], dim=1)
            context_features = self.context_processor(context_input)
        
        # Final prediction
        if context_features is not None:
            combined_features = torch.cat([x, context_features], dim=1)
            output = self.final_layer(combined_features)
        else:
            output = self.output_layer(x)
        
        return output
    
    def training_step(self, batch, batch_idx):
        """Training step"""
        if len(batch) == 5:
            features, targets, game_ids, team_ids, opponent_ids = batch
        else:
            features, targets = batch[:2]
            team_ids = opponent_ids = None
        
        predictions = self.forward(features, team_ids, opponent_ids)
        predictions = predictions.squeeze(-1)
        
        loss = F.mse_loss(predictions, targets)
        
        # Calculate metrics
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('train_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('train_mae', mae, on_step=False, on_epoch=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        if len(batch) == 5:
            features, targets, game_ids, team_ids, opponent_ids = batch
        else:
            features, targets = batch[:2]
            team_ids = opponent_ids = None
        
        predictions = self.forward(features, team_ids, opponent_ids)
        predictions = predictions.squeeze(-1)
        
        loss = F.mse_loss(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Calculate additional metrics
        with torch.no_grad():
            # Mean Absolute Percentage Error
            mape = torch.mean(torch.abs((targets - predictions) / (targets + 1e-8))) * 100
            
            # R-squared (coefficient of determination)
            ss_res = torch.sum((targets - predictions) ** 2)
            ss_tot = torch.sum((targets - torch.mean(targets)) ** 2)
            r2 = 1 - ss_res / (ss_tot + 1e-8)
        
        # Log metrics
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_mae', mae, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_mape', mape, on_step=False, on_epoch=True)
        self.log('val_r2', r2, on_step=False, on_epoch=True)
        
        return {
            'val_loss': loss,
            'val_mae': mae,
            'val_mape': mape,
            'val_r2': r2,
            'predictions': predictions.detach(),
            'targets': targets.detach()
        }
    
    def test_step(self, batch, batch_idx):
        """Test step"""
        if len(batch) == 5:
            features, targets, game_ids, team_ids, opponent_ids = batch
        else:
            features, targets = batch[:2]
            team_ids = opponent_ids = None
        
        predictions = self.forward(features, team_ids, opponent_ids)
        predictions = predictions.squeeze(-1)
        
        loss = F.mse_loss(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Calculate additional metrics
        with torch.no_grad():
            mape = torch.mean(torch.abs((targets - predictions) / (targets + 1e-8))) * 100
            ss_res = torch.sum((targets - predictions) ** 2)
            ss_tot = torch.sum((targets - torch.mean(targets)) ** 2)
            r2 = 1 - ss_res / (ss_tot + 1e-8)
        
        # Log metrics
        self.log('test_loss', loss, on_step=False, on_epoch=True)
        self.log('test_mae', mae, on_step=False, on_epoch=True)
        self.log('test_mape', mape, on_step=False, on_epoch=True)
        self.log('test_r2', r2, on_step=False, on_epoch=True)
        
        return {
            'test_loss': loss,
            'test_mae': mae,
            'test_mape': mape,
            'test_r2': r2,
            'predictions': predictions.detach(),
            'targets': targets.detach()
        }
    
    def configure_optimizers(self):
        """Configure optimizers and learning rate schedulers"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.7,
            patience=5,
            min_lr=1e-6
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',
                'frequency': 1
            }
        }
    
    def predict_step(self, batch, batch_idx):
        """Prediction step for inference"""
        if len(batch) == 5:
            features, targets, game_ids, team_ids, opponent_ids = batch
        else:
            features = batch[0]
            game_ids = team_ids = opponent_ids = None
        
        predictions = self.forward(features, team_ids, opponent_ids)
        return predictions.squeeze(-1)


class HierarchicalGameTotalsModel(GameTotalsModel):
    """
    🔗 Hierarchical version that integrates player predictions from Step 1
    
    This model can take player-level predictions and combine them with
    team-level features for enhanced game total predictions.
    """
    
    def __init__(
        self,
        input_dim: int,
        player_pred_dim: int = 12,  # e.g., 12 players * 1 prediction each
        **kwargs
    ):
        super().__init__(
            input_dim=input_dim,
            use_player_predictions=True,
            player_pred_dim=player_pred_dim,
            **kwargs
        )
        
        # Additional processing for player predictions
        self.player_processor = nn.Sequential(
            nn.Linear(player_pred_dim, player_pred_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout * 0.5),
            nn.Linear(player_pred_dim // 2, player_pred_dim // 4)
        )
    
    def forward(
        self, 
        x: torch.Tensor, 
        team_ids: Optional[torch.Tensor] = None,
        opponent_ids: Optional[torch.Tensor] = None,
        player_predictions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Forward pass with enhanced player prediction processing
        """
        
        # Process player predictions if available
        if player_predictions is not None:
            processed_player_preds = self.player_processor(player_predictions)
            x = torch.cat([x, processed_player_preds], dim=1)
        
        # Continue with parent forward pass (without player_predictions since we already processed)
        return super().forward(x, team_ids, opponent_ids, player_predictions=None)
