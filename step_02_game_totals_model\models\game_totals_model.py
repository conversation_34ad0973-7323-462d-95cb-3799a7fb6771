"""
🏀 WNBA Game Totals Model - Step 2 of Hierarchical Pipeline

This model predicts total points scored in WNBA games by aggregating individual player
predictions from the Player Points Model (Step 1) and combining them with team-level features.

Architecture:
- Input: Team-level features + aggregated player predictions
- Output: Game total points (combined home + away scores)
- Hierarchical integration with Player Points Model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, Any, Optional
import numpy as np


class GameTotalsModel(pl.LightningModule):
    """
    🏀 Game Totals Prediction Model - Hierarchical Architecture
    
    Combines:
    1. Player-level predictions from Step 1 model (frozen)
    2. Team-level efficiency metrics  
    3. Game context features (rest, travel, pace)
    4. Market calibration layer
    """
    
    def __init__(
        self,
        player_points_model: Optional[pl.LightningModule] = None,
        input_dim: int = 28,
        hidden_dim: int = 256,
        dropout: float = 0.2,
        learning_rate: float = 1e-3
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['player_points_model'])
        
        # Store the trained player model (frozen for inference)
        self.player_model = player_points_model
        if self.player_model is not None:
            self.player_model.freeze()
            print("🔒 Player Points Model frozen for hierarchical inference")
        
        # Enhanced team-level neural network with hierarchical features
        self.team_net = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ELU(),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(128, 64),
            nn.ELU(),
            
            nn.Linear(64, 1)
        )
        
        # Market calibration layer for real-world adjustment
        self.market_calibration = nn.Sequential(
            nn.Linear(3, 8),  # [model_pred, pace_factor, efficiency_diff]
            nn.Tanh(),
            nn.Linear(8, 1)
        )
        
        # Robust loss function for game-level variance
        self.loss_fn = nn.HuberLoss(delta=2.0)  # Larger delta for game totals
        
        # Performance tracking
        self.train_mae = []
        self.val_mae = []
        
    def forward(self, team_features: torch.Tensor, 
                player_features_dict: Optional[Dict[str, torch.Tensor]] = None) -> torch.Tensor:
        """
        Hierarchical forward pass combining player and team predictions
        
        Args:
            team_features: Team-level features [batch_size, input_dim]
            player_features_dict: Dict with 'home'/'away' player features
            
        Returns:
            Predicted game total points [batch_size]
        """
        batch_size = team_features.size(0)
        
        # Hierarchical approach: Player predictions → Team aggregation
        if self.player_model is not None and player_features_dict is not None:
            home_features = player_features_dict.get('home')
            away_features = player_features_dict.get('away')
            
            if home_features is not None and away_features is not None:
                # Get player-level predictions (frozen model)
                with torch.no_grad():
                    home_player_preds = self.player_model(home_features)
                    away_player_preds = self.player_model(away_features)
                
                # Aggregate with outlier clipping (key innovation)
                home_clipped = torch.clamp(home_player_preds, min=0, max=30)  
                away_clipped = torch.clamp(away_player_preds, min=0, max=30)
                
                home_total = home_clipped.sum(dim=1, keepdim=True)
                away_total = away_clipped.sum(dim=1, keepdim=True)
                
                # Create hierarchical features
                team_agg_features = torch.cat([
                    home_total,                           # Home team predicted total
                    away_total,                           # Away team predicted total
                    home_total - away_total,              # Score differential
                    (home_total + away_total) / 2,        # Average team scoring
                    torch.abs(home_total - away_total),   # Absolute differential
                ], dim=1)
                
                # Combine with original team features
                if team_features.size(1) + team_agg_features.size(1) != self.team_net[0].in_features:
                    # Adaptive combination to match expected input dimensions
                    combined_dim = team_features.size(1) + team_agg_features.size(1)
                    if not hasattr(self, 'feature_projection'):
                        self.feature_projection = nn.Linear(combined_dim, self.team_net[0].in_features)
                        self.feature_projection = self.feature_projection.to(team_features.device)
                    
                    enhanced_features = torch.cat([team_features, team_agg_features], dim=1)
                    team_features = self.feature_projection(enhanced_features)
                else:
                    team_features = torch.cat([team_features, team_agg_features], dim=1)
        
        # Main team network prediction
        raw_prediction = self.team_net(team_features).squeeze()
        
        # Market calibration (if we have pace and efficiency data)
        if team_features.size(1) >= 3:
            # Extract calibration features from team data
            pace_factor = team_features[:, -3:-2] if team_features.size(1) > 3 else torch.ones_like(raw_prediction).unsqueeze(1)
            efficiency_diff = team_features[:, -2:-1] if team_features.size(1) > 2 else torch.zeros_like(raw_prediction).unsqueeze(1)
            
            calibration_input = torch.cat([
                raw_prediction.unsqueeze(1),
                pace_factor,
                efficiency_diff
            ], dim=1)
            
            calibration_adjustment = self.market_calibration(calibration_input).squeeze()
            final_prediction = raw_prediction + calibration_adjustment
        else:
            final_prediction = raw_prediction
        
        return final_prediction
    
    def training_step(self, batch, batch_idx):
        """Training step with hierarchical loss"""
        if len(batch) == 3:
            team_features, player_features_dict, targets = batch
            predictions = self(team_features, player_features_dict)
        else:
            team_features, targets = batch
            predictions = self(team_features)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_mae', mae, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        if len(batch) == 3:
            team_features, player_features_dict, targets = batch
            predictions = self(team_features, player_features_dict)
        else:
            team_features, targets = batch
            predictions = self(team_features)
        
        loss = self.loss_fn(predictions, targets)
        mae = F.l1_loss(predictions, targets)
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_mae', mae, prog_bar=True)
        
        return {'val_loss': loss, 'val_mae': mae}
    
    def test_step(self, batch, batch_idx):
        """Test step with comprehensive metrics"""
        if len(batch) == 3:
            team_features, player_features_dict, targets = batch
            predictions = self(team_features, player_features_dict)
        else:
            team_features, targets = batch
            predictions = self(team_features)
        
        mae = F.l1_loss(predictions, targets)
        mse = F.mse_loss(predictions, targets)
        rmse = torch.sqrt(mse)
        
        # Additional game totals metrics
        within_5 = (torch.abs(predictions - targets) <= 5).float().mean()
        within_10 = (torch.abs(predictions - targets) <= 10).float().mean()
        
        self.log('test_mae', mae)
        self.log('test_rmse', rmse)
        self.log('test_within_5pts', within_5)
        self.log('test_within_10pts', within_10)
        
        return {
            'test_mae': mae, 
            'test_rmse': rmse,
            'within_5': within_5,
            'within_10': within_10
        }
    
    def configure_optimizers(self):
        """Advanced optimizer configuration"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # Use simple step scheduler instead of ReduceLROnPlateau
        scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=10,
            gamma=0.7
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch'
            }
        }


def validate_hierarchical_consistency(player_model, totals_model, dataloader, device='cpu'):
    """
    🧪 Validate consistency between player predictions and game totals
    
    Ensures hierarchical coherence within acceptable tolerance thresholds.
    """
    player_model.eval()
    totals_model.eval()
    
    total_games = 0
    consistency_errors = 0
    total_mae = 0
    consistency_diffs = []
    
    print("🧪 Running Hierarchical Consistency Validation...")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(dataloader):
            if len(batch) == 3:
                team_features, player_features_dict, targets = batch
                team_features = team_features.to(device)
                targets = targets.to(device)
                
                # Get hierarchical predictions
                game_preds = totals_model(team_features, player_features_dict)
                
                # Direct player sum (if player features available)
                if player_features_dict and 'home' in player_features_dict and 'away' in player_features_dict:
                    home_preds = player_model(player_features_dict['home'].to(device))
                    away_preds = player_model(player_features_dict['away'].to(device))
                    direct_sum = home_preds.sum(dim=1) + away_preds.sum(dim=1)
                    
                    # Check consistency (tolerance: 5 points)
                    diff = torch.abs(direct_sum - game_preds)
                    consistency_diffs.extend(diff.cpu().numpy())
                    consistency_errors += (diff > 5).sum().item()
                
            else:
                team_features, targets = batch
                team_features = team_features.to(device)
                targets = targets.to(device)
                game_preds = totals_model(team_features)
            
            # Calculate MAE
            mae = F.l1_loss(game_preds, targets)
            total_mae += mae.item()
            total_games += len(targets)
    
    # Calculate metrics
    avg_mae = total_mae / len(dataloader)
    consistency_rate = 1 - (consistency_errors / total_games) if total_games > 0 else 1.0
    avg_consistency_diff = np.mean(consistency_diffs) if consistency_diffs else 0.0
    
    metrics = {
        'game_total_mae': avg_mae,
        'consistency_rate': consistency_rate,
        'avg_consistency_diff': avg_consistency_diff,
        'total_games_validated': total_games,
        'consistency_violations': consistency_errors
    }
    
    # Results summary
    print(f"📊 Hierarchical Validation Results:")
    print(f"   Game Total MAE: {avg_mae:.2f} points")
    print(f"   Consistency Rate: {consistency_rate:.1%}")
    print(f"   Avg Consistency Diff: {avg_consistency_diff:.2f} points")
    print(f"   Games Validated: {total_games}")
    print(f"   Violations (>5pts): {consistency_errors}")
    
    # Performance benchmarks
    if avg_mae < 8.0:
        print("✅ Game Total MAE meets target (<8.0)")
    else:
        print("⚠️  Game Total MAE above target threshold")
        
    if consistency_rate > 0.95:
        print("✅ Consistency rate meets target (>95%)")
    else:
        print("⚠️  Consistency rate below target threshold")
    
    return metrics


def calculate_impact_weights(player_predictions, expected_minutes=None):
    """
    Calculate dynamic impact weights for player predictions based on 
    expected minutes and prediction reliability.
    """
    if expected_minutes is not None:
        # Weight by expected playing time
        minute_weights = torch.sigmoid(expected_minutes / 40.0)  # Normalize to [0,1]
        
        # Weight by prediction confidence (inverse of variance if available)
        if hasattr(player_predictions, 'variance'):
            confidence_weights = 1 / (1 + player_predictions.variance)
            return minute_weights * confidence_weights
        else:
            return minute_weights
    else:
        # Equal weighting if no minute data
        return torch.ones_like(player_predictions)


class MarketCalibration(nn.Module):
    """
    🎯 Market Calibration Layer for Real-World Adjustment
    
    Adjusts model predictions based on market indicators:
    - Opening lines
    - Line movement
    - Market efficiency signals
    """
    
    def __init__(self, input_dim=3):
        super().__init__()
        self.adjustment = nn.Sequential(
            nn.Linear(input_dim, 16),  # [model_pred, opening_line, line_movement]
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(16, 8),
            nn.Tanh(),
            nn.Linear(8, 1)
        )
    
    def forward(self, model_pred, market_data):
        """
        Apply market calibration to model predictions
        
        Args:
            model_pred: Raw model predictions [batch_size, 1]
            market_data: Market indicators [batch_size, 2] (opening_line, movement)
            
        Returns:
            Calibrated predictions [batch_size, 1]
        """
        combined_input = torch.cat([model_pred.unsqueeze(1), market_data], dim=1)
        adjustment = self.adjustment(combined_input)
        return model_pred + adjustment.squeeze()


# Team-level features for hierarchical integration
TEAM_FEATURES = [
    # Offensive efficiency metrics
    'home_off_eff', 'away_off_eff', 'home_off_rating', 'away_off_rating',
    
    # Defensive efficiency metrics  
    'home_def_eff', 'away_def_eff', 'home_def_rating', 'away_def_rating',
    
    # Pace and possessions
    'home_pace', 'away_pace', 'avg_possessions_last5', 'pace_differential',
    
    # Rest and travel
    'rest_diff', 'travel_miles_diff', 'altitude_diff', 'b2b_home', 'b2b_away',
    
    # Recent performance
    'home_form_last5', 'away_form_last5', 'home_total_last3', 'away_total_last3',
    
    # Matchup specific
    'head_to_head_avg', 'season_avg_total', 'venue_scoring_factor'
]

# Contextual features for game environment
CONTEXTUAL_FEATURES = [
    'overtime_prob', 'referee_avg_total', 'season_phase', 'playoff_intensity',
    'injury_impact_home', 'injury_impact_away', 'motivation_factor'
]


def track_prediction_drift(model, reference_data, current_data):
    """
    🎯 Detect feature distribution shifts using MMD

    Performance Monitoring Implementation for detecting model drift

    Args:
        model: Trained GameTotalsModel
        reference_data: Historical reference dataset
        current_data: Current prediction dataset

    Returns:
        MMD score indicating distribution shift
    """
    model.eval()

    with torch.no_grad():
        # Extract features from both datasets
        ref_features = []
        curr_features = []

        for data in reference_data:
            if len(data) == 3:
                team_feats, player_feats, _ = data
                # Get intermediate features before final prediction
                if model.player_model is not None and player_feats is not None:
                    # Use hierarchical features
                    home_preds = model.player_model(player_feats['home'])
                    away_preds = model.player_model(player_feats['away'])
                    home_clipped = torch.clamp(home_preds, min=0, max=30)
                    away_clipped = torch.clamp(away_preds, min=0, max=30)
                    features = torch.cat([
                        team_feats,
                        home_clipped.sum(dim=1, keepdim=True),
                        away_clipped.sum(dim=1, keepdim=True)
                    ], dim=1)
                else:
                    features = team_feats
                ref_features.append(features)

        for data in current_data:
            if len(data) == 3:
                team_feats, player_feats, _ = data
                if model.player_model is not None and player_feats is not None:
                    home_preds = model.player_model(player_feats['home'])
                    away_preds = model.player_model(player_feats['away'])
                    home_clipped = torch.clamp(home_preds, min=0, max=30)
                    away_clipped = torch.clamp(away_preds, min=0, max=30)
                    features = torch.cat([
                        team_feats,
                        home_clipped.sum(dim=1, keepdim=True),
                        away_clipped.sum(dim=1, keepdim=True)
                    ], dim=1)
                else:
                    features = team_feats
                curr_features.append(features)

    # Compute MMD (Maximum Mean Discrepancy)
    ref_tensor = torch.cat(ref_features, dim=0)
    curr_tensor = torch.cat(curr_features, dim=0)

    return mmd_kernel(ref_tensor, curr_tensor)


def mmd_kernel(x, y, kernel='rbf', gamma=1.0):
    """
    Compute Maximum Mean Discrepancy using RBF kernel

    Args:
        x: Reference features [n_samples, n_features]
        y: Current features [m_samples, n_features]
        kernel: Kernel type ('rbf')
        gamma: RBF kernel parameter

    Returns:
        MMD score (higher = more drift)
    """
    def rbf_kernel(x, y, gamma):
        """RBF kernel computation"""
        x_norm = (x ** 2).sum(dim=1, keepdim=True)
        y_norm = (y ** 2).sum(dim=1, keepdim=True)
        dist = x_norm + y_norm.t() - 2 * torch.mm(x, y.t())
        return torch.exp(-gamma * dist)

    # Compute kernel matrices
    kxx = rbf_kernel(x, x, gamma).mean()
    kyy = rbf_kernel(y, y, gamma).mean()
    kxy = rbf_kernel(x, y, gamma).mean()

    # MMD^2 = E[k(x,x)] + E[k(y,y)] - 2*E[k(x,y)]
    mmd_squared = kxx + kyy - 2 * kxy
    return torch.sqrt(torch.clamp(mmd_squared, min=0.0))


def evaluate_performance_benchmarks(model, dataloader, market_data=None):
    """
    🎯 Evaluate model against performance benchmarks

    Benchmarks:
    - Game Total MAE < 8.0 (Alert > 9.0)
    - Player-Game Consistency > 95% (Alert < 90%)
    - Market Calibration Error < 1.5% (Alert > 2.5%)
    - Overtime Game Detection AUC > 0.75 (Alert < 0.65)

    Args:
        model: Trained GameTotalsModel
        dataloader: Validation/test dataloader
        market_data: Optional market data for calibration evaluation

    Returns:
        Dict with benchmark results and alerts
    """
    model.eval()

    total_mae = 0
    consistency_errors = 0
    total_samples = 0
    predictions = []
    targets = []

    with torch.no_grad():
        for batch in dataloader:
            if len(batch) == 3:
                team_feats, player_feats, batch_targets = batch

                # Get predictions
                batch_preds = model(team_feats, player_feats)

                # Calculate MAE
                mae = torch.abs(batch_preds - batch_targets).sum().item()
                total_mae += mae

                # Check consistency if player model available
                if model.player_model is not None and player_feats is not None:
                    home_preds = model.player_model(player_feats['home'])
                    away_preds = model.player_model(player_feats['away'])
                    direct_sum = home_preds.sum(dim=1) + away_preds.sum(dim=1)
                    diff = torch.abs(direct_sum - batch_preds)
                    consistency_errors += (diff > 5).sum().item()

                total_samples += len(batch_targets)
                predictions.extend(batch_preds.cpu().numpy())
                targets.extend(batch_targets.cpu().numpy())

    # Calculate metrics
    game_total_mae = total_mae / total_samples
    consistency_rate = 1 - (consistency_errors / total_samples) if total_samples > 0 else 1.0

    # Overtime detection (games > 200 points)
    predictions_np = np.array(predictions)
    targets_np = np.array(targets)
    overtime_actual = (targets_np > 200).astype(int)
    overtime_pred_scores = predictions_np / 200  # Normalize as probability

    try:
        from sklearn.metrics import roc_auc_score
        overtime_auc = roc_auc_score(overtime_actual, overtime_pred_scores)
    except:
        overtime_auc = 0.5  # Default if no overtime games or sklearn not available

    # Market calibration error (if market data available)
    market_error = 0.0
    if market_data is not None:
        # Calculate percentage error between predictions and market lines
        market_error = np.mean(np.abs(predictions_np - market_data)) / np.mean(predictions_np) * 100

    # Generate alerts
    alerts = []
    if game_total_mae > 9.0:
        alerts.append(f"🚨 ALERT: Game Total MAE ({game_total_mae:.2f}) exceeds threshold (9.0)")
    if consistency_rate < 0.90:
        alerts.append(f"🚨 ALERT: Consistency Rate ({consistency_rate:.1%}) below threshold (90%)")
    if market_error > 2.5:
        alerts.append(f"🚨 ALERT: Market Calibration Error ({market_error:.1%}) exceeds threshold (2.5%)")
    if overtime_auc < 0.65:
        alerts.append(f"🚨 ALERT: Overtime Detection AUC ({overtime_auc:.3f}) below threshold (0.65)")

    results = {
        'game_total_mae': game_total_mae,
        'consistency_rate': consistency_rate,
        'market_calibration_error': market_error,
        'overtime_detection_auc': overtime_auc,
        'alerts': alerts,
        'meets_targets': {
            'mae_target': game_total_mae < 8.0,
            'consistency_target': consistency_rate > 0.95,
            'market_target': market_error < 1.5,
            'overtime_target': overtime_auc > 0.75
        }
    }

    print("🎯 Performance Benchmark Results:")
    print(f"   Game Total MAE: {game_total_mae:.2f} (Target: <8.0, Alert: >9.0)")
    print(f"   Consistency Rate: {consistency_rate:.1%} (Target: >95%, Alert: <90%)")
    print(f"   Market Calibration Error: {market_error:.1%} (Target: <1.5%, Alert: >2.5%)")
    print(f"   Overtime Detection AUC: {overtime_auc:.3f} (Target: >0.75, Alert: <0.65)")

    if alerts:
        print("\n🚨 ALERTS:")
        for alert in alerts:
            print(f"   {alert}")
    else:
        print("\n✅ All benchmarks within acceptable ranges")

    return results
