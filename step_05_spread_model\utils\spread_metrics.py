"""
Spread Model Metrics and Evaluation
===================================

Specialized metrics for evaluating spread model performance,
including consistency checks with moneyline predictions.
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class SpreadMetrics:
    """Comprehensive metrics for spread model evaluation"""
    
    def __init__(self, spread_std: float = 8.0):
        self.spread_std = spread_std
        self.epsilon = 1e-8
        
    def calculate_spread_mae(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Calculate Mean Absolute Error for spread predictions"""
        return torch.mean(torch.abs(predictions - targets)).item()
        
    def calculate_spread_mse(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Calculate Mean Squared Error for spread predictions"""
        return torch.mean((predictions - targets) ** 2).item()
        
    def calculate_spread_rmse(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Calculate Root Mean Squared Error for spread predictions"""
        return torch.sqrt(torch.mean((predictions - targets) ** 2)).item()
        
    def calculate_spread_accuracy(self, predictions: torch.Tensor, targets: torch.Tensor, 
                                tolerance: float = 3.0) -> float:
        """Calculate accuracy within tolerance (default 3 points)"""
        within_tolerance = torch.abs(predictions - targets) <= tolerance
        return torch.mean(within_tolerance.float()).item()
        
    def calculate_directional_accuracy(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Calculate accuracy of predicting spread direction (positive/negative)"""
        pred_sign = torch.sign(predictions)
        target_sign = torch.sign(targets)
        correct = (pred_sign == target_sign)
        return torch.mean(correct.float()).item()
        
    def calculate_spread_correlation(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Calculate Pearson correlation between predictions and targets"""
        pred_mean = torch.mean(predictions)
        target_mean = torch.mean(targets)
        
        pred_centered = predictions - pred_mean
        target_centered = targets - target_mean
        
        numerator = torch.sum(pred_centered * target_centered)
        denominator = torch.sqrt(torch.sum(pred_centered ** 2) * torch.sum(target_centered ** 2))
        
        if denominator < self.epsilon:
            return 0.0
        return (numerator / denominator).item()
        
    def calculate_spread_distribution_stats(self, predictions: torch.Tensor, 
                                          targets: torch.Tensor) -> Dict[str, float]:
        """Calculate distribution statistics for spread predictions"""
        pred_std = torch.std(predictions).item()
        target_std = torch.std(targets).item()
        
        pred_mean = torch.mean(predictions).item()
        target_mean = torch.mean(targets).item()
        
        return {
            'pred_mean': pred_mean,
            'pred_std': pred_std,
            'target_mean': target_mean,
            'target_std': target_std,
            'std_ratio': pred_std / (target_std + self.epsilon)
        }
        
    def calculate_consistency_score(self, spread_preds: torch.Tensor, 
                                  moneyline_preds: torch.Tensor) -> float:
        """
        Calculate consistency between spread and moneyline predictions
        
        Args:
            spread_preds: Predicted spreads (positive = home favored)
            moneyline_preds: Predicted moneyline probabilities (home win prob)
        """
        # Convert spread to implied win probability
        spread_win_prob = torch.sigmoid(spread_preds * 0.1)
        
        # Calculate consistency (1 - abs difference)
        consistency = 1.0 - torch.abs(spread_win_prob - moneyline_preds)
        return torch.mean(consistency).item()
        
    def calculate_betting_metrics(self, predictions: torch.Tensor, targets: torch.Tensor,
                                betting_lines: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        Calculate betting-specific metrics
        
        Args:
            predictions: Model predictions
            targets: Actual outcomes
            betting_lines: Market spreads (if available)
        """
        metrics = {}
        
        # Model vs actual performance
        beat_spread = (predictions - targets) > 0
        beat_rate = torch.mean(beat_spread.float()).item()
        metrics['beat_spread_rate'] = beat_rate
        
        # If market lines are available, calculate edge metrics
        if betting_lines is not None:
            # Model edge over market
            model_edge = torch.abs(predictions - betting_lines)
            metrics['avg_model_edge'] = torch.mean(model_edge).item()
            
            # Model accuracy vs market accuracy
            model_correct = torch.abs(predictions - targets) < torch.abs(betting_lines - targets)
            metrics['model_vs_market_accuracy'] = torch.mean(model_correct.float()).item()
            
        return metrics
        
    def calculate_position_specific_metrics(self, predictions: torch.Tensor, 
                                          targets: torch.Tensor,
                                          home_indicators: torch.Tensor) -> Dict[str, float]:
        """Calculate metrics split by home/away position"""
        home_mask = home_indicators.bool()
        away_mask = ~home_mask
        
        metrics = {}
        
        if torch.sum(home_mask) > 0:
            home_preds = predictions[home_mask]
            home_targets = targets[home_mask]
            metrics['home_mae'] = self.calculate_spread_mae(home_preds, home_targets)
            metrics['home_accuracy'] = self.calculate_spread_accuracy(home_preds, home_targets)
            
        if torch.sum(away_mask) > 0:
            away_preds = predictions[away_mask]
            away_targets = targets[away_mask]
            metrics['away_mae'] = self.calculate_spread_mae(away_preds, away_targets)
            metrics['away_accuracy'] = self.calculate_spread_accuracy(away_preds, away_targets)
            
        return metrics
        
    def calculate_all_metrics(self, predictions: torch.Tensor, targets: torch.Tensor,
                            moneyline_preds: Optional[torch.Tensor] = None,
                            betting_lines: Optional[torch.Tensor] = None,
                            home_indicators: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """Calculate comprehensive metrics for spread model evaluation"""
        metrics = {}
        
        # Basic spread metrics
        metrics['spread_mae'] = self.calculate_spread_mae(predictions, targets)
        metrics['spread_mse'] = self.calculate_spread_mse(predictions, targets)
        metrics['spread_rmse'] = self.calculate_spread_rmse(predictions, targets)
        metrics['spread_accuracy'] = self.calculate_spread_accuracy(predictions, targets)
        metrics['directional_accuracy'] = self.calculate_directional_accuracy(predictions, targets)
        metrics['spread_correlation'] = self.calculate_spread_correlation(predictions, targets)
        
        # Distribution stats
        dist_stats = self.calculate_spread_distribution_stats(predictions, targets)
        metrics.update(dist_stats)
        
        # Consistency with moneyline
        if moneyline_preds is not None:
            metrics['consistency_score'] = self.calculate_consistency_score(
                predictions, moneyline_preds
            )
            
        # Betting metrics
        if betting_lines is not None:
            betting_metrics = self.calculate_betting_metrics(predictions, targets, betting_lines)
            metrics.update(betting_metrics)
            
        # Position-specific metrics
        if home_indicators is not None:
            position_metrics = self.calculate_position_specific_metrics(
                predictions, targets, home_indicators
            )
            metrics.update(position_metrics)
            
        return metrics


class SpreadEvaluator:
    """Comprehensive evaluator for spread model performance"""
    
    def __init__(self, model, device: str = 'cpu'):
        self.model = model
        self.device = device
        self.metrics = SpreadMetrics()
        
    def evaluate_model(self, dataloader, return_predictions: bool = False) -> Dict[str, float]:
        """Evaluate model on a dataset"""
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        all_moneyline_preds = []
        
        with torch.no_grad():
            for batch in dataloader:
                # Handle TensorDataset format
                if isinstance(batch, (list, tuple)) and len(batch) == 2:
                    features, targets = batch
                    features = features.to(self.device)
                    targets = targets.to(self.device)
                else:
                    # Dictionary format
                    features = batch['features'].to(self.device)
                    targets = batch['spread'].to(self.device)
                
                # Get predictions
                outputs = self.model(features)
                predictions = outputs['spread_pred']
                
                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())
                
                # Get moneyline predictions if available
                if 'moneyline_pred' in outputs:
                    all_moneyline_preds.append(outputs['moneyline_pred'].cpu())
        
        # Concatenate all predictions
        predictions = torch.cat(all_predictions, dim=0)
        targets = torch.cat(all_targets, dim=0)
        
        moneyline_preds = None
        if all_moneyline_preds:
            moneyline_preds = torch.cat(all_moneyline_preds, dim=0)
        
        # Calculate metrics
        metrics = self.metrics.calculate_all_metrics(
            predictions, targets, moneyline_preds
        )
        
        if return_predictions:
            return metrics, predictions, targets
        return metrics
        
    def generate_evaluation_report(self, metrics: Dict[str, float]) -> str:
        """Generate a formatted evaluation report"""
        report = []
        report.append("=== Spread Model Evaluation Report ===\n")
        
        # Basic metrics
        report.append("Basic Spread Metrics:")
        report.append(f"  MAE: {metrics.get('spread_mae', 0):.3f}")
        report.append(f"  RMSE: {metrics.get('spread_rmse', 0):.3f}")
        report.append(f"  Accuracy (±3pts): {metrics.get('spread_accuracy', 0):.3f}")
        report.append(f"  Directional Accuracy: {metrics.get('directional_accuracy', 0):.3f}")
        report.append(f"  Correlation: {metrics.get('spread_correlation', 0):.3f}")
        
        # Distribution stats
        report.append("\nDistribution Statistics:")
        report.append(f"  Prediction Mean: {metrics.get('pred_mean', 0):.3f}")
        report.append(f"  Prediction Std: {metrics.get('pred_std', 0):.3f}")
        report.append(f"  Target Mean: {metrics.get('target_mean', 0):.3f}")
        report.append(f"  Target Std: {metrics.get('target_std', 0):.3f}")
        
        # Consistency
        if 'consistency_score' in metrics:
            report.append(f"\nConsistency Score: {metrics['consistency_score']:.3f}")
        
        # Position-specific metrics
        if 'home_mae' in metrics:
            report.append("\nPosition-Specific Metrics:")
            report.append(f"  Home MAE: {metrics.get('home_mae', 0):.3f}")
            report.append(f"  Away MAE: {metrics.get('away_mae', 0):.3f}")
            report.append(f"  Home Accuracy: {metrics.get('home_accuracy', 0):.3f}")
            report.append(f"  Away Accuracy: {metrics.get('away_accuracy', 0):.3f}")
        
        # Betting metrics
        if 'beat_spread_rate' in metrics:
            report.append("\nBetting Metrics:")
            report.append(f"  Beat Spread Rate: {metrics.get('beat_spread_rate', 0):.3f}")
            if 'avg_model_edge' in metrics:
                report.append(f"  Average Model Edge: {metrics.get('avg_model_edge', 0):.3f}")
                report.append(f"  Model vs Market Accuracy: {metrics.get('model_vs_market_accuracy', 0):.3f}")
        
        return '\n'.join(report)
