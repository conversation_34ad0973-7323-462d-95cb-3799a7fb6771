"""
🔧 Feature Engineering Utilities for WNBA Player Points Model

This module provides advanced feature engineering functions to enhance
the predictive power of the player points model using basketball domain knowledge.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from sklearn.preprocessing import LabelEncoder


def engineer_temporal_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add temporal features for better time-aware predictions
    
    Args:
        df: DataFrame with date column
    
    Returns:
        DataFrame with additional temporal features
    """
    df = df.copy()
    
    if 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])
        
        # Days since season start
        df['days_since_season_start'] = (df['date'] - df['date'].min()).dt.days
        
        # Day of week (games on different days may have different patterns)
        df['day_of_week'] = df['date'].dt.dayofweek
        
        # Month (season progression)
        df['month'] = df['date'].dt.month
        
        # Weekend indicator
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    
    # Days rest calculation (if we have player game sequences)
    if 'player_id' in df.columns and 'date' in df.columns:
        df = df.sort_values(['player_id', 'date'])
        df['days_rest'] = df.groupby('player_id')['date'].diff().dt.days.fillna(3)
        
        # Back-to-back games indicator
        df['b2b_flag'] = (df['days_rest'] <= 1).astype(int)
        
        # Days until next game (if available)
        df['days_next_game'] = df.groupby('player_id')['date'].shift(-1) - df['date']
        df['days_next_game'] = df['days_next_game'].dt.days.fillna(3)
    
    return df


def engineer_matchup_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add matchup-specific features based on opponent and game context
    
    Args:
        df: DataFrame with team and opponent information
    
    Returns:
        DataFrame with matchup features
    """
    df = df.copy()
    
    # Home/away advantage
    if 'home_team' in df.columns and 'team_abbreviation' in df.columns:
        df['home_advantage'] = (df['home_team'] == df['team_abbreviation']).astype(int)
    elif 'home_advantage' not in df.columns:
        # Use player index to create consistent home/away split (not random)
        # This ensures reproducible results while maintaining realistic distribution
        df['home_advantage'] = (df.index % 2).astype(int)  # 50% home games, consistent assignment
    
    # Opponent strength (if we have historical data)
    if 'opponent_team' in df.columns:
        # Calculate opponent defensive rating
        opp_def_rating = df.groupby('opponent_team')['points'].mean()
        df['opp_def_rating'] = df['opponent_team'].map(opp_def_rating).fillna(df['points'].mean())
        
        # Opponent pace (possessions per game)
        if 'possessions' in df.columns:
            opp_pace = df.groupby('opponent_team')['possessions'].mean()
            df['opp_pace_impact'] = df['opponent_team'].map(opp_pace).fillna(df['possessions'].mean())
    
    # Altitude adjustment (for teams playing at different altitudes)
    altitude_map = {
        'DEN': 5280,  # Denver (example)
        'UTA': 4226,  # Salt Lake City (example)
        # Add more teams as needed
    }
    
    if 'team_abbreviation' in df.columns:
        df['altitude'] = df['team_abbreviation'].map(altitude_map).fillna(0)
        df['altitude_adjustment'] = (df['altitude'] > 3000).astype(int)
    
    return df


def engineer_advanced_metrics(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate advanced basketball metrics for better predictions
    
    Args:
        df: DataFrame with basic basketball stats
    
    Returns:
        DataFrame with advanced metrics
    """
    df = df.copy()
    
    # Usage rate (if we have team stats)
    if all(col in df.columns for col in ['minutes_per_game', 'field_goal_percentage']):
        # Approximate usage rate
        df['usage_rate'] = df['minutes_per_game'] / 48.0 * df['field_goal_percentage']
        
        # Exponentially weighted moving average of usage rate
        if 'player_id' in df.columns:
            df = df.sort_values(['player_id', 'date'] if 'date' in df.columns else ['player_id'])
            df['usage_rate_ema10'] = df.groupby('player_id')['usage_rate'].transform(
                lambda x: x.ewm(span=10, min_periods=1).mean()
            )
    
    # Shot quality index
    if all(col in df.columns for col in ['field_goal_percentage', 'free_throw_percentage']):
        df['shot_quality_index'] = (
            0.7 * df['field_goal_percentage'] + 
            0.3 * df['free_throw_percentage']
        )
    
    # Defensive impact
    if all(col in df.columns for col in ['steals', 'blocks']):
        df['defensive_impact'] = df['steals'] + 2 * df['blocks']  # Blocks weighted more
    
    # Efficiency metrics
    if 'minutes_per_game' in df.columns and df['minutes_per_game'].sum() > 0:
        for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks']:
            if stat in df.columns:
                df[f'{stat}_per_minute'] = df[stat] / (df['minutes_per_game'] + 1e-6)
    
    # Player consistency (rolling standard deviation)
    if 'player_id' in df.columns and 'points' in df.columns:
        df = df.sort_values(['player_id', 'date'] if 'date' in df.columns else ['player_id'])
        df['points_consistency'] = df.groupby('player_id')['points'].transform(
            lambda x: x.rolling(window=10, min_periods=3).std().fillna(0)
        )
    
    return df


def engineer_contextual_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add contextual features based on game situation and player role
    
    Args:
        df: DataFrame with player and game data
    
    Returns:
        DataFrame with contextual features
    """
    df = df.copy()
    
    # Player role indicators (based on minutes and stats)
    if 'minutes_per_game' in df.columns:
        df['starter_role'] = (df['minutes_per_game'] >= 25).astype(int)
        df['bench_role'] = ((df['minutes_per_game'] >= 10) & (df['minutes_per_game'] < 25)).astype(int)
        df['limited_role'] = (df['minutes_per_game'] < 10).astype(int)
    
    # Scoring role (primary, secondary, role player)
    if 'points' in df.columns:
        # Calculate percentiles within team
        if 'team_abbreviation' in df.columns:
            df['team_scoring_rank'] = df.groupby('team_abbreviation')['points'].rank(pct=True)
            df['primary_scorer'] = (df['team_scoring_rank'] >= 0.8).astype(int)
            df['secondary_scorer'] = ((df['team_scoring_rank'] >= 0.6) & (df['team_scoring_rank'] < 0.8)).astype(int)
        else:
            # Global percentiles
            df['scoring_percentile'] = df['points'].rank(pct=True)
            df['primary_scorer'] = (df['scoring_percentile'] >= 0.8).astype(int)
            df['secondary_scorer'] = ((df['scoring_percentile'] >= 0.6) & (df['scoring_percentile'] < 0.8)).astype(int)
    
    # Age-based features
    if 'age' in df.columns:
        df['rookie'] = (df['age'] <= 22).astype(int)
        df['veteran'] = (df['age'] >= 30).astype(int)
        df['prime_age'] = ((df['age'] >= 25) & (df['age'] <= 29)).astype(int)
    
    # Season progression features
    if 'games_played' in df.columns:
        df['season_fatigue'] = np.minimum(df['games_played'] / 30.0, 1.0)  # Normalize to 30 games
        df['early_season'] = (df['games_played'] <= 10).astype(int)
        df['late_season'] = (df['games_played'] >= 25).astype(int)
    
    return df


def create_interaction_features(df: pd.DataFrame, feature_pairs: List[tuple]) -> pd.DataFrame:
    """
    Create interaction features between specified feature pairs
    
    Args:
        df: DataFrame with features
        feature_pairs: List of tuples specifying feature pairs to interact
    
    Returns:
        DataFrame with interaction features
    """
    df = df.copy()
    
    for feat1, feat2 in feature_pairs:
        if feat1 in df.columns and feat2 in df.columns:
            # Multiplicative interaction
            df[f'{feat1}_x_{feat2}'] = df[feat1] * df[feat2]
            
            # Ratio interaction (if feat2 is not zero)
            if (df[feat2] != 0).all():
                df[f'{feat1}_div_{feat2}'] = df[feat1] / (df[feat2] + 1e-6)
    
    return df


def engineer_all_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Apply all feature engineering functions in the correct order
    
    Args:
        df: Raw DataFrame with basic stats
    
    Returns:
        DataFrame with all engineered features
    """
    print("🔧 Starting feature engineering...")
    
    # Apply feature engineering in order
    df = engineer_temporal_features(df)
    print("✅ Temporal features added")
    
    df = engineer_matchup_features(df)
    print("✅ Matchup features added")
    
    df = engineer_advanced_metrics(df)
    print("✅ Advanced metrics calculated")
    
    df = engineer_contextual_features(df)
    print("✅ Contextual features added")
    
    # Key interaction features for basketball
    basketball_interactions = [
        ('minutes_per_game', 'usage_rate'),
        ('field_goal_percentage', 'shot_quality_index'),
        ('home_advantage', 'points_per_minute'),
        ('days_rest', 'minutes_per_game'),
        ('age', 'usage_rate')
    ]
    
    df = create_interaction_features(df, basketball_interactions)
    print("✅ Interaction features created")
    
    # Fill any remaining NaN values
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df[numeric_columns] = df[numeric_columns].fillna(0)
    
    print(f"🎯 Feature engineering complete! Total features: {len(df.columns)}")
    
    return df


def get_feature_importance_names() -> Dict[str, str]:
    """
    Return a mapping of feature names to human-readable descriptions
    for model interpretation
    """
    return {
        'minutes_per_game': 'Minutes Per Game',
        'field_goal_percentage': 'Field Goal %',
        'free_throw_percentage': 'Free Throw %',
        'usage_rate': 'Usage Rate',
        'usage_rate_ema10': 'Usage Rate (10-game avg)',
        'shot_quality_index': 'Shot Quality Index',
        'days_rest': 'Days Rest',
        'b2b_flag': 'Back-to-Back Game',
        'home_advantage': 'Home Court Advantage',
        'opp_def_rating': 'Opponent Defensive Rating',
        'starter_role': 'Starter Role',
        'primary_scorer': 'Primary Scorer Role',
        'points_consistency': 'Scoring Consistency',
        'defensive_impact': 'Defensive Impact',
        'season_fatigue': 'Season Fatigue Factor',
        'age': 'Player Age',
        'altitude_adjustment': 'Altitude Adjustment'
    }
