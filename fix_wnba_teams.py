"""
WNBA Team Mapping - 2025 Season (13 Teams)
==========================================

Corrects team abbreviations and names to match the actual WNBA roster.
"""

# Official WNBA Teams for 2025 (13 teams)
OFFICIAL_WNBA_TEAMS_2025 = {
    # Eastern Conference (6 teams)
    'ATL': 'Atlanta Dream',
    'CHI': 'Chicago Sky',
    'CON': 'Connecticut Sun',  # Correct
    'IND': 'Indiana Fever',
    'NYL': 'New York Liberty',  # Correct abbreviation
    'WAS': 'Washington Mystics',
    
    # Western Conference (7 teams)
    'DAL': 'Dallas Wings',
    'GSV': 'Golden State Valkyries',  # New in 2025
    'LAS': 'Las Vegas Aces',
    'LA': 'Los Angeles Sparks',
    'MIN': 'Minnesota Lynx',
    'PHX': 'Phoenix Mercury',
    'SEA': 'Seattle Storm',
}

# Mapping of incorrect abbreviations in our data to correct ones
TEAM_ABBREVIATION_FIXES = {
    'LVA': 'LAS',  # Las Vegas Aces (LVA → LAS)
    'LAS': 'LA',   # Los Angeles Sparks (LAS → LA)
    'PHO': 'PHX',  # Phoenix Mercury (PHO → PHX)
    'SAN': 'REMOVE',  # San Antonio - not a current WNBA team
}

def fix_team_abbreviations(df):
    """Fix team abbreviations to match official WNBA roster"""
    import pandas as pd
    
    print("BEFORE fixes:")
    print(f"Teams: {sorted(df['team_abbreviation'].unique())}")
    print(f"Total: {len(df['team_abbreviation'].unique())}")
    
    # Apply fixes in specific order to avoid conflicts
    # 1. First rename LAS to LA (Los Angeles Sparks)
    if 'LAS' in df['team_abbreviation'].unique():
        mask = df['team_abbreviation'] == 'LAS'
        df.loc[mask, 'team_abbreviation'] = 'LA'
        print(f"Fixed LAS → LA (Los Angeles Sparks)")
    
    # 2. Then rename LVA to LAS (Las Vegas Aces)
    if 'LVA' in df['team_abbreviation'].unique():
        mask = df['team_abbreviation'] == 'LVA'
        df.loc[mask, 'team_abbreviation'] = 'LAS'
        print(f"Fixed LVA → LAS (Las Vegas Aces)")
    
    # 3. Fix Phoenix
    if 'PHO' in df['team_abbreviation'].unique():
        mask = df['team_abbreviation'] == 'PHO'
        df.loc[mask, 'team_abbreviation'] = 'PHX'
        print(f"Fixed PHO → PHX (Phoenix Mercury)")
    
    # 4. Remove San Antonio (not a current WNBA team)
    if 'SAN' in df['team_abbreviation'].unique():
        df = df[df['team_abbreviation'] != 'SAN']
        print(f"Removed SAN (not a current WNBA team)")
    
    print("\nAFTER fixes:")
    print(f"Teams: {sorted(df['team_abbreviation'].unique())}")
    print(f"Total: {len(df['team_abbreviation'].unique())}")
    
    # Verify all teams are official
    current_teams = set(df['team_abbreviation'].unique())
    official_teams = set(OFFICIAL_WNBA_TEAMS_2025.keys())
    
    print(f"\nValidation:")
    print(f"Official WNBA teams: {len(official_teams)}")
    print(f"Teams in data: {len(current_teams)}")
    
    if current_teams == official_teams:
        print("✅ All teams match official WNBA roster!")
    else:
        missing = official_teams - current_teams
        extra = current_teams - official_teams
        if missing:
            print(f"❌ Missing teams: {missing}")
        if extra:
            print(f"❌ Extra teams: {extra}")
    
    return df

if __name__ == "__main__":
    import pandas as pd
    
    # Load and fix the data
    df = pd.read_csv('consolidated_wnba/01_player_data/basic_stats/complete_real_wnba_features_with_metadata.csv')
    df_fixed = fix_team_abbreviations(df)
    
    # Save the corrected data
    df_fixed.to_csv('consolidated_wnba/01_player_data/basic_stats/complete_real_wnba_features_with_metadata_fixed.csv', index=False)
    print(f"\n✅ Fixed data saved to complete_real_wnba_features_with_metadata_fixed.csv")
    
    # Show team distribution
    print("\nTeam distribution:")
    team_counts = df_fixed['team_abbreviation'].value_counts().sort_index()
    for team, count in team_counts.items():
        team_name = OFFICIAL_WNBA_TEAMS_2025.get(team, 'Unknown')
        print(f"{team}: {count} players ({team_name})")
