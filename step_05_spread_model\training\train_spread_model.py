"""
Spread Model Training Script
============================

Main training script for the margin-sensitive spread prediction model.
Integrates with existing Totals and Moneyline models from previous steps.
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import argparse
import os
from typing import Dict, Optional
import logging
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.spread_model import SpreadModel
from data_processing.spread_data_module import SpreadDataModule
from utils.spread_metrics import SpreadMetrics, SpreadEvaluator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_callbacks(checkpoint_dir: str = "step_05_checkpoints") -> list:
    """Create training callbacks"""
    callbacks = []
    
    # Model checkpoint callback
    checkpoint_callback = ModelCheckpoint(
        dirpath=checkpoint_dir,
        filename='spread-model-{epoch:02d}-{val_loss:.2f}',
        monitor='val_loss',
        mode='min',
        save_top_k=3,
        save_last=True,
        verbose=True
    )
    callbacks.append(checkpoint_callback)
    
    # Early stopping callback
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        mode='min',
        verbose=True,
        min_delta=0.001
    )
    callbacks.append(early_stopping)
    
    return callbacks


def load_base_models(totals_model_path: Optional[str] = None,
                    moneyline_model_path: Optional[str] = None) -> tuple:
    """Load pre-trained totals and moneyline models"""
    totals_model = None
    moneyline_model = None
    
    if totals_model_path and os.path.exists(totals_model_path):
        try:
            # This would load the actual totals model from step 2
            # For now, we'll use a placeholder
            logger.info(f"Loading totals model from {totals_model_path}")
            # totals_model = TotalsModel.load_from_checkpoint(totals_model_path)
        except Exception as e:
            logger.warning(f"Failed to load totals model: {e}")
    
    if moneyline_model_path and os.path.exists(moneyline_model_path):
        try:
            # This would load the actual moneyline model from step 3
            # For now, we'll use a placeholder
            logger.info(f"Loading moneyline model from {moneyline_model_path}")
            # moneyline_model = MoneylineModel.load_from_checkpoint(moneyline_model_path)
        except Exception as e:
            logger.warning(f"Failed to load moneyline model: {e}")
    
    return totals_model, moneyline_model


def train_spread_model(
    consolidated_data_path: str = "../consolidated_wnba",
    totals_model_path: Optional[str] = None,
    moneyline_model_path: Optional[str] = None,
    max_epochs: int = 50,
    batch_size: int = 32,
    learning_rate: float = 1e-3,
    hidden_dim: int = 256,
    dropout: float = 0.2,
    checkpoint_dir: str = "step_05_checkpoints",
    log_dir: str = "step_05_logs",
    use_demo_data: bool = False,
    **kwargs
) -> SpreadModel:
    """
    Train the spread prediction model using unified data structure
    
    Args:
        totals_model_path: Path to pre-trained totals model
        moneyline_model_path: Path to pre-trained moneyline model
        max_epochs: Maximum number of training epochs
        batch_size: Training batch size
        learning_rate: Learning rate for optimization
        hidden_dim: Hidden dimension size
        dropout: Dropout rate
        checkpoint_dir: Directory to save model checkpoints
        log_dir: Directory for training logs
        use_unified_data: Whether to use unified data from consolidated_wnba
        **kwargs: Additional arguments
    
    Returns:
        Trained SpreadModel
    """
    
    # Create directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Load base models
    totals_model, moneyline_model = load_base_models(
        totals_model_path, moneyline_model_path
    )
    
    # Initialize data module with unified data
    data_module = SpreadDataModule(
        consolidated_data_path=consolidated_data_path,
        batch_size=batch_size,
        use_demo_data=use_demo_data
    )
    
    # Setup data
    data_module.setup()
    
    # Initialize model
    model = SpreadModel(
        totals_model=totals_model,
        moneyline_model=moneyline_model,
        input_dim=data_module.get_input_dim(),
        hidden_dim=hidden_dim,
        dropout=dropout,
        learning_rate=learning_rate,
        **kwargs
    )
    
    # Create callbacks
    callbacks = create_callbacks(checkpoint_dir)
    
    # Initialize logger
    logger_tb = TensorBoardLogger(
        save_dir=log_dir,
        name="spread_model",
        version=None
    )
    
    # Initialize trainer
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        callbacks=callbacks,
        logger=logger_tb,
        accelerator='auto',
        devices='auto',
        deterministic=True,
        enable_progress_bar=True,
        log_every_n_steps=10
    )
    
    # Train model
    logger.info("Starting spread model training...")
    trainer.fit(model, data_module)
    
    # Load best model
    best_model_path = callbacks[0].best_model_path
    if best_model_path:
        logger.info(f"Loading best model from {best_model_path}")
        model = SpreadModel.load_from_checkpoint(best_model_path)
    
    # Evaluate model
    logger.info("Evaluating trained model...")
    evaluator = SpreadEvaluator(model)
    
    # Validation metrics
    val_metrics = evaluator.evaluate_model(data_module.val_dataloader())
    logger.info("Validation Metrics:")
    for key, value in val_metrics.items():
        logger.info(f"  {key}: {value:.4f}")
    
    # Test metrics
    test_metrics = evaluator.evaluate_model(data_module.test_dataloader())
    logger.info("Test Metrics:")
    for key, value in test_metrics.items():
        logger.info(f"  {key}: {value:.4f}")
    
    # Generate evaluation report
    report = evaluator.generate_evaluation_report(test_metrics)
    logger.info(f"\n{report}")
    
    # Save evaluation report
    report_path = os.path.join(checkpoint_dir, "evaluation_report.txt")
    with open(report_path, 'w') as f:
        f.write(report)
    
    return model


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train Spread Model')
    
    # Data arguments
    parser.add_argument('--consolidated-data-path', type=str, default='../consolidated_wnba',
                       help='Path to consolidated WNBA data')
    parser.add_argument('--use-demo-data', action='store_true',
                       help='Use demo data for training')
    
    # Model arguments
    parser.add_argument('--totals-model-path', type=str, default=None,
                       help='Path to pre-trained totals model')
    parser.add_argument('--moneyline-model-path', type=str, default=None,
                       help='Path to pre-trained moneyline model')
    parser.add_argument('--hidden-dim', type=int, default=256,
                       help='Hidden dimension size')
    parser.add_argument('--dropout', type=float, default=0.2,
                       help='Dropout rate')
    
    # Training arguments
    parser.add_argument('--max-epochs', type=int, default=50,
                       help='Maximum number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='Training batch size')
    parser.add_argument('--learning-rate', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-4,
                       help='Weight decay')
    
    # Output arguments
    parser.add_argument('--checkpoint-dir', type=str, default='step_05_checkpoints',
                       help='Directory to save model checkpoints')
    parser.add_argument('--log-dir', type=str, default='step_05_logs',
                       help='Directory for training logs')
    
    args = parser.parse_args()
    
    # Train model
    trained_model = train_spread_model(
        consolidated_data_path=args.consolidated_data_path,
        totals_model_path=args.totals_model_path,
        moneyline_model_path=args.moneyline_model_path,
        max_epochs=args.max_epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        hidden_dim=args.hidden_dim,
        dropout=args.dropout,
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir,
        use_demo_data=args.use_demo_data,
        weight_decay=args.weight_decay
    )
    
    logger.info("Training completed successfully!")
    return trained_model


if __name__ == "__main__":
    main()
