"""
Rebound Metrics and Evaluation (Step 4 of 9)
=============================================

This module implements rebound-specific metrics and evaluation utilities:
- Zero-Inflated Poisson evaluation metrics
- Position-weighted evaluation
- Rebounding efficiency metrics
- Uncertainty quantification
- Model performance benchmarking

Key Components:
- ReboundMetrics: Comprehensive evaluation metrics
- PositionWeightedEvaluator: Position-specific performance assessment
- ZeroInflatedEvaluator: ZIP-specific evaluation
- ReboundBenchmarks: Performance benchmarking utilities
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ZeroInflatedEvaluator:
    """
    Evaluation metrics specific to Zero-Inflated Poisson models.
    
    Provides specialized metrics for evaluating ZIP model performance,
    including zero-inflation assessment and count distribution analysis.
    """
    
    def __init__(self):
        self.epsilon = 1e-8
        
    def zero_inflation_metrics(self, y_true: np.ndarray, gate_probs: np.ndarray,
                              poisson_rates: np.ndarray) -> Dict[str, float]:
        """
        Calculate zero-inflation specific metrics.
        
        Args:
            y_true: True target values
            gate_probs: Predicted zero-inflation probabilities
            poisson_rates: Predicted Poisson rates
            
        Returns:
            Dictionary of zero-inflation metrics
        """
        # Observed zero rate
        observed_zero_rate = np.mean(y_true == 0)
        
        # Predicted zero rate
        poisson_zero_prob = np.exp(-poisson_rates)
        predicted_zero_rate = np.mean(gate_probs + (1 - gate_probs) * poisson_zero_prob)
        
        # Zero prediction accuracy
        zero_predictions = (gate_probs + (1 - gate_probs) * poisson_zero_prob) > 0.5
        zero_accuracy = np.mean((y_true == 0) == zero_predictions)
        
        # Gate probability calibration
        gate_calibration = np.abs(np.mean(gate_probs) - observed_zero_rate)
        
        return {
            'observed_zero_rate': observed_zero_rate,
            'predicted_zero_rate': predicted_zero_rate,
            'zero_rate_error': abs(observed_zero_rate - predicted_zero_rate),
            'zero_accuracy': zero_accuracy,
            'gate_calibration': gate_calibration,
            'mean_gate_prob': np.mean(gate_probs),
            'mean_poisson_rate': np.mean(poisson_rates)
        }
    
    def count_distribution_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        Evaluate count distribution alignment.
        
        Args:
            y_true: True count values
            y_pred: Predicted count values
            
        Returns:
            Dictionary of distribution metrics
        """
        # Count distribution comparison
        true_counts = np.bincount(y_true.astype(int))
        pred_counts = np.bincount(y_pred.astype(int))
        
        # Align lengths
        max_len = max(len(true_counts), len(pred_counts))
        true_counts = np.pad(true_counts, (0, max_len - len(true_counts)))
        pred_counts = np.pad(pred_counts, (0, max_len - len(pred_counts)))
        
        # Chi-square test
        chi2_stat, chi2_pval = stats.chisquare(pred_counts + 1, true_counts + 1)
        
        # KL divergence
        true_dist = (true_counts + 1) / (np.sum(true_counts) + len(true_counts))
        pred_dist = (pred_counts + 1) / (np.sum(pred_counts) + len(pred_counts))
        kl_div = stats.entropy(true_dist, pred_dist)
        
        return {
            'chi2_statistic': chi2_stat,
            'chi2_pvalue': chi2_pval,
            'kl_divergence': kl_div,
            'max_count_true': np.max(y_true),
            'max_count_pred': np.max(y_pred),
            'mean_count_true': np.mean(y_true),
            'mean_count_pred': np.mean(y_pred),
            'std_count_true': np.std(y_true),
            'std_count_pred': np.std(y_pred)
        }
    
    def likelihood_metrics(self, y_true: np.ndarray, gate_probs: np.ndarray,
                          poisson_rates: np.ndarray) -> Dict[str, float]:
        """
        Calculate likelihood-based metrics.
        
        Args:
            y_true: True target values
            gate_probs: Predicted zero-inflation probabilities
            poisson_rates: Predicted Poisson rates
            
        Returns:
            Dictionary of likelihood metrics
        """
        # Zero-inflated Poisson likelihood
        poisson_zero_prob = np.exp(-poisson_rates)
        total_zero_prob = gate_probs + (1 - gate_probs) * poisson_zero_prob
        
        # Calculate log-likelihood
        log_likelihood = 0
        for i in range(len(y_true)):
            if y_true[i] == 0:
                log_likelihood += np.log(total_zero_prob[i] + self.epsilon)
            else:
                log_likelihood += (
                    np.log(1 - gate_probs[i] + self.epsilon) +
                    y_true[i] * np.log(poisson_rates[i] + self.epsilon) -
                    poisson_rates[i] -
                    np.sum(np.log(np.arange(1, y_true[i] + 1)))
                )
        
        # AIC and BIC (approximate)
        n_params = 2  # gate and rate parameters
        n_obs = len(y_true)
        aic = -2 * log_likelihood + 2 * n_params
        bic = -2 * log_likelihood + n_params * np.log(n_obs)
        
        return {
            'log_likelihood': log_likelihood,
            'aic': aic,
            'bic': bic,
            'average_log_likelihood': log_likelihood / n_obs
        }


class PositionWeightedEvaluator:
    """
    Position-weighted evaluation for rebound models.
    
    Provides position-specific performance assessment, recognizing that
    different positions have different rebounding expectations and importance.
    """
    
    def __init__(self, position_weights: Optional[Dict[str, float]] = None):
        # Default position weights (Centers and Forwards are more important for rebounds)
        self.position_weights = position_weights or {
            'Guard': 0.8,      # Guards: lower expectation
            'Forward': 1.2,    # Forwards: higher expectation
            'Center': 1.5      # Centers: highest expectation
        }
        
        # Position indices mapping
        self.position_map = {'Guard': 0, 'Forward': 1, 'Center': 2}
        
    def position_specific_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                                 positions: np.ndarray) -> Dict[str, Dict[str, float]]:
        """
        Calculate metrics for each position separately.
        
        Args:
            y_true: True target values
            y_pred: Predicted values
            positions: Position indices (0=Guard, 1=Forward, 2=Center)
            
        Returns:
            Dictionary of position-specific metrics
        """
        position_names = ['Guard', 'Forward', 'Center']
        results = {}
        
        for pos_idx, pos_name in enumerate(position_names):
            mask = positions == pos_idx
            if np.sum(mask) == 0:
                continue
                
            pos_true = y_true[mask]
            pos_pred = y_pred[mask]
            
            results[pos_name] = {
                'count': np.sum(mask),
                'mae': mean_absolute_error(pos_true, pos_pred),
                'mse': mean_squared_error(pos_true, pos_pred),
                'rmse': np.sqrt(mean_squared_error(pos_true, pos_pred)),
                'r2': r2_score(pos_true, pos_pred) if len(np.unique(pos_true)) > 1 else 0,
                'mean_true': np.mean(pos_true),
                'mean_pred': np.mean(pos_pred),
                'std_true': np.std(pos_true),
                'std_pred': np.std(pos_pred),
                'zero_rate_true': np.mean(pos_true == 0),
                'zero_rate_pred': np.mean(pos_pred == 0)
            }
        
        return results
    
    def weighted_overall_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                               positions: np.ndarray) -> Dict[str, float]:
        """
        Calculate position-weighted overall metrics.
        
        Args:
            y_true: True target values
            y_pred: Predicted values
            positions: Position indices
            
        Returns:
            Dictionary of weighted metrics
        """
        position_names = ['Guard', 'Forward', 'Center']
        
        # Calculate weights for each sample
        sample_weights = np.ones(len(y_true))
        for pos_idx, pos_name in enumerate(position_names):
            mask = positions == pos_idx
            sample_weights[mask] = self.position_weights[pos_name]
        
        # Weighted errors
        weighted_errors = np.abs(y_true - y_pred) * sample_weights
        weighted_squared_errors = (y_true - y_pred) ** 2 * sample_weights
        
        # Weighted metrics
        weighted_mae = np.mean(weighted_errors)
        weighted_mse = np.mean(weighted_squared_errors)
        weighted_rmse = np.sqrt(weighted_mse)
        
        # Position balance
        position_counts = [np.sum(positions == i) for i in range(3)]
        position_balance = np.min(position_counts) / np.max(position_counts) if np.max(position_counts) > 0 else 0
        
        return {
            'weighted_mae': weighted_mae,
            'weighted_mse': weighted_mse,
            'weighted_rmse': weighted_rmse,
            'position_balance': position_balance,
            'total_samples': len(y_true)
        }


class ReboundMetrics:
    """
    Comprehensive rebound-specific metrics and evaluation.
    
    Combines standard regression metrics with rebound-specific evaluations
    including efficiency, opportunity utilization, and contextual performance.
    """
    
    def __init__(self):
        self.zip_evaluator = ZeroInflatedEvaluator()
        self.position_evaluator = PositionWeightedEvaluator()
        
    def standard_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate standard regression metrics."""
        return {
            'mae': mean_absolute_error(y_true, y_pred),
            'mse': mean_squared_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'r2': r2_score(y_true, y_pred) if len(np.unique(y_true)) > 1 else 0,
            'mean_true': np.mean(y_true),
            'mean_pred': np.mean(y_pred),
            'std_true': np.std(y_true),
            'std_pred': np.std(y_pred),
            'correlation': np.corrcoef(y_true, y_pred)[0, 1] if len(np.unique(y_true)) > 1 else 0
        }
    
    def rebound_specific_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                               minutes: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Calculate rebound-specific performance metrics.
        
        Args:
            y_true: True rebound counts
            y_pred: Predicted rebound counts
            minutes: Playing minutes (optional)
            
        Returns:
            Dictionary of rebound-specific metrics
        """
        metrics = {}
        
        # Efficiency metrics
        if minutes is not None:
            true_rate = y_true / (minutes + 1e-8)
            pred_rate = y_pred / (minutes + 1e-8)
            metrics['rate_mae'] = mean_absolute_error(true_rate, pred_rate)
            metrics['rate_correlation'] = np.corrcoef(true_rate, pred_rate)[0, 1]
        
        # Count distribution metrics
        metrics['zero_count_accuracy'] = np.mean((y_true == 0) == (y_pred < 0.5))
        metrics['high_rebound_recall'] = np.mean(y_pred[y_true >= 10] >= 8) if np.sum(y_true >= 10) > 0 else 0
        
        # Prediction intervals
        residuals = y_true - y_pred
        metrics['prediction_interval_50'] = np.percentile(np.abs(residuals), 50)
        metrics['prediction_interval_80'] = np.percentile(np.abs(residuals), 80)
        metrics['prediction_interval_95'] = np.percentile(np.abs(residuals), 95)
        
        # Bias analysis
        metrics['mean_bias'] = np.mean(residuals)
        metrics['median_bias'] = np.median(residuals)
        metrics['bias_std'] = np.std(residuals)
        
        return metrics
    
    def uncertainty_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                          y_std: np.ndarray) -> Dict[str, float]:
        """
        Evaluate uncertainty quantification quality.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            y_std: Predicted standard deviations
            
        Returns:
            Dictionary of uncertainty metrics
        """
        # Normalized residuals
        normalized_residuals = (y_true - y_pred) / (y_std + 1e-8)
        
        # Calibration metrics
        metrics = {
            'uncertainty_correlation': np.corrcoef(np.abs(y_true - y_pred), y_std)[0, 1],
            'normalized_residual_mean': np.mean(normalized_residuals),
            'normalized_residual_std': np.std(normalized_residuals),
            'coverage_50': np.mean(np.abs(normalized_residuals) <= 0.67),  # Should be ~0.5
            'coverage_80': np.mean(np.abs(normalized_residuals) <= 1.28),  # Should be ~0.8
            'coverage_95': np.mean(np.abs(normalized_residuals) <= 1.96),  # Should be ~0.95
            'mean_uncertainty': np.mean(y_std),
            'uncertainty_range': np.max(y_std) - np.min(y_std)
        }
        
        return metrics
    
    def comprehensive_evaluation(self, y_true: np.ndarray, y_pred: np.ndarray,
                               positions: np.ndarray, gate_probs: np.ndarray,
                               poisson_rates: np.ndarray, y_std: Optional[np.ndarray] = None,
                               minutes: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Comprehensive evaluation combining all metrics.
        
        Args:
            y_true: True rebound counts
            y_pred: Predicted rebound counts
            positions: Position indices
            gate_probs: Zero-inflation probabilities
            poisson_rates: Poisson rate parameters
            y_std: Prediction uncertainties (optional)
            minutes: Playing minutes (optional)
            
        Returns:
            Dictionary of comprehensive evaluation results
        """
        results = {}
        
        # Standard metrics
        results['standard'] = self.standard_metrics(y_true, y_pred)
        
        # Rebound-specific metrics
        results['rebound_specific'] = self.rebound_specific_metrics(y_true, y_pred, minutes)
        
        # Zero-inflated metrics
        results['zero_inflated'] = self.zip_evaluator.zero_inflation_metrics(
            y_true, gate_probs, poisson_rates
        )
        results['count_distribution'] = self.zip_evaluator.count_distribution_metrics(
            y_true, y_pred
        )
        results['likelihood'] = self.zip_evaluator.likelihood_metrics(
            y_true, gate_probs, poisson_rates
        )
        
        # Position-specific metrics
        results['position_specific'] = self.position_evaluator.position_specific_metrics(
            y_true, y_pred, positions
        )
        results['position_weighted'] = self.position_evaluator.weighted_overall_metrics(
            y_true, y_pred, positions
        )
        
        # Uncertainty metrics
        if y_std is not None:
            results['uncertainty'] = self.uncertainty_metrics(y_true, y_pred, y_std)
        
        return results


class ReboundBenchmarks:
    """
    Benchmarking utilities for rebound model performance.
    
    Provides baseline comparisons and performance benchmarks
    for evaluating model effectiveness.
    """
    
    def __init__(self):
        self.baselines = {}
        
    def create_baselines(self, y_true: np.ndarray, positions: np.ndarray,
                        minutes: Optional[np.ndarray] = None) -> Dict[str, np.ndarray]:
        """
        Create baseline predictions for comparison.
        
        Args:
            y_true: True rebound counts
            positions: Position indices
            minutes: Playing minutes (optional)
            
        Returns:
            Dictionary of baseline predictions
        """
        baselines = {}
        
        # Overall mean baseline
        baselines['overall_mean'] = np.full_like(y_true, np.mean(y_true), dtype=float)
        
        # Position-specific mean baseline
        position_means = {}
        for pos in np.unique(positions):
            position_means[pos] = np.mean(y_true[positions == pos])
        
        position_baseline = np.zeros_like(y_true, dtype=float)
        for pos in np.unique(positions):
            position_baseline[positions == pos] = position_means[pos]
        baselines['position_mean'] = position_baseline
        
        # Minutes-adjusted baseline
        if minutes is not None:
            # Linear relationship between minutes and rebounds
            from sklearn.linear_model import LinearRegression
            lr = LinearRegression()
            lr.fit(minutes.reshape(-1, 1), y_true)
            baselines['minutes_linear'] = lr.predict(minutes.reshape(-1, 1))
            
            # Position + minutes baseline
            position_minutes_baseline = np.zeros_like(y_true, dtype=float)
            for pos in np.unique(positions):
                pos_mask = positions == pos
                if np.sum(pos_mask) > 1:
                    pos_lr = LinearRegression()
                    pos_lr.fit(minutes[pos_mask].reshape(-1, 1), y_true[pos_mask])
                    position_minutes_baseline[pos_mask] = pos_lr.predict(minutes[pos_mask].reshape(-1, 1))
                else:
                    position_minutes_baseline[pos_mask] = position_means[pos]
            baselines['position_minutes'] = position_minutes_baseline
        
        # Random baseline (for reference)
        np.random.seed(42)
        baselines['random'] = np.random.poisson(np.mean(y_true), size=len(y_true))
        
        self.baselines = baselines
        return baselines
    
    def benchmark_comparison(self, y_true: np.ndarray, y_pred: np.ndarray,
                           positions: np.ndarray, minutes: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Compare model performance against baselines.
        
        Args:
            y_true: True rebound counts
            y_pred: Model predictions
            positions: Position indices
            minutes: Playing minutes (optional)
            
        Returns:
            Dictionary of benchmark comparisons
        """
        # Create baselines
        baselines = self.create_baselines(y_true, positions, minutes)
        
        # Evaluate each baseline
        metrics = ReboundMetrics()
        results = {}
        
        # Model performance
        results['model'] = metrics.standard_metrics(y_true, y_pred)
        
        # Baseline performance
        for baseline_name, baseline_pred in baselines.items():
            results[baseline_name] = metrics.standard_metrics(y_true, baseline_pred)
        
        # Improvement over baselines
        improvements = {}
        for baseline_name in baselines.keys():
            improvements[baseline_name] = {
                'mae_improvement': (results[baseline_name]['mae'] - results['model']['mae']) / results[baseline_name]['mae'],
                'mse_improvement': (results[baseline_name]['mse'] - results['model']['mse']) / results[baseline_name]['mse'],
                'r2_improvement': results['model']['r2'] - results[baseline_name]['r2']
            }
        
        results['improvements'] = improvements
        return results
    
    def performance_summary(self, evaluation_results: Dict[str, Any]) -> str:
        """
        Generate a human-readable performance summary.
        
        Args:
            evaluation_results: Results from comprehensive_evaluation
            
        Returns:
            Formatted performance summary string
        """
        summary = []
        summary.append("=== REBOUND MODEL PERFORMANCE SUMMARY ===")
        summary.append("")
        
        # Standard metrics
        std_metrics = evaluation_results['standard']
        summary.append(f"Standard Metrics:")
        summary.append(f"  MAE: {std_metrics['mae']:.3f}")
        summary.append(f"  RMSE: {std_metrics['rmse']:.3f}")
        summary.append(f"  R²: {std_metrics['r2']:.3f}")
        summary.append(f"  Correlation: {std_metrics['correlation']:.3f}")
        summary.append("")
        
        # Zero-inflated metrics
        zip_metrics = evaluation_results['zero_inflated']
        summary.append(f"Zero-Inflated Metrics:")
        summary.append(f"  Zero Rate (True): {zip_metrics['observed_zero_rate']:.3f}")
        summary.append(f"  Zero Rate (Pred): {zip_metrics['predicted_zero_rate']:.3f}")
        summary.append(f"  Zero Accuracy: {zip_metrics['zero_accuracy']:.3f}")
        summary.append(f"  Gate Calibration: {zip_metrics['gate_calibration']:.3f}")
        summary.append("")
        
        # Position-specific performance
        pos_metrics = evaluation_results['position_specific']
        summary.append(f"Position-Specific Performance:")
        for pos_name, metrics in pos_metrics.items():
            summary.append(f"  {pos_name}:")
            summary.append(f"    Count: {metrics['count']}")
            summary.append(f"    MAE: {metrics['mae']:.3f}")
            summary.append(f"    R²: {metrics['r2']:.3f}")
            summary.append(f"    Mean (True/Pred): {metrics['mean_true']:.2f}/{metrics['mean_pred']:.2f}")
        summary.append("")
        
        # Weighted metrics
        weighted_metrics = evaluation_results['position_weighted']
        summary.append(f"Position-Weighted Metrics:")
        summary.append(f"  Weighted MAE: {weighted_metrics['weighted_mae']:.3f}")
        summary.append(f"  Weighted RMSE: {weighted_metrics['weighted_rmse']:.3f}")
        summary.append(f"  Position Balance: {weighted_metrics['position_balance']:.3f}")
        summary.append("")
        
        return "\n".join(summary)
