"""
Threes Model Training Script (Step 7 of 9)
==========================================

Training script for the spatial three-point shooting model with comprehensive validation.

Features:
- Spatial shooting architecture training
- Court-space efficiency validation
- Shooting form analysis
- Unified data integration
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, Optional

import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import (
    ModelCheckpoint, 
    EarlyStopping, 
    LearningRateMonitor,
    StochasticWeightAveraging
)
from pytorch_lightning.loggers import TensorBoardLogger

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from step_07_threes_model.models.threes_model import ThreePointModel, ShootingFormAnalyzer
from step_07_threes_model.data_processing.threes_data_module import ThreesDataModule

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def train_threes_model(
    data_path: Optional[str] = None,
    max_epochs: int = 150,
    batch_size: int = 64,
    learning_rate: float = 1e-3,
    spatial_dim: int = 16,
    hidden_dims: list = [256, 128],
    dropout: float = 0.3,
    early_stopping_patience: int = 20,
    checkpoint_dir: str = 'step_07_checkpoints',
    log_dir: str = 'step_07_logs',
    use_unified_data: bool = True
) -> Dict[str, Any]:
    """
    Train the WNBA Three-Point Shooting Model with spatial architecture.
    
    Args:
        data_path: Path to training data (None for unified data)
        max_epochs: Maximum training epochs
        batch_size: Training batch size
        learning_rate: Learning rate
        spatial_dim: Spatial encoder dimension
        hidden_dims: Hidden layer dimensions
        dropout: Dropout rate
        early_stopping_patience: Early stopping patience
        checkpoint_dir: Directory for model checkpoints
        log_dir: Directory for TensorBoard logs
        use_unified_data: Whether to use unified data
    
    Returns:
        Dictionary with training results and model path
    """
    
    print("🏀 Training WNBA Three-Point Shooting Model")
    print("=" * 60)
    
    # Create directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Determine data path
    if use_unified_data and data_path is None:
        unified_data_dir = Path(__file__).parent.parent.parent / "unified_data"
        data_path = str(unified_data_dir / "unified_player_data.csv")
        print(f"📊 Using unified data: {data_path}")
    elif data_path is None:
        raise ValueError("data_path must be provided if use_unified_data=False")
    
    # Set up data module
    print("📊 Setting up data module...")
    data_module = ThreesDataModule(
        data_path=data_path,
        batch_size=batch_size,
        use_unified_data=use_unified_data
    )
    
    # Setup data to get feature dimensions
    data_module.setup()
    
    # Get input dimension from sample batch
    sample_batch = next(iter(data_module.train_dataloader()))
    player_features, shot_profile, targets = sample_batch
    input_dim = player_features.shape[1]
    
    print(f"📏 Input dimensions: {input_dim} features, {shot_profile.shape[1]} spatial features")
    
    # Initialize model
    print("🧠 Initializing Three-Point Model...")
    model = ThreePointModel(
        input_dim=input_dim,
        spatial_dim=spatial_dim,
        hidden_dims=hidden_dims,
        dropout=dropout,
        learning_rate=learning_rate
    )
    
    # Setup callbacks
    checkpoint_callback = ModelCheckpoint(
        dirpath=checkpoint_dir,
        filename='threes-model-{epoch:02d}-{val_loss:.2f}',
        monitor='val_loss',
        mode='min',
        save_top_k=3,
        save_last=True
    )
    
    early_stopping_callback = EarlyStopping(
        monitor='val_loss',
        patience=early_stopping_patience,
        mode='min',
        verbose=True
    )
    
    lr_monitor = LearningRateMonitor(logging_interval='epoch')
    
    swa_callback = StochasticWeightAveraging(swa_lrs=1e-4)
    
    # Setup logger
    tb_logger = TensorBoardLogger(
        save_dir=log_dir,
        name='threes_model',
        version=None
    )
    
    # Initialize trainer
    print("🚀 Initializing trainer...")
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        accelerator='auto',
        devices=1,
        logger=tb_logger,
        callbacks=[
            checkpoint_callback,
            early_stopping_callback,
            lr_monitor,
            swa_callback
        ],
        gradient_clip_val=1.0,
        deterministic=True,
        enable_progress_bar=True
    )
    
    # Train model
    print("🎯 Starting training...")
    trainer.fit(model, data_module)
    
    # Test model
    print("🧪 Testing model...")
    test_results = trainer.test(model, data_module, ckpt_path='best')
    
    # Load best model for analysis
    best_model = ThreePointModel.load_from_checkpoint(
        checkpoint_callback.best_model_path,
        input_dim=input_dim,
        spatial_dim=spatial_dim,
        hidden_dims=hidden_dims,
        dropout=dropout,
        learning_rate=learning_rate
    )
    
    # Generate shooting form analysis
    print("📊 Generating shooting form analysis...")
    analyzer = ShootingFormAnalyzer()
    
    # Analyze a sample player
    sample_player = player_features[0:1]  # First player
    efficiency_heatmap = analyzer.analyze_shooting_form(sample_player, best_model)
    
    print(f"📈 Generated efficiency heatmap: {efficiency_heatmap.shape}")
    
    # Generate comprehensive report
    print("📋 Generating validation report...")
    
    # Calculate validation benchmarks
    val_metrics = {}
    for key, value in test_results[0].items():
        if key.startswith('test_'):
            metric_name = key.replace('test_', '')
            val_metrics[metric_name] = value
    
    # Benchmark targets
    benchmarks = {
        'makes_mae': 1.5,      # Target: < 1.5 makes MAE
        'attempts_mae': 2.0,   # Target: < 2.0 attempts MAE
        'pct_mae': 0.08,       # Target: < 8% shooting percentage MAE
        'volume_correlation': 0.70,  # Target: > 70% volume correlation
        'efficiency_consistency': 0.80  # Target: > 80% efficiency consistency
    }
    
    # Check benchmarks
    benchmarks_passed = {}
    for metric, target in benchmarks.items():
        if metric in val_metrics:
            if metric == 'volume_correlation' or metric == 'efficiency_consistency':
                passed = val_metrics[metric] >= target
            else:
                passed = val_metrics[metric] <= target
            benchmarks_passed[metric] = passed
            
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {metric}: {val_metrics[metric]:.4f} (target: {target}) {status}")
    
    overall_pass = all(benchmarks_passed.values())
    
    print(f"\n🎯 Overall Benchmark Status: {'✅ PASS' if overall_pass else '❌ FAIL'}")
    print(f"💾 Best model saved to: {checkpoint_callback.best_model_path}")
    
    return {
        'model': best_model,
        'trainer': trainer,
        'model_path': checkpoint_callback.best_model_path,
        'test_results': test_results,
        'validation_metrics': val_metrics,
        'benchmarks_passed': overall_pass,
        'efficiency_heatmap': efficiency_heatmap
    }


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description='Train WNBA Three-Point Shooting Model')
    
    # Data parameters
    parser.add_argument('--data_path', type=str, default=None,
                       help='Path to training data (None for unified data)')
    parser.add_argument('--use_unified_data', action='store_true', default=True,
                       help='Use unified data')
    
    # Training parameters
    parser.add_argument('--max_epochs', type=int, default=150,
                       help='Maximum training epochs')
    parser.add_argument('--batch_size', type=int, default=64,
                       help='Training batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--early_stopping_patience', type=int, default=20,
                       help='Early stopping patience')
    
    # Model parameters
    parser.add_argument('--spatial_dim', type=int, default=16,
                       help='Spatial encoder dimension')
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[256, 128],
                       help='Hidden layer dimensions')
    parser.add_argument('--dropout', type=float, default=0.3,
                       help='Dropout rate')
    
    # Output parameters
    parser.add_argument('--checkpoint_dir', type=str, default='step_07_checkpoints',
                       help='Directory for model checkpoints')
    parser.add_argument('--log_dir', type=str, default='step_07_logs',
                       help='Directory for logs')
    
    args = parser.parse_args()
    
    # Train the model
    results = train_threes_model(
        data_path=args.data_path,
        max_epochs=args.max_epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        spatial_dim=args.spatial_dim,
        hidden_dims=args.hidden_dims,
        dropout=args.dropout,
        early_stopping_patience=args.early_stopping_patience,
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir,
        use_unified_data=args.use_unified_data
    )
    
    print("\n🎉 Training Complete!")
    print(f"💾 Model saved to: {results['model_path']}")
    print(f"📈 TensorBoard logs: {args.log_dir}")
    
    print("\n🔄 Next Steps:")
    print(f"  1. Check TensorBoard logs: tensorboard --logdir {args.log_dir}")
    print("  2. Analyze shooting form heatmaps")
    print("  3. Validate spatial shooting patterns")
    print("  4. Integrate with team offensive efficiency")
    
    if results['benchmarks_passed']:
        print("\n✅ Model is ready for production deployment!")
    else:
        print("\n⚠️  Model needs improvement before production deployment.")


if __name__ == "__main__":
    main()
