"""
🏀 Demo Training Script for Moneyline Model

This script demonstrates the hierarchical moneyline model training using
unified data and the existing Game Totals Model.
"""

import os
import sys
import torch
import pytorch_lightning as pl
from pathlib import Path
import pandas as pd
import numpy as np

# Setup paths using the working pattern
current_dir = Path(__file__).parent
step2_dir = current_dir.parent / 'step_02_game_totals_model'
step1_dir = current_dir.parent / 'step_01_player_points_model'

# Add to sys.path
sys.path.insert(0, str(step2_dir))
sys.path.insert(0, str(step1_dir))
sys.path.insert(0, str(step2_dir / 'models'))
sys.path.insert(0, str(current_dir))  # Add current directory

# Now import with the working pattern
from game_totals_model import GameTotalsModel
sys.path.insert(0, str(current_dir / 'models'))
from moneyline_model import MoneylineModel, generate_moneyline_report
sys.path.insert(0, str(current_dir / 'data_processing'))
from moneyline_data_module import MoneylineDataModule


def load_trained_totals_model():
    """Load the best trained Game Totals Model for hierarchical integration"""
    
    # Path to Step 2 model checkpoints
    checkpoints_dir = Path(__file__).parent.parent / "step_02_game_totals_model" / "step_02_checkpoints"
    
    if not checkpoints_dir.exists():
        print(f"❌ Checkpoints directory not found: {checkpoints_dir}")
        return None
    
    # Find the best checkpoint (lowest validation loss)
    best_checkpoint = None
    best_val_loss = float('inf')
    
    for checkpoint_path in checkpoints_dir.glob("*.ckpt"):
        if "val_loss" in checkpoint_path.name:
            try:
                # Extract validation loss from filename
                val_loss_str = checkpoint_path.name.split("val_loss=")[1].split(".ckpt")[0]
                val_loss = float(val_loss_str)
                
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    best_checkpoint = checkpoint_path
            except (ValueError, IndexError):
                continue
    
    if best_checkpoint is None:
        # Fallback to last checkpoint
        last_checkpoint = checkpoints_dir / "last.ckpt"
        if last_checkpoint.exists():
            best_checkpoint = last_checkpoint
            print("⚠️  Using last checkpoint (no best validation loss found)")
        else:
            print("❌ No trained Game Totals Model found")
            return None
    
    print(f"📁 Loading Game Totals Model from: {best_checkpoint}")
    print(f"📊 Best validation loss: {best_val_loss:.4f}")
    
    try:
        # Import GameTotalsModel
        step2_path = Path(__file__).parent.parent / "step_02_game_totals_model"
        sys.path.append(str(step2_path))
        sys.path.append(str(step2_path / "models"))
        from game_totals_model import GameTotalsModel
        
        # Load the model
        totals_model = GameTotalsModel.load_from_checkpoint(str(best_checkpoint))
        totals_model.eval()
        totals_model.freeze()
        
        print("✅ Game Totals Model loaded successfully")
        return totals_model
        
    except Exception as e:
        print(f"❌ Error loading Game Totals Model: {e}")
        import traceback
        traceback.print_exc()
        return None


def demo_training():
    """Run demonstration training using unified data and trained Game Totals Model"""
    
    print("🏀 WNBA Moneyline Model Demo Training")
    print("=" * 60)
    print("🔗 Using unified data and hierarchical integration")
    
    # Load trained Game Totals Model
    print("\n🤖 Loading trained Game Totals Model...")
    totals_model = load_trained_totals_model()
    
    if totals_model is None:
        print("❌ Cannot proceed without trained Game Totals Model")
        return None
    
    # Setup data module with unified data
    print("\n📊 Setting up data module with unified data...")
    try:
        data_module = MoneylineDataModule(
            batch_size=32,
            num_workers=0  # Windows compatibility
        )
        
        # Setup data
        data_module.setup()
        print("✅ Data module setup complete")
        
        # Get sample batch to determine dimensions
        sample_batch = next(iter(data_module.train_dataloader()))
        team_features, player_features, target = sample_batch
        
        print(f"📊 Sample batch info:")
        print(f"  Team features keys: {list(team_features.keys())}")
        print(f"  Player features shape: {player_features.shape}")
        print(f"  Target shape: {target.shape}")
        
        # Determine input dimension based on actual features
        contextual_dim = team_features['contextual'].shape[1]
        # Calculate actual feature dimensions from forward pass:
        # total_pred: 1, off_advantage: 64, def_advantage: 64, momentum_gap: 1, contextual: 8
        # Total: 1 + 64 + 64 + 1 + 8 = 138
        actual_features_dim = 138
        # But the network expects input_dim + 128, so input_dim should be 138 - 128 = 10
        input_dim = actual_features_dim - 128
        
        print(f"📏 Input dimension: {input_dim}")
        print(f"📏 Contextual features: {contextual_dim}")
        print(f"📏 Actual concatenated features: {actual_features_dim}")
        
        # Initialize Moneyline Model with trained totals model
        print("\n🤖 Initializing Moneyline Model...")
        model = MoneylineModel(
            totals_model=totals_model,
            input_dim=input_dim,
            learning_rate=1e-3,
            hidden_dim=128,  # Smaller for demo
            dropout_rate=0.25
        )
        
        print("✅ Moneyline Model initialized successfully")
        
        # Setup training components
        print("\n🏋️ Setting up training...")
        
        # Import required callbacks
        from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
        from pytorch_lightning.loggers import TensorBoardLogger
        
        # Callbacks
        checkpoint_callback = ModelCheckpoint(
            dirpath='step_03_checkpoints',
            filename='moneyline-{epoch:02d}-{val_loss:.4f}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            verbose=True
        )
        
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=15,
            mode='min',
            verbose=True
        )
        
        lr_monitor = LearningRateMonitor(logging_interval='epoch')
        
        # Logger
        logger = TensorBoardLogger(
            save_dir='step_03_logs',
            name='moneyline_model'
        )
        
        # Trainer
        trainer = pl.Trainer(
            max_epochs=50,  # Reduced for demo
            callbacks=[checkpoint_callback, early_stopping, lr_monitor],
            logger=logger,
            deterministic=True,
            log_every_n_steps=10,
            check_val_every_n_epoch=1
        )
        
        # Train model
        print("\n🚀 Starting training...")
        trainer.fit(model, data_module)
        
        # Test model
        print("\n📊 Testing model...")
        test_results = trainer.test(model, data_module, ckpt_path='best')
        
        print("\n📊 Training Complete!")
        print("=" * 60)
        print(f"Best model checkpoint: {checkpoint_callback.best_model_path}")
        print(f"Test Results: {test_results[0]}")
        
        print("\n✅ Demo Training Completed Successfully!")
        print("=" * 60)
        
        print("📊 Final Test Results:")
        for key, value in test_results[0].items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # Save final model info
        model_save_path = checkpoint_callback.best_model_path
        print(f"\n💾 Model saved to: {model_save_path}")
        print(f"📈 TensorBoard logs: step_03_logs")
        
        print("\n🔄 Next Steps:")
        print("  1. Check TensorBoard logs: tensorboard --logdir step_03_logs")
        print("  2. Use saved model for predictions")
        print("  3. Integrate with hierarchical pipeline")
        print("  4. Tune hyperparameters based on validation results")
        
        print("\n🎉 Demo Complete!")
        
        return test_results[0]
        
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # Set random seeds for reproducibility
    import random
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # Create directories
    os.makedirs('step_03_checkpoints', exist_ok=True)
    os.makedirs('step_03_logs', exist_ok=True)
    
    # Run demo training
    results = demo_training()
    
    if results:
        print("\n🎉 Training completed successfully!")
    else:
        print("\n❌ Training failed. Check logs for details.")
