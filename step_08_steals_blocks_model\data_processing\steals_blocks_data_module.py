"""
Steals & Blocks Data Module (Step 8 of 9) - Defensive Playmaker Data Processing
===============================================================================

This module handles data processing for the steals and blocks model with defensive features.

Key Components:
- StealsBlocksDataModule: PyTorch Lightning data module
- DefensiveFeatures: Physical traits and defensive positioning features
- DefensiveActionProcessor: Steal and block opportunity analysis
- DefensiveImpactFeatures: Defensive value and impact metrics

Features:
- Unified data integration
- Physical defensive traits processing
- Defensive positioning analysis
- Rare event handling for steals/blocks
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

logger = logging.getLogger(__name__)


class StealsBlocksDataset(Dataset):
    """Dataset for steals and blocks prediction."""
    
    def __init__(self, features: np.ndarray, steals_targets: np.ndarray, blocks_targets: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.steals_targets = torch.LongTensor(steals_targets)
        self.blocks_targets = torch.LongTensor(blocks_targets)
    
    def __len__(self) -> int:
        return len(self.features)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self.features[idx], self.steals_targets[idx], self.blocks_targets[idx]


class DefensiveFeatures:
    """Creates defensive features for steals and blocks analysis."""
    
    # Core defensive features
    DEFENSIVE_FEATURES = [
        # Physical traits
        'wingspan_ratio',
        'estimated_wingspan',
        'estimated_height',
        'estimated_vertical',
        'hand_size_estimate',
        'lateral_quickness',
        'reaction_time_estimate',
        
        # Steal-specific features
        'passing_lane_aggression',
        'deflections_per_minute',
        'ball_denial_index',
        'steal_attempt_success_rate',
        'anticipation_rating',
        'steal_opportunity_index',
        
        # Block-specific features
        'rim_protection_radius',
        'help_defense_index',
        'chase_down_ability',
        'shot_contest_rate',
        'block_attempt_success_rate',
        'interior_defense_rating',
        
        # Contextual defensive factors
        'ball_handler_pressure',
        'opponent_turnover_prone',
        'defensive_scheme_type',
        'defensive_iq_rating',
        'team_defensive_rating',
        'minutes_per_game',
        
        # Performance-based indicators
        'steals_per_minute',
        'blocks_per_minute',
        'defensive_tier',
        'defensive_stats'
    ]
    
    def __init__(self):
        self.feature_scaler = StandardScaler()
    
    def create_defensive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive defensive features."""
        features = df.copy()
        
        logger.info("Creating defensive features...")
        
        # 1. PHYSICAL DEFENSIVE TRAITS
        features = self._create_physical_traits(features)
        
        # 2. STEAL-SPECIFIC FEATURES
        features = self._create_steal_features(features)
        
        # 3. BLOCK-SPECIFIC FEATURES
        features = self._create_block_features(features)
        
        # 4. CONTEXTUAL DEFENSIVE FACTORS
        features = self._create_contextual_features(features)
        
        # 5. PERFORMANCE-BASED INDICATORS
        features = self._create_performance_indicators(features)
        
        logger.info(f"Created {len(features.columns)} defensive features")
        
        return features
    
    def _create_physical_traits(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create physical defensive trait features."""
        features = df.copy()
        
        # Use existing estimated physical traits or create them
        if 'estimated_wingspan' not in features.columns:
            # Estimate wingspan from performance
            steal_indicator = features.get('steals_per_minute', 0) * 20
            block_indicator = features.get('blocks_per_minute', 0) * 15
            features['estimated_wingspan'] = 78 + steal_indicator + block_indicator + np.random.normal(0, 3, len(features))
            features['estimated_wingspan'] = np.clip(features['estimated_wingspan'], 70, 85)
        
        if 'estimated_height' not in features.columns:
            # Estimate height from defensive performance
            defensive_indicator = features.get('blocks_per_minute', 0) * 25
            features['estimated_height'] = 71 + defensive_indicator + np.random.normal(0, 2, len(features))
            features['estimated_height'] = np.clip(features['estimated_height'], 64, 80)
        
        # Wingspan ratio
        features['wingspan_ratio'] = features['estimated_wingspan'] / features['estimated_height']
        
        # Hand size estimate (correlated with steals)
        features['hand_size_estimate'] = (
            features.get('steals_per_minute', 0) * 5 + 
            features['wingspan_ratio'] * 2 + 
            np.random.normal(8.5, 0.8, len(features))
        )
        features['hand_size_estimate'] = np.clip(features['hand_size_estimate'], 7, 11)
        
        # Athletic traits
        features['lateral_quickness'] = (
            features.get('steals_per_minute', 0) * 8 + 
            np.random.uniform(0.5, 1.0, len(features))
        )
        
        features['reaction_time_estimate'] = (
            1.0 - features.get('steals_per_minute', 0) * 2 + 
            np.random.normal(0, 0.1, len(features))
        )
        features['reaction_time_estimate'] = np.clip(features['reaction_time_estimate'], 0.3, 1.2)
        
        # Vertical leap for blocks
        if 'estimated_vertical' not in features.columns:
            features['estimated_vertical'] = (
                24 + features.get('blocks_per_minute', 0) * 40 + 
                np.random.normal(0, 3, len(features))
            )
            features['estimated_vertical'] = np.clip(features['estimated_vertical'], 18, 35)
        
        return features
    
    def _create_steal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create steal-specific defensive features."""
        features = df.copy()
        
        # Base steal rate
        base_steal_rate = features.get('steals_per_minute', 0)
        
        # Passing lane aggression
        features['passing_lane_aggression'] = (
            base_steal_rate * 10 + 
            np.random.uniform(0.3, 0.9, len(features))
        )
        
        # Deflections per minute (estimated from steals)
        features['deflections_per_minute'] = base_steal_rate * 3 + np.random.uniform(0, 0.1, len(features))
        
        # Ball denial index
        features['ball_denial_index'] = (
            base_steal_rate * 8 + 
            features.get('defensive_tier', 0) * 0.2 + 
            np.random.uniform(0.4, 0.8, len(features))
        )
        
        # Steal attempt success rate
        features['steal_attempt_success_rate'] = np.clip(
            base_steal_rate * 5 + np.random.normal(0.3, 0.1, len(features)),
            0.1, 0.8
        )
        
        # Anticipation rating
        features['anticipation_rating'] = (
            base_steal_rate * 12 + 
            features['lateral_quickness'] * 0.3 + 
            np.random.uniform(0.5, 1.0, len(features))
        )
        
        # Steal opportunity index
        features['steal_opportunity_index'] = (
            features['passing_lane_aggression'] * 0.4 + 
            features['anticipation_rating'] * 0.6
        )
        
        return features
    
    def _create_block_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create block-specific defensive features."""
        features = df.copy()
        
        # Base block rate
        base_block_rate = features.get('blocks_per_minute', 0)
        
        # Rim protection radius
        features['rim_protection_radius'] = (
            base_block_rate * 15 + 
            features['estimated_height'] * 0.1 + 
            np.random.uniform(3, 8, len(features))
        )
        
        # Help defense index
        features['help_defense_index'] = (
            base_block_rate * 10 + 
            features.get('defensive_tier', 0) * 0.3 + 
            np.random.uniform(0.4, 0.9, len(features))
        )
        
        # Chase down ability
        features['chase_down_ability'] = (
            base_block_rate * 8 + 
            features['estimated_vertical'] * 0.02 + 
            np.random.uniform(0.2, 0.7, len(features))
        )
        
        # Shot contest rate
        features['shot_contest_rate'] = (
            base_block_rate * 6 + 
            features['rim_protection_radius'] * 0.1 + 
            np.random.uniform(0.3, 0.8, len(features))
        )
        
        # Block attempt success rate
        features['block_attempt_success_rate'] = np.clip(
            base_block_rate * 4 + np.random.normal(0.2, 0.08, len(features)),
            0.05, 0.6
        )
        
        # Interior defense rating
        features['interior_defense_rating'] = (
            base_block_rate * 12 + 
            features['estimated_height'] * 0.05 + 
            features['estimated_vertical'] * 0.03 + 
            np.random.uniform(0.5, 1.0, len(features))
        )
        
        return features
    
    def _create_contextual_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create contextual defensive factors."""
        features = df.copy()
        
        # Ball handler pressure
        features['ball_handler_pressure'] = (
            features.get('steals_per_minute', 0) * 8 + 
            np.random.uniform(0.4, 0.9, len(features))
        )
        
        # Opponent turnover prone (team-level factor)
        features['opponent_turnover_prone'] = np.random.uniform(0.3, 0.7, len(features))
        
        # Defensive scheme type (0=man, 1=zone, 2=hybrid)
        features['defensive_scheme_type'] = np.random.choice([0, 1, 2], len(features))
        
        # Defensive IQ rating
        features['defensive_iq_rating'] = (
            features.get('defensive_tier', 0) * 0.3 + 
            (features.get('steals_per_minute', 0) + features.get('blocks_per_minute', 0)) * 5 + 
            np.random.uniform(0.5, 1.0, len(features))
        )
        
        # Team defensive rating
        features['team_defensive_rating'] = np.random.normal(105, 8, len(features))  # Points per 100 possessions
        
        return features
    
    def _create_performance_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create performance-based defensive indicators."""
        features = df.copy()
        
        # Ensure per-minute stats exist
        minutes_col = 'minutes_played' if 'minutes_played' in features.columns else 'minutes_per_game'
        
        if 'steals_per_minute' not in features.columns:
            steals = features.get('steals', 0)
            minutes = features.get(minutes_col, 20)
            features['steals_per_minute'] = steals / np.maximum(minutes, 1)
        
        if 'blocks_per_minute' not in features.columns:
            blocks = features.get('blocks', 0)
            minutes = features.get(minutes_col, 20)
            features['blocks_per_minute'] = blocks / np.maximum(minutes, 1)
        
        # Defensive tier (0=poor, 1=average, 2=good, 3=elite)
        if 'defensive_tier' not in features.columns:
            total_defensive = features['steals_per_minute'] + features['blocks_per_minute']
            features['defensive_tier'] = pd.cut(
                total_defensive, 
                bins=[-np.inf, 0.02, 0.05, 0.08, np.inf], 
                labels=[0, 1, 2, 3]
            ).astype(int)
        
        # Defensive stats composite
        if 'defensive_stats' not in features.columns:
            features['defensive_stats'] = (
                features['steals_per_minute'] * 20 + 
                features['blocks_per_minute'] * 15 + 
                features.get('defensive_tier', 0) * 5
            )
        
        return features


class StealsBlocksDataModule(pl.LightningDataModule):
    """PyTorch Lightning data module for steals and blocks model."""
    
    def __init__(
        self,
        data_path: Optional[str] = None,
        batch_size: int = 64,
        test_size: float = 0.2,
        val_size: float = 0.2,
        random_state: int = 42,
        use_unified_data: bool = True
    ):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.test_size = test_size
        self.val_size = val_size
        self.random_state = random_state
        self.use_unified_data = use_unified_data
        
        # Feature processor
        self.feature_processor = DefensiveFeatures()
        
        # Data storage
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        # Setup unified data path
        if self.use_unified_data and self.data_path is None:
            unified_data_dir = Path(__file__).parent.parent.parent / "unified_data"
            self.data_path = str(unified_data_dir / "unified_player_data.csv")
            logger.info(f"Using unified data: {self.data_path}")
    
    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing."""
        logger.info("Setting up steals & blocks data module...")
        
        # Load data
        if self.data_path and Path(self.data_path).exists():
            df = pd.read_csv(self.data_path)
            logger.info(f"Loaded data shape: {df.shape}")
        else:
            if self.data_path:
                raise FileNotFoundError(
                    f"Unified data file not found: {self.data_path}. "
                    f"Please ensure consolidated_wnba data is properly structured."
                )
            else:
                raise FileNotFoundError(
                    "No data path provided and no unified data available. "
                    "Please ensure consolidated_wnba data is properly structured."
                )
        
        # Create features
        features_df = self.feature_processor.create_defensive_features(df)
        
        # Prepare features and targets
        feature_cols = [col for col in self.feature_processor.DEFENSIVE_FEATURES if col in features_df.columns]
        
        # Fill missing features with defaults
        for col in self.feature_processor.DEFENSIVE_FEATURES:
            if col not in features_df.columns:
                features_df[col] = 0.0
        
        # Extract features and targets
        X = features_df[feature_cols].fillna(0).values
        
        # Create targets (steals and blocks counts)
        steals = features_df.get('steals', np.random.poisson(1.2, len(features_df))).fillna(0).astype(int).values
        blocks = features_df.get('blocks', np.random.poisson(0.8, len(features_df))).fillna(0).astype(int).values
        
        # Scale features
        X_scaled = self.feature_processor.feature_scaler.fit_transform(X)
        
        # Split data
        X_temp, X_test, steals_temp, steals_test, blocks_temp, blocks_test = train_test_split(
            X_scaled, steals, blocks, test_size=self.test_size, random_state=self.random_state
        )
        
        val_size_adjusted = self.val_size / (1 - self.test_size)
        X_train, X_val, steals_train, steals_val, blocks_train, blocks_val = train_test_split(
            X_temp, steals_temp, blocks_temp, test_size=val_size_adjusted, random_state=self.random_state
        )
        
        # Create datasets
        self.train_dataset = StealsBlocksDataset(X_train, steals_train, blocks_train)
        self.val_dataset = StealsBlocksDataset(X_val, steals_val, blocks_val)
        self.test_dataset = StealsBlocksDataset(X_test, steals_test, blocks_test)
        
        logger.info(f"Train: {len(self.train_dataset)}, Val: {len(self.val_dataset)}, Test: {len(self.test_dataset)}")
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample data for demonstration."""
        logger.info("Creating sample defensive data...")
        
        np.random.seed(42)
        n_samples = 500
        
        data = {
            'player_name': [f'Player_{i}' for i in range(n_samples)],
            'steals': np.random.poisson(1.2, n_samples),
            'blocks': np.random.poisson(0.8, n_samples),
            'minutes_played': np.random.normal(25, 8, n_samples),
            'defensive_tier': np.random.choice([0, 1, 2, 3], n_samples, p=[0.3, 0.4, 0.2, 0.1])
        }
        
        df = pd.DataFrame(data)
        
        # Ensure non-negative values
        df['steals'] = np.maximum(df['steals'], 0)
        df['blocks'] = np.maximum(df['blocks'], 0)
        df['minutes_played'] = np.maximum(df['minutes_played'], 5)
        
        return df
    
    def train_dataloader(self) -> DataLoader:
        return DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=0)
    
    def val_dataloader(self) -> DataLoader:
        return DataLoader(self.val_dataset, batch_size=self.batch_size, shuffle=False, num_workers=0)
    
    def test_dataloader(self) -> DataLoader:
        return DataLoader(self.test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=0)
