"""
Team Abbreviation Standardization for WNBA Data

This module provides utilities to standardize team abbreviations across all WNBA datasets,
ensuring consistency and removing historical/duplicate team entries.

Team Issues Identified:
1. PHX vs PHO (Phoenix Mercury) - same team, different abbreviations
2. LAS vs LVA (Las Vegas Aces) - need to investigate which is correct  
3. <PERSON><PERSON> (San Antonio Silver Stars) - historical team that became Las Vegas Aces

Standard WNBA Teams (2025):
- 12 original teams + GSV (Golden State Valkyries expansion in 2025)
"""

import pandas as pd
import logging
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WNBATeamStandardizer:
    """Standardizes WNBA team abbreviations across all datasets."""
    
    def __init__(self):
        """Initialize with team standardization mappings."""
        
        # Standard WNBA team abbreviations (2025) - 13 total teams
        self.standard_teams_2025 = {
            'ATL': 'Atlanta Dream',
            'CHI': 'Chicago Sky', 
            'CON': 'Connecticut Sun',
            'DAL': 'Dallas Wings',
            'GSV': 'Golden State Valkyries',  # New in 2025
            'IND': 'Indiana Fever',
            'LAS': 'Los Angeles Sparks',
            'LVA': 'Las Vegas Aces',
            'MIN': 'Minnesota Lynx',
            'NYL': 'New York Liberty', 
            'PHO': 'Phoenix Mercury',
            'SEA': 'Seattle Storm',
            'WAS': 'Washington Mystics'
        }
        
        # Teams by era (for historical context)
        self.standard_teams_2020_2024 = {
            k: v for k, v in self.standard_teams_2025.items() 
            if k != 'GSV'  # No GSV before 2025
        }
        
        # Team mapping for standardization
        self.team_mappings = {
            # Phoenix Mercury standardization
            'PHX': 'PHO',  # 2025 data uses PHX, standardize to PHO
            
            # Historical team mappings (San Antonio became Las Vegas)
            'SAN': 'LVA',  # San Antonio Silver Stars → Las Vegas Aces
            
            # Note: LAS = Los Angeles Sparks, LVA = Las Vegas Aces (both valid)
        }
        
        # Team ID mappings for validation
        self.team_ids = {
            'ATL': 1611661330,
            'CHI': 1611661325, 
            'CON': 1611661313,  # Connecticut Sun
            'DAL': 1611661328,
            'GSV': None,  # New team, ID TBD
            'IND': 1611661322,
            'LAS': 1611661320,  # Los Angeles Sparks
            'LVA': 1611661319,  # Las Vegas Aces (confirmed)
            'MIN': 1611661324,
            'NYL': 1611661323, 
            'PHO': 1611661317,  # Phoenix Mercury (confirmed)
            'SEA': 1611661321,
            'WAS': 1611661329
        }
        
    def standardize_team_abbreviation(self, team_abbr: str, year: int = None) -> str:
        """
        Standardize a team abbreviation.
        
        Args:
            team_abbr: Original team abbreviation
            year: Year of the data (optional, for context)
            
        Returns:
            Standardized team abbreviation
        """
        if team_abbr in self.team_mappings:
            standardized = self.team_mappings[team_abbr]
            logger.info(f"Mapping {team_abbr} → {standardized}" + 
                       (f" (year: {year})" if year else ""))
            return standardized
        
        return team_abbr
    
    def validate_team_for_year(self, team_abbr: str, year: int) -> bool:
        """
        Validate if a team should exist in a given year.
        
        Args:
            team_abbr: Team abbreviation
            year: Year to validate against
            
        Returns:
            True if team should exist in that year
        """
        if year >= 2025:
            return team_abbr in self.standard_teams_2025
        else:
            return team_abbr in self.standard_teams_2020_2024
    
    def clean_dataframe(self, df: pd.DataFrame, team_col: str = 'TEAM_ABBREVIATION', 
                       year_col: str = None) -> pd.DataFrame:
        """
        Clean team abbreviations in a DataFrame.
        
        Args:
            df: DataFrame to clean
            team_col: Name of the team abbreviation column
            year_col: Name of the year/season column (optional)
            
        Returns:
            Cleaned DataFrame
        """
        df_clean = df.copy()
        
        # Apply standardization
        if year_col and year_col in df_clean.columns:
            # Year-aware cleaning
            def standardize_with_year(row):
                return self.standardize_team_abbreviation(
                    row[team_col], row[year_col]
                )
            df_clean[team_col] = df_clean.apply(standardize_with_year, axis=1)
        else:
            # Simple standardization
            df_clean[team_col] = df_clean[team_col].apply(
                self.standardize_team_abbreviation
            )
        
        # Log summary of changes
        original_teams = set(df[team_col].unique())
        cleaned_teams = set(df_clean[team_col].unique())
        
        logger.info(f"Original teams: {sorted(original_teams)}")
        logger.info(f"Cleaned teams: {sorted(cleaned_teams)}")
        
        if original_teams != cleaned_teams:
            removed = original_teams - cleaned_teams
            added = cleaned_teams - original_teams
            if removed:
                logger.info(f"Removed teams: {sorted(removed)}")
            if added:
                logger.info(f"Added teams: {sorted(added)}")
        
        return df_clean
    
    def get_team_distribution(self, df: pd.DataFrame, 
                            team_col: str = 'TEAM_ABBREVIATION') -> pd.Series:
        """Get team distribution from a DataFrame."""
        return df[team_col].value_counts().sort_index()
    
    def validate_team_count_by_year(self, df: pd.DataFrame, 
                                  team_col: str = 'TEAM_ABBREVIATION',
                                  year_col: str = 'season') -> Dict[int, int]:
        """
        Validate expected team counts by year.
        
        Returns:
            Dictionary of {year: team_count}
        """
        if year_col not in df.columns:
            logger.warning(f"Year column '{year_col}' not found")
            return {}
        
        year_team_counts = {}
        for year in sorted(df[year_col].unique()):
            year_data = df[df[year_col] == year]
            team_count = year_data[team_col].nunique()
            year_team_counts[year] = team_count
            
            # Validate expected count
            expected_count = 13 if year >= 2025 else 12
            if team_count != expected_count:
                logger.warning(f"Year {year}: Found {team_count} teams, expected {expected_count}")
                teams = sorted(year_data[team_col].unique())
                logger.warning(f"  Teams: {teams}")
                
                # Check for missing teams
                if year >= 2025:
                    expected_teams = set(self.standard_teams_2025.keys())
                else:
                    expected_teams = set(self.standard_teams_2020_2024.keys())
                
                actual_teams = set(teams)
                missing_teams = expected_teams - actual_teams
                extra_teams = actual_teams - expected_teams
                
                if missing_teams:
                    logger.warning(f"  Missing teams: {sorted(missing_teams)}")
                if extra_teams:
                    logger.warning(f"  Extra teams: {sorted(extra_teams)}")
        
        return year_team_counts


def clean_wnba_dataset(file_path: str, output_path: str = None, 
                      team_col: str = 'TEAM_ABBREVIATION',
                      year_col: str = None) -> pd.DataFrame:
    """
    Clean a WNBA dataset file.
    
    Args:
        file_path: Path to the input CSV file
        output_path: Path to save cleaned file (optional)
        team_col: Name of team abbreviation column
        year_col: Name of year/season column
        
    Returns:
        Cleaned DataFrame
    """
    logger.info(f"Cleaning dataset: {file_path}")
    
    # Load data
    df = pd.read_csv(file_path)
    logger.info(f"Loaded {len(df)} rows, {len(df.columns)} columns")
    
    # Initialize standardizer
    standardizer = WNBATeamStandardizer()
    
    # Clean data
    df_clean = standardizer.clean_dataframe(df, team_col, year_col)
    
    # Save if output path provided
    if output_path:
        df_clean.to_csv(output_path, index=False)
        logger.info(f"Saved cleaned data to: {output_path}")
    
    return df_clean


if __name__ == "__main__":
    # Example usage
    standardizer = WNBATeamStandardizer()
    
    # Test standardization
    test_teams = ['PHX', 'PHO', 'LAS', 'LVA', 'SAN', 'ATL', 'GSV']
    print("Team standardization test:")
    for team in test_teams:
        standardized = standardizer.standardize_team_abbreviation(team)
        print(f"  {team} → {standardized}")
    
    print(f"\nStandard 2025 teams ({len(standardizer.standard_teams_2025)}):")
    for abbr, name in sorted(standardizer.standard_teams_2025.items()):
        print(f"  {abbr}: {name}")
