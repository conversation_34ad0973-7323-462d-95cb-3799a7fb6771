# Player Rebounds Model (Step 4 of 9)

This module implements a position-sensitive player rebounds model with Zero-Inflated Poisson output for WNBA rebounding predictions.

## Features

### Model Architecture
- **Position-Sensitive Processing**: Separate embeddings and transformations for Guards, Forwards, and Centers
- **Zero-Inflated Poisson Output**: Handles games with zero rebounds due to limited minutes or specific situations
- **Opponent Interaction**: Models how opponent characteristics affect rebounding opportunities
- **Boxout Impact**: Captures opponent interaction effects on rebounding success
- **Hierarchical Features**: Integrates features from previous models in the pipeline

### Key Components

#### 1. PlayerReboundsModel
- Main PyTorch Lightning model with position-sensitive architecture
- Zero-Inflated Poisson loss function
- Uncertainty quantification
- Comprehensive prediction capabilities

#### 2. ReboundDataModule
- Rebound-specific feature engineering
- Position encoding and standardization
- Opponent interaction features
- Advanced rebounding metrics

#### 3. Evaluation Metrics
- Zero-inflated Poisson evaluation
- Position-weighted assessment
- Uncertainty calibration
- Comprehensive benchmarking

## Directory Structure

```
step_04_rebounds_model/
├── models/
│   └── player_rebounds_model.py      # Main model architecture
├── data_processing/
│   └── rebound_data_module.py        # Data processing pipeline
├── utils/
│   └── rebound_metrics.py            # Evaluation metrics
├── training/
│   └── train_rebounds_model.py       # Full training script
├── validation/
├── demo_training.py                  # Demo training script
├── requirements.txt                  # Dependencies
└── README.md                         # This file
```

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Demo Training
```bash
python demo_training.py
```

### 3. Full Training
```bash
python training/train_rebounds_model.py
```

## Model Architecture Details

### Zero-Inflated Poisson Output
The model uses a Zero-Inflated Poisson (ZIP) distribution to handle:
- **Structural zeros**: Games where players don't get rebounds due to limited minutes
- **Count distribution**: Proper modeling of discrete rebound counts
- **Overdispersion**: Handles variance greater than mean in rebound data

### Position-Sensitive Features
- **Guards (PG, SG)**: Lower rebounding expectations, focus on perimeter positioning
- **Forwards (SF, PF)**: Moderate rebounding expectations, versatile positioning
- **Centers (C)**: High rebounding expectations, interior positioning focus

### Opponent Interaction Features
- Height and size mismatches
- Pace differential effects
- Opponent rebounding strength
- Defensive pressure metrics

## Feature Engineering

### Rebound-Specific Features
- Physical attributes (height, wingspan)
- Pace and shot volume indicators
- Defensive positioning metrics
- Historical rebounding patterns
- Opportunity indices

### Opponent Features
- Opponent rebounding strength
- Size mismatch indicators
- Pace differential effects
- Defensive pressure metrics

## Evaluation Metrics

### Standard Metrics
- Mean Absolute Error (MAE)
- Root Mean Square Error (RMSE)
- R² Score
- Correlation

### Zero-Inflated Metrics
- Zero-inflation rate accuracy
- Gate probability calibration
- Count distribution alignment
- Likelihood-based metrics

### Position-Weighted Metrics
- Position-specific performance
- Weighted overall metrics
- Position balance assessment

## Training Configuration

### Model Parameters
- Input dimensions: Auto-detected from data
- Position embedding: 32 dimensions
- Hidden layers: 128 → 64 → 32
- Dropout: 0.3
- Learning rate: 0.001

### Training Parameters
- Max epochs: 100
- Batch size: 32
- Early stopping: 15 epochs patience
- Gradient clipping: 1.0

## Usage Examples

### Basic Training
```python
from training.train_rebounds_model import ReboundTrainer, ReboundTrainingConfig

config = ReboundTrainingConfig()
trainer = ReboundTrainer(config)
trainer.train_model()
```

### Model Prediction
```python
from models.player_rebounds_model import PlayerReboundsModel

model = PlayerReboundsModel.load_from_checkpoint('checkpoint.ckpt')
predictions = model.predict_rebounds(features, positions, opponent_features)
```

### Evaluation
```python
from utils.rebound_metrics import ReboundMetrics

metrics = ReboundMetrics()
results = metrics.comprehensive_evaluation(
    y_true, y_pred, positions, gate_probs, poisson_rates
)
```

## Integration with Pipeline

This model integrates with the broader WNBA prediction pipeline:

### Input Features
- Player statistics and advanced metrics
- Team context and pace factors
- Opponent characteristics
- Historical performance patterns

### Output Integration
- Rebound predictions for game totals modeling
- Player performance assessment
- Lineup optimization inputs
- Betting market analysis

## Performance Expectations

### Baseline Comparisons
- Overall mean baseline
- Position-specific means
- Minutes-adjusted linear models
- Historical averages

### Target Metrics
- MAE < 2.0 rebounds
- R² > 0.6
- Zero accuracy > 0.8
- Position-weighted RMSE < 2.5

## Troubleshooting

### Common Issues
1. **Feature dimension mismatch**: Check data module feature extraction
2. **Training instability**: Adjust learning rate and dropout
3. **Poor zero-inflation**: Verify gate network training
4. **Position imbalance**: Check position encoding and weighting

### Performance Tips
- Ensure sufficient data for each position
- Balance training examples across positions
- Monitor gate probability calibration
- Validate uncertainty estimates

## Future Enhancements

### Planned Features
- Advanced opponent modeling
- Situational context (clutch time, blowouts)
- Injury and fatigue factors
- Multi-game sequence modeling

### Model Improvements
- Hierarchical Bayesian approach
- Attention mechanisms
- Transfer learning from other sports
- Real-time adaptation

## Citation

If you use this model in your research, please cite:

```
HMNV WNBA Analytics Pipeline - Player Rebounds Model (Step 4 of 9)
Position-Sensitive Zero-Inflated Poisson Architecture
2024
```
