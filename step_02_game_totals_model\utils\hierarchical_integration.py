"""
🔗 Hierarchical Integration Utilities

This module provides utilities for integrating the Player Points model (Step 1)
with the Game Totals model (Step 2) for hierarchical predictions.
"""

import torch
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import sys

# Add step_01 to path
sys.path.append(str(Path(__file__).parent.parent.parent / "step_01_player_points_model"))

try:
    from models.player_points_model import PlayerPointsModel
    from data_processing.wnba_data_module import WNBADataModule
except ImportError:
    print("Warning: Could not import Step 1 modules. Hierarchical integration may not work.")


class HierarchicalPredictor:
    """
    🎯 Hierarchical predictor that combines player and game-level models
    
    This class orchestrates predictions from both levels:
    1. Player Points Model (Step 1) - predicts individual player points
    2. Game Totals Model (Step 2) - predicts team game totals
    """
    
    def __init__(
        self,
        player_model_path: str,
        game_model_path: str,
        player_data_module: Optional[WNBADataModule] = None
    ):
        self.player_model_path = player_model_path
        self.game_model_path = game_model_path
        self.player_data_module = player_data_module
        
        # Load models
        self.player_model = None
        self.game_model = None
        self._load_models()
    
    def _load_models(self):
        """Load both player and game models"""
        
        try:
            # Load player model
            if Path(self.player_model_path).exists():
                self.player_model = PlayerPointsModel.load_from_checkpoint(
                    self.player_model_path
                )
                self.player_model.eval()
                print(f"✅ Loaded player model from {self.player_model_path}")
            else:
                print(f"❌ Player model not found: {self.player_model_path}")
        
        except Exception as e:
            print(f"❌ Error loading player model: {e}")
        
        try:
            # Load game model
            if Path(self.game_model_path).exists():
                from models.game_totals_model import GameTotalsModel
                self.game_model = GameTotalsModel.load_from_checkpoint(
                    self.game_model_path
                )
                self.game_model.eval()
                print(f"✅ Loaded game model from {self.game_model_path}")
            else:
                print(f"❌ Game model not found: {self.game_model_path}")
        
        except Exception as e:
            print(f"❌ Error loading game model: {e}")
    
    def predict_player_points(
        self, 
        player_features: torch.Tensor
    ) -> torch.Tensor:
        """
        Predict individual player points using Step 1 model
        
        Args:
            player_features: Player feature tensor [n_players, feature_dim]
        
        Returns:
            Player points predictions [n_players]
        """
        
        if self.player_model is None:
            raise ValueError("Player model not loaded")
        
        with torch.no_grad():
            predictions = self.player_model(player_features)
            return predictions.squeeze(-1)
    
    def predict_game_totals(
        self,
        game_features: torch.Tensor,
        team_ids: Optional[torch.Tensor] = None,
        opponent_ids: Optional[torch.Tensor] = None,
        player_predictions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Predict team game totals using Step 2 model
        
        Args:
            game_features: Game-level features [n_games, feature_dim]
            team_ids: Team IDs [n_games]
            opponent_ids: Opponent IDs [n_games]
            player_predictions: Optional player predictions [n_games, n_players]
        
        Returns:
            Game total predictions [n_games]
        """
        
        if self.game_model is None:
            raise ValueError("Game model not loaded")
        
        with torch.no_grad():
            predictions = self.game_model(
                game_features, 
                team_ids, 
                opponent_ids, 
                player_predictions
            )
            return predictions.squeeze(-1)
    
    def predict_hierarchical(
        self,
        player_features: torch.Tensor,
        game_features: torch.Tensor,
        team_rosters: Dict[int, List[int]],
        team_ids: torch.Tensor,
        opponent_ids: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Perform hierarchical prediction combining both models
        
        Args:
            player_features: Player features [n_players, player_feature_dim]
            game_features: Game features [n_games, game_feature_dim]
            team_rosters: Mapping from team_id to list of player indices
            team_ids: Team IDs for each game [n_games]
            opponent_ids: Opponent IDs [n_games]
        
        Returns:
            Dictionary with 'player_predictions' and 'game_predictions'
        """
        
        # Step 1: Get player predictions
        player_predictions = self.predict_player_points(player_features)
        
        # Step 2: Aggregate player predictions by team for each game
        n_games = len(team_ids)
        max_players_per_team = max(len(roster) for roster in team_rosters.values())
        
        # Create player prediction matrix for games
        game_player_predictions = torch.zeros(n_games, max_players_per_team)
        
        for game_idx, team_id in enumerate(team_ids):
            team_id_int = int(team_id.item()) if torch.is_tensor(team_id) else int(team_id)
            if team_id_int in team_rosters:
                player_indices = team_rosters[team_id_int]
                for i, player_idx in enumerate(player_indices):
                    if i < max_players_per_team and player_idx < len(player_predictions):
                        game_player_predictions[game_idx, i] = player_predictions[player_idx]
        
        # Step 3: Get game total predictions using player predictions
        game_predictions = self.predict_game_totals(
            game_features,
            team_ids,
            opponent_ids,
            game_player_predictions
        )
        
        return {
            'player_predictions': player_predictions,
            'game_predictions': game_predictions,
            'aggregated_player_contributions': game_player_predictions
        }


def create_sample_team_rosters() -> Dict[int, List[int]]:
    """Create sample team rosters for demonstration"""
    
    # WNBA has 13 teams with ~12 players each (including expansion teams)
    team_rosters = {}
    player_idx = 0
    
    for team_id in range(13):  # Updated to 13 teams
        roster_size = np.random.randint(10, 15)  # 10-14 players per team
        team_rosters[team_id] = list(range(player_idx, player_idx + roster_size))
        player_idx += roster_size
    
    return team_rosters


def aggregate_player_predictions_to_team(
    player_predictions: torch.Tensor,
    team_rosters: Dict[int, List[int]],
    team_id: int,
    aggregation_method: str = 'sum'
) -> float:
    """
    Aggregate individual player predictions to team total
    
    Args:
        player_predictions: Player predictions tensor [n_players]
        team_rosters: Team roster mapping
        team_id: Team ID to aggregate for
        aggregation_method: 'sum', 'mean', or 'weighted_sum'
    
    Returns:
        Aggregated team prediction
    """
    
    if team_id not in team_rosters:
        return 0.0
    
    player_indices = team_rosters[team_id]
    team_player_preds = player_predictions[player_indices]
    
    if aggregation_method == 'sum':
        return float(torch.sum(team_player_preds))
    elif aggregation_method == 'mean':
        return float(torch.mean(team_player_preds))
    elif aggregation_method == 'weighted_sum':
        # Simple weighting by predicted points (higher predictions get more weight)
        weights = torch.softmax(team_player_preds, dim=0)
        return float(torch.sum(team_player_preds * weights))
    else:
        raise ValueError(f"Unknown aggregation method: {aggregation_method}")


def validate_hierarchical_consistency(
    player_predictions: torch.Tensor,
    game_predictions: torch.Tensor,
    team_rosters: Dict[int, List[int]],
    team_ids: torch.Tensor,
    tolerance: float = 5.0
) -> Dict[str, float]:
    """
    Validate consistency between player and game predictions
    
    Args:
        player_predictions: Player predictions [n_players]
        game_predictions: Game total predictions [n_games]
        team_rosters: Team roster mapping
        team_ids: Team IDs for games [n_games]
        tolerance: Allowed difference tolerance
    
    Returns:
        Validation metrics
    """
    
    aggregated_totals = []
    direct_predictions = []
    
    for game_idx, team_id in enumerate(team_ids):
        team_id_int = int(team_id.item()) if torch.is_tensor(team_id) else int(team_id)
        
        # Aggregate player predictions
        aggregated_total = aggregate_player_predictions_to_team(
            player_predictions, team_rosters, team_id_int, 'sum'
        )
        
        # Direct game prediction
        direct_prediction = float(game_predictions[game_idx])
        
        aggregated_totals.append(aggregated_total)
        direct_predictions.append(direct_prediction)
    
    aggregated_totals = np.array(aggregated_totals)
    direct_predictions = np.array(direct_predictions)
    
    # Calculate validation metrics
    differences = np.abs(aggregated_totals - direct_predictions)
    
    metrics = {
        'mean_absolute_difference': float(np.mean(differences)),
        'max_difference': float(np.max(differences)),
        'consistency_rate': float(np.mean(differences <= tolerance)),
        'correlation': float(np.corrcoef(aggregated_totals, direct_predictions)[0, 1])
    }
    
    return metrics


def demo_hierarchical_prediction():
    """Demonstrate hierarchical prediction workflow"""
    
    print("🔗 Hierarchical Prediction Demo")
    print("=" * 40)
    
    # Create sample data
    n_players = 156  # 13 teams * 12 players average
    n_games = 50
    player_feature_dim = 20
    game_feature_dim = 15
    
    # Sample features
    player_features = torch.randn(n_players, player_feature_dim)
    game_features = torch.randn(n_games, game_feature_dim)
    team_ids = torch.randint(0, 13, (n_games,))  # Updated to 13 teams
    opponent_ids = torch.randint(0, 13, (n_games,))
    
    # Create team rosters
    team_rosters = create_sample_team_rosters()
    
    print(f"📊 Demo Data:")
    print(f"  Players: {n_players}")
    print(f"  Games: {n_games}")
    print(f"  Teams: {len(team_rosters)}")
    print(f"  Player feature dim: {player_feature_dim}")
    print(f"  Game feature dim: {game_feature_dim}")
    
    # Note: This is just a demo - actual models would be loaded
    print(f"\n⚠️  Note: This demo shows the workflow structure.")
    print(f"   Actual models need to be trained and loaded for real predictions.")
    
    return team_rosters


if __name__ == "__main__":
    demo_hierarchical_prediction()
