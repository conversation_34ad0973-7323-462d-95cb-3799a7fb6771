"""
🏀 Moneyline Model Training Script

This script trains the hierarchical moneyline model using the Game Totals Model
and implements market calibration for production-ready probability predictions.
"""

import os
import sys
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
import argparse
from pathlib import Path
import numpy as np

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from models.moneyline_model import MoneylineModel, generate_moneyline_report
from data_processing.moneyline_data_module import MoneylineDataModule


def train_moneyline_model(
    game_data_path: str = None,  # Default to None to use unified data
    totals_model_path: str = None,  # Will auto-detect Step 2 model
    market_data_path: str = None,
    max_epochs: int = 150,
    batch_size: int = 64,
    learning_rate: float = 1e-3,
    early_stopping_patience: int = 20,
    checkpoint_dir: str = 'step_03_checkpoints',
    log_dir: str = 'step_03_logs',
    use_unified_data: bool = True  # Use unified data by default
):
    """
    Train the WNBA Moneyline Model with hierarchical integration
    
    Performance Benchmarks:
    - Target Log Loss: < 0.62
    - Target AUC: > 0.72
    - Target Calibration Error: < 2.5%
    - Target Accuracy: > 65%
    
    Args:
        game_data_path: Path to game data
        totals_model_path: Path to trained Game Totals Model
        market_data_path: Path to market odds data (optional)
        max_epochs: Maximum training epochs
        batch_size: Training batch size
        learning_rate: Learning rate
        early_stopping_patience: Early stopping patience
        checkpoint_dir: Directory for model checkpoints
        log_dir: Directory for logs
    """
    
    print("🏀 WNBA Moneyline Model Training")
    print("=" * 60)
    
    # Create directories
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Setup data module
    print("\n📊 Setting up data module...")
    data_module = MoneylineDataModule(
        game_data_path=game_data_path,
        totals_model_path=totals_model_path,
        market_data_path=market_data_path,
        batch_size=batch_size,
        num_workers=0  # Windows compatibility
    )
    
    # Setup data
    data_module.setup()
    
    # Load the totals model
    print(f"\n🔗 Loading Game Totals Model from: {totals_model_path}")
    try:
        # Try to load the model
        from step_02_game_totals_model.models.game_totals_model import GameTotalsModel
        totals_model = GameTotalsModel.load_from_checkpoint(totals_model_path)
        totals_model.eval()
        print("✅ Game Totals Model loaded successfully")
    except Exception as e:
        print(f"❌ Could not load Game Totals Model: {e}")
        print("⚠️  Cannot proceed without real Game Totals Model")
        print("Please ensure step_02_game_totals_model is trained and checkpoints exist")
        raise RuntimeError("Game Totals Model is required for Moneyline Model training. Please train step_02 first.")
    
    # Get input dimension from sample batch
    sample_batch = next(iter(data_module.train_dataloader()))
    team_features, player_features, target = sample_batch
    
    # Calculate input dimensions
    contextual_dim = team_features['contextual'].shape[1]
    input_dim = contextual_dim + 2  # +2 for momentum features
    
    print(f"Input dimension: {input_dim}")
    print(f"Contextual features: {contextual_dim}")
    
    # Initialize model
    print(f"\n🤖 Initializing Moneyline Model...")
    model = MoneylineModel(
        totals_model=totals_model,
        input_dim=input_dim,
        learning_rate=learning_rate,
        hidden_dim=192,
        dropout_rate=0.25
    )
    
    # Setup callbacks
    print("\n⚙️  Setting up training callbacks...")
    
    # Early stopping
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )
    
    # Model checkpoint
    checkpoint_callback = ModelCheckpoint(
        dirpath=checkpoint_dir,
        filename='moneyline_model-{epoch:02d}-{val_loss:.4f}',
        monitor='val_loss',
        mode='min',
        save_top_k=3,
        verbose=True
    )
    
    # Learning rate monitor
    lr_monitor = LearningRateMonitor(logging_interval='epoch')
    
    # Logger
    logger = TensorBoardLogger(log_dir, name='moneyline_model')
    
    # Initialize trainer
    print(f"\n🏃 Starting training for {max_epochs} epochs...")
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        callbacks=[early_stopping, checkpoint_callback, lr_monitor],
        logger=logger,
        gradient_clip_val=1.0,
        precision='16-mixed',  # Mixed precision for faster training
        accelerator='auto',
        devices=1,
        log_every_n_steps=10,
        check_val_every_n_epoch=1
    )
    
    # Train the model
    trainer.fit(model, data_module)
    
    # Test the model
    print("\n🧪 Testing model on test set...")
    test_results = trainer.test(model, data_module, ckpt_path='best')
    
    # Load best model for calibration
    best_model = MoneylineModel.load_from_checkpoint(
        checkpoint_callback.best_model_path,
        totals_model=totals_model
    )

    # Calibrate probabilities
    print("\n🎯 Calibrating probabilities...")
    best_model.calibrate_probabilities(data_module.val_dataloader())
    
    # Generate comprehensive report
    print("\n📊 Generating validation report...")
    
    # Collect predictions for report
    predictions = []
    targets = []
    
    best_model.eval()
    with torch.no_grad():
        for batch in data_module.test_dataloader():
            team_features, player_features, target = batch
            logits = best_model(team_features, player_features)
            probs = torch.sigmoid(logits.squeeze())
            predictions.extend(probs.cpu().numpy())
            targets.extend(target.cpu().numpy())
    
    # Generate report
    report = generate_moneyline_report(
        np.array(targets),
        np.array(predictions)
    )
    
    # Print results
    print("\n📊 Training Complete!")
    print("=" * 60)
    print(f"Best model checkpoint: {checkpoint_callback.best_model_path}")
    print(f"Test Results: {test_results}")
    
    print("\n📈 Model Performance Report:")
    print(f"  Log Loss: {report['Log Loss']:.4f}")
    print(f"  Brier Score: {report['Brier Score']:.4f}")
    print(f"  AUC: {report['AUC']:.4f}")
    print(f"  Accuracy: {report['Accuracy']:.4f}")
    print(f"  Calibration Error: {report['Calibration Error']:.4f}")
    
    # Benchmark status
    benchmarks = {
        'Log Loss': report['Log Loss'] < 0.62,
        'AUC': report['AUC'] > 0.72,
        'Calibration Error': report['Calibration Error'] < 0.025,
        'Accuracy': report['Accuracy'] > 0.65
    }
    
    print("\n🎯 Benchmark Status:")
    for metric, passed in benchmarks.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {metric}: {status}")
    
    overall_pass = all(benchmarks.values())
    print(f"\n🏆 Overall Status: {'✅ PRODUCTION READY' if overall_pass else '⚠️  NEEDS IMPROVEMENT'}")
    
    return {
        'model_path': checkpoint_callback.best_model_path,
        'test_results': test_results,
        'performance_report': report,
        'benchmarks_passed': overall_pass
    }\n\n\ndef main():\n    \"\"\"Main training function\"\"\"\n    parser = argparse.ArgumentParser(description='Train WNBA Moneyline Model')\n    \n    parser.add_argument('--game_data_path', type=str, required=True,\n                       help='Path to game data CSV/Parquet file')\n    parser.add_argument('--totals_model_path', type=str, required=True,\n                       help='Path to trained Game Totals Model checkpoint')\n    parser.add_argument('--market_data_path', type=str, default=None,\n                       help='Path to market odds data (optional)')\n    parser.add_argument('--max_epochs', type=int, default=150,\n                       help='Maximum training epochs')\n    parser.add_argument('--batch_size', type=int, default=64,\n                       help='Training batch size')\n    parser.add_argument('--learning_rate', type=float, default=1e-3,\n                       help='Learning rate')\n    parser.add_argument('--early_stopping_patience', type=int, default=20,\n                       help='Early stopping patience')\n    parser.add_argument('--checkpoint_dir', type=str, default='step_03_checkpoints',\n                       help='Directory for model checkpoints')\n    parser.add_argument('--log_dir', type=str, default='step_03_logs',\n                       help='Directory for logs')\n    \n    args = parser.parse_args()\n    \n    # Train the model\n    results = train_moneyline_model(\n        game_data_path=args.game_data_path,\n        totals_model_path=args.totals_model_path,\n        market_data_path=args.market_data_path,\n        max_epochs=args.max_epochs,\n        batch_size=args.batch_size,\n        learning_rate=args.learning_rate,\n        early_stopping_patience=args.early_stopping_patience,\n        checkpoint_dir=args.checkpoint_dir,\n        log_dir=args.log_dir\n    )\n    \n    print(\"\\n🎉 Training Complete!\")\n    print(f\"💾 Model saved to: {results['model_path']}\")\n    print(f\"📈 TensorBoard logs: {args.log_dir}\")\n    \n    print(\"\\n🔄 Next Steps:\")\n    print(\"  1. Check TensorBoard logs: tensorboard --logdir\", args.log_dir)\n    print(\"  2. Run calibration validation\")\n    print(\"  3. Implement market integration\")\n    print(\"  4. Set up production monitoring\")\n    \n    if results['benchmarks_passed']:\n        print(\"\\n✅ Model is ready for production deployment!\")\n    else:\n        print(\"\\n⚠️  Model needs improvement before production deployment.\")\n\n\nif __name__ == \"__main__\":\n    main()
