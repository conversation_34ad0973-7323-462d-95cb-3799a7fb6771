"""
Spread Model Demo Training Script
=================================

Quick demo script to test the spread model training pipeline
with synthetic data and basic validation.
"""

import torch
import os
import sys
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from training.train_spread_model import train_spread_model
from utils.spread_metrics import SpreadEvaluator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_demo_training():
    """Run a quick demo training session"""
    
    logger.info("=== Spread Model Demo Training ===")
    
    # Training configuration
    config = {
        'consolidated_data_path': '../consolidated_wnba',
        'use_demo_data': True,
        'max_epochs': 10,
        'batch_size': 16,
        'learning_rate': 5e-4,
        'hidden_dim': 128,
        'dropout': 0.1,
        'checkpoint_dir': 'demo_checkpoints',
        'log_dir': 'demo_logs'
    }
    
    logger.info("Training configuration:")
    for key, value in config.items():
        logger.info(f"  {key}: {value}")
    
    try:
        # Train the model
        trained_model = train_spread_model(**config)
        
        # Quick validation
        logger.info("\n=== Demo Validation ===")
        
        # Create a small test batch
        test_features = torch.randn(8, 11)  # 8 samples, 11 features
        test_targets = torch.randn(8, 1) * 8  # Random spreads
        
        # Get predictions
        trained_model.eval()
        with torch.no_grad():
            outputs = trained_model(test_features)
            predictions = outputs['spread_pred']
        
        # Calculate basic metrics
        mae = torch.mean(torch.abs(predictions - test_targets)).item()
        rmse = torch.sqrt(torch.mean((predictions - test_targets) ** 2)).item()
        
        logger.info(f"Test MAE: {mae:.3f}")
        logger.info(f"Test RMSE: {rmse:.3f}")
        
        # Check output shapes
        logger.info(f"Input shape: {test_features.shape}")
        logger.info(f"Output shape: {predictions.shape}")
        logger.info(f"Target shape: {test_targets.shape}")
        
        # Validate spread predictions are reasonable
        pred_mean = predictions.mean().item()
        pred_std = predictions.std().item()
        
        logger.info(f"Prediction mean: {pred_mean:.3f}")
        logger.info(f"Prediction std: {pred_std:.3f}")
        
        # Check for consistency (if moneyline predictions available)
        if 'moneyline_pred' in outputs:
            moneyline_preds = outputs['moneyline_pred']
            
            # Convert spread to implied probability
            spread_prob = torch.sigmoid(predictions * 0.1)
            consistency = 1.0 - torch.abs(spread_prob - moneyline_preds)
            avg_consistency = consistency.mean().item()
            
            logger.info(f"Spread-Moneyline consistency: {avg_consistency:.3f}")
        
        logger.info("\n=== Demo Training Completed Successfully! ===")
        
        return trained_model
        
    except Exception as e:
        logger.error(f"Demo training failed: {e}")
        raise


def validate_model_architecture():
    """Validate the model architecture and basic functionality"""
    
    logger.info("=== Model Architecture Validation ===")
    
    # Import the model
    from models.spread_model import SpreadModel
    
    # Create a test model
    model = SpreadModel(
        input_dim=24,
        hidden_dim=128,
        dropout=0.1
    )
    
    # Test forward pass
    test_input = torch.randn(4, 24)
    
    model.eval()
    with torch.no_grad():
        output = model(test_input)
    
    logger.info(f"Model input shape: {test_input.shape}")
    logger.info(f"Model output keys: {list(output.keys())}")
    
    if 'spread_pred' in output:
        logger.info(f"Spread prediction shape: {output['spread_pred'].shape}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    
    logger.info("Model architecture validation completed!")
    
    return model


if __name__ == "__main__":
    # Set random seed for reproducibility
    torch.manual_seed(42)
    
    # Validate model architecture first
    validate_model_architecture()
    
    # Run demo training
    trained_model = run_demo_training()
    
    print("\nDemo training completed successfully!")
    print("The spread model is ready for integration with the full pipeline.")
