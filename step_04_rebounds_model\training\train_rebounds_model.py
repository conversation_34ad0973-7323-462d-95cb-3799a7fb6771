"""
Training Script for Player Rebounds Model (Step 4 of 9)
========================================================

This script handles the training of the position-sensitive player rebounds model
with Zero-Inflated Poisson output and comprehensive evaluation.

Features:
- Hierarchical feature integration
- Position-weighted training
- Zero-Inflated Poisson loss optimization
- Comprehensive evaluation with rebound-specific metrics
- Model checkpointing and logging
"""

import os
import sys
from pathlib import Path
import logging
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional

# Import model and data modules
from step_04_rebounds_model.models.player_rebounds_model import PlayerReboundsModel
from step_04_rebounds_model.data_processing.rebound_data_module import ReboundDataModule
from step_04_rebounds_model.utils.rebound_metrics import ReboundMetrics, ReboundBenchmarks

logger = logging.getLogger(__name__)


class ReboundTrainingConfig:
    """Configuration for rebounds model training."""
    
    def __init__(self):
        # Model parameters
        self.model_params = {
            'input_dim': 64,  # Will be updated based on actual data
            'position_embedding_dim': 32,
            'opponent_dim': 16,  # Will be updated based on actual data
            'hidden_dim': 128,
            'learning_rate': 0.001,
            'weight_decay': 0.01,
            'dropout_rate': 0.3
        }
        
        # Training parameters
        self.training_params = {
            'max_epochs': 100,
            'batch_size': 32,
            'num_workers': 4,
            'patience': 15,
            'min_delta': 0.001,
            'gradient_clip_val': 1.0
        }
        
        # Data parameters
        self.data_params = {
            'val_split': 0.2,
            'test_split': 0.1,
            'target_column': 'REB',
            'position_column': 'POSITION',
            'min_minutes': 5.0
        }
        
        # Paths
        self.paths = {
            'data_dir': project_root / 'consolidated_wnba',
            'checkpoint_dir': project_root / 'step_04_rebounds_model' / 'step_04_checkpoints',
            'log_dir': project_root / 'step_04_rebounds_model' / 'step_04_logs',
            'unified_data_dir': project_root / 'unified_data'
        }
        
        # Create directories
        for path in self.paths.values():
            path.mkdir(parents=True, exist_ok=True)


class ReboundTrainer:
    """Main training class for the rebounds model."""
    
    def __init__(self, config: ReboundTrainingConfig):
        self.config = config
        self.model = None
        self.data_module = None
        self.trainer = None
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        log_file = self.config.paths['log_dir'] / 'training.log'
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.info("Logging setup complete")
    
    def setup_data_module(self):
        """Setup the data module."""
        logger.info("Setting up data module...")
        
        # Try different data sources
        data_paths = [
            self.config.paths['unified_data_dir'],
            self.config.paths['data_dir'],
            self.config.paths['data_dir'] / '04_training_data'
        ]
        
        data_path = None
        for path in data_paths:
            if path.exists():
                data_path = path
                break
        
        if data_path is None:
            logger.warning("No data directory found, using fallback data generation")
            data_path = self.config.paths['data_dir']
        
        self.data_module = ReboundDataModule(
            data_path=str(data_path),
            batch_size=self.config.training_params['batch_size'],
            num_workers=self.config.training_params['num_workers'],
            **self.config.data_params
        )
        
        # Setup data module
        self.data_module.setup()
        
        # Get feature dimensions
        feature_info = self.data_module.get_feature_info()
        self.config.model_params['input_dim'] = feature_info['n_features']
        self.config.model_params['opponent_dim'] = feature_info['n_opponent_features']
        
        logger.info(f"Data module setup complete. Features: {feature_info['n_features']}, "
                   f"Opponent features: {feature_info['n_opponent_features']}")
    
    def setup_model(self):
        """Setup the rebounds model."""
        logger.info("Setting up rebounds model...")
        
        self.model = PlayerReboundsModel(**self.config.model_params)
        
        logger.info(f"Model setup complete. Input dim: {self.config.model_params['input_dim']}, "
                   f"Opponent dim: {self.config.model_params['opponent_dim']}")
    
    def setup_trainer(self):
        """Setup the PyTorch Lightning trainer."""
        logger.info("Setting up trainer...")
        
        # Callbacks
        checkpoint_callback = ModelCheckpoint(
            dirpath=self.config.paths['checkpoint_dir'],
            filename='rebounds-model-{epoch:02d}-{val_loss:.2f}',
            save_top_k=3,
            monitor='val_loss',
            mode='min',
            save_last=True
        )
        
        early_stop_callback = EarlyStopping(
            monitor='val_loss',
            patience=self.config.training_params['patience'],
            min_delta=self.config.training_params['min_delta'],
            mode='min'
        )
        
        lr_monitor = LearningRateMonitor(logging_interval='epoch')
        
        # Logger
        tb_logger = TensorBoardLogger(
            save_dir=self.config.paths['log_dir'],
            name='rebounds_model',
            version=None
        )
        
        # Trainer
        self.trainer = pl.Trainer(
            max_epochs=self.config.training_params['max_epochs'],
            callbacks=[checkpoint_callback, early_stop_callback, lr_monitor],
            logger=tb_logger,
            gradient_clip_val=self.config.training_params['gradient_clip_val'],
            deterministic=True,
            enable_progress_bar=True,
            log_every_n_steps=10
        )
        
        logger.info("Trainer setup complete")
    
    def train_model(self):
        """Train the rebounds model."""
        logger.info("Starting model training...")
        
        # Setup components
        self.setup_data_module()
        self.setup_model()
        self.setup_trainer()
        
        # Start training
        self.trainer.fit(self.model, self.data_module)
        
        logger.info("Training complete!")
    
    def evaluate_model(self):
        """Evaluate the trained model."""
        logger.info("Starting model evaluation...")
        
        if self.model is None or self.data_module is None:
            logger.error("Model and data module must be setup before evaluation")
            return
        
        # Test the model
        test_results = self.trainer.test(self.model, self.data_module)
        
        # Detailed evaluation
        self.model.eval()
        test_dataloader = self.data_module.test_dataloader()
        
        all_predictions = []
        all_targets = []
        all_positions = []
        all_gate_probs = []
        all_poisson_rates = []
        
        with torch.no_grad():
            for batch in test_dataloader:
                features = batch['features']
                positions = batch['position_indices']
                opponent_features = batch['opponent_features']
                targets = batch['rebounds']
                
                # Get predictions
                gate_probs, poisson_rates = self.model(features, positions, opponent_features)
                predictions = (1 - gate_probs) * poisson_rates
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
                all_positions.append(positions.cpu().numpy())
                all_gate_probs.append(gate_probs.cpu().numpy())
                all_poisson_rates.append(poisson_rates.cpu().numpy())
        
        # Concatenate results
        all_predictions = np.concatenate(all_predictions)
        all_targets = np.concatenate(all_targets)
        all_positions = np.concatenate(all_positions)
        all_gate_probs = np.concatenate(all_gate_probs)
        all_poisson_rates = np.concatenate(all_poisson_rates)
        
        # Comprehensive evaluation
        metrics = ReboundMetrics()
        evaluation_results = metrics.comprehensive_evaluation(
            y_true=all_targets.flatten(),
            y_pred=all_predictions.flatten(),
            positions=all_positions.flatten(),
            gate_probs=all_gate_probs.flatten(),
            poisson_rates=all_poisson_rates.flatten()
        )
        
        # Benchmark comparison
        benchmarks = ReboundBenchmarks()
        benchmark_results = benchmarks.benchmark_comparison(
            y_true=all_targets.flatten(),
            y_pred=all_predictions.flatten(),
            positions=all_positions.flatten()
        )
        
        # Performance summary
        summary = benchmarks.performance_summary(evaluation_results)
        
        logger.info("Evaluation Results:")
        logger.info(summary)
        
        # Save detailed results
        results_path = self.config.paths['log_dir'] / 'evaluation_results.txt'
        with open(results_path, 'w') as f:
            f.write(summary)
            f.write("\n\n=== DETAILED METRICS ===\n")
            f.write(str(evaluation_results))
            f.write("\n\n=== BENCHMARK COMPARISON ===\n")
            f.write(str(benchmark_results))
        
        logger.info(f"Detailed evaluation results saved to {results_path}")
        
        return evaluation_results, benchmark_results
    
    def save_model_artifacts(self):
        """Save model artifacts for deployment."""
        logger.info("Saving model artifacts...")
        
        if self.model is None or self.data_module is None:
            logger.error("Model and data module must be setup before saving")
            return
        
        # Save model
        model_path = self.config.paths['checkpoint_dir'] / 'final_model.ckpt'
        self.trainer.save_checkpoint(model_path)
        
        # Save feature information
        feature_info = self.data_module.get_feature_info()
        feature_info_path = self.config.paths['checkpoint_dir'] / 'feature_info.pkl'
        
        import pickle
        with open(feature_info_path, 'wb') as f:
            pickle.dump(feature_info, f)
        
        # Save configuration
        config_path = self.config.paths['checkpoint_dir'] / 'config.pkl'
        with open(config_path, 'wb') as f:
            pickle.dump(self.config, f)
        
        logger.info(f"Model artifacts saved to {self.config.paths['checkpoint_dir']}")


def main():
    """Main training function."""
    logger.info("Starting rebounds model training...")
    
    # Set random seeds for reproducibility
    pl.seed_everything(42)
    
    # Create configuration
    config = ReboundTrainingConfig()
    
    # Create trainer
    trainer = ReboundTrainer(config)
    
    try:
        # Train model
        trainer.train_model()
        
        # Evaluate model
        evaluation_results, benchmark_results = trainer.evaluate_model()
        
        # Save artifacts
        trainer.save_model_artifacts()
        
        logger.info("Training pipeline completed successfully!")
        
        # Print final summary
        print("\n" + "="*60)
        print("REBOUNDS MODEL TRAINING COMPLETE")
        print("="*60)
        print(f"Model checkpoints saved to: {config.paths['checkpoint_dir']}")
        print(f"Training logs saved to: {config.paths['log_dir']}")
        print(f"Best validation loss: {trainer.trainer.checkpoint_callback.best_model_score:.4f}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
