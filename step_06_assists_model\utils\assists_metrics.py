"""
Assists Model Metrics (Step 6 of 9) - Playmaking Evaluation
===========================================================

This module provides specialized metrics for evaluating assists predictions:
- Assist-specific performance metrics
- Playmaking efficiency evaluation
- Usage ratio validation
- Teammate interaction analysis

Key Components:
- AssistsMetrics: Core metrics for assists predictions
- PlaymakingEvaluator: Playmaking efficiency evaluation
- UsageRatioValidator: Usage-to-assist ratio validation
- TeammateInteractionAnalyzer: Teammate synergy metrics
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AssistsMetrics:
    """
    Core metrics for assists prediction evaluation.
    
    Provides comprehensive metrics for evaluating assists model performance
    with focus on playmaking accuracy and efficiency.
    """
    
    def __init__(self):
        self.metrics_history = []
        
    def calculate_core_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate core assists prediction metrics"""
        
        # Ensure arrays are numpy arrays
        y_true = np.asarray(y_true)
        y_pred = np.asarray(y_pred)
        
        # Basic regression metrics
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        
        # Assists-specific metrics
        assists_accuracy = self._calculate_assists_accuracy(y_true, y_pred)
        playmaking_efficiency = self._calculate_playmaking_efficiency(y_true, y_pred)
        assist_distribution_similarity = self._calculate_distribution_similarity(y_true, y_pred)
        
        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'assists_accuracy': assists_accuracy,
            'playmaking_efficiency': playmaking_efficiency,
            'assist_distribution_similarity': assist_distribution_similarity
        }
    
    def _calculate_assists_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate assists prediction accuracy within tolerance"""
        # Allow for 1 assist tolerance
        tolerance = 1.0
        accurate_predictions = np.abs(y_true - y_pred) <= tolerance
        return np.mean(accurate_predictions)
    
    def _calculate_playmaking_efficiency(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate playmaking efficiency metric"""
        # Efficiency based on high-assist game predictions
        high_assist_threshold = 5.0
        
        true_high_assists = y_true >= high_assist_threshold
        pred_high_assists = y_pred >= high_assist_threshold
        
        if np.sum(true_high_assists) == 0:
            return 0.0
        
        # True positive rate for high-assist games
        true_positives = np.sum(true_high_assists & pred_high_assists)
        return true_positives / np.sum(true_high_assists)
    
    def _calculate_distribution_similarity(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate similarity between true and predicted assist distributions"""
        # Binned distribution comparison
        bins = np.arange(0, 15, 1)  # 0-14 assists
        
        true_hist, _ = np.histogram(y_true, bins=bins, density=True)
        pred_hist, _ = np.histogram(y_pred, bins=bins, density=True)
        
        # Cosine similarity between distributions
        dot_product = np.dot(true_hist, pred_hist)
        norm_true = np.linalg.norm(true_hist)
        norm_pred = np.linalg.norm(pred_hist)
        
        if norm_true == 0 or norm_pred == 0:
            return 0.0
        
        return dot_product / (norm_true * norm_pred)
    
    def calculate_advanced_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                  features: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, float]:
        """Calculate advanced assists-specific metrics"""
        
        metrics = {}
        
        # Zero-assist prediction accuracy
        zero_assist_accuracy = self._calculate_zero_assist_accuracy(y_true, y_pred)
        metrics['zero_assist_accuracy'] = zero_assist_accuracy
        
        # High-assist prediction precision
        high_assist_precision = self._calculate_high_assist_precision(y_true, y_pred)
        metrics['high_assist_precision'] = high_assist_precision
        
        # Consistency metrics
        consistency_score = self._calculate_consistency_score(y_true, y_pred)
        metrics['consistency_score'] = consistency_score
        
        # If features provided, calculate feature-based metrics
        if features:
            usage_correlation = self._calculate_usage_correlation(y_true, y_pred, features)
            metrics['usage_correlation'] = usage_correlation
        
        return metrics
    
    def _calculate_zero_assist_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate accuracy for zero-assist predictions"""
        zero_true = y_true == 0
        zero_pred = y_pred < 0.5
        
        if np.sum(zero_true) == 0:
            return 0.0
        
        correct_zeros = np.sum(zero_true & zero_pred)
        return correct_zeros / np.sum(zero_true)
    
    def _calculate_high_assist_precision(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate precision for high-assist predictions"""
        high_threshold = 7.0
        
        true_high = y_true >= high_threshold
        pred_high = y_pred >= high_threshold
        
        if np.sum(pred_high) == 0:
            return 0.0
        
        true_positives = np.sum(true_high & pred_high)
        return true_positives / np.sum(pred_high)
    
    def _calculate_consistency_score(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate prediction consistency score"""
        # Measure how consistent predictions are across similar true values
        consistency_scores = []
        
        for value in np.unique(y_true):
            mask = y_true == value
            if np.sum(mask) > 1:
                pred_variance = np.var(y_pred[mask])
                consistency_scores.append(1.0 / (1.0 + pred_variance))
        
        return np.mean(consistency_scores) if consistency_scores else 0.0
    
    def _calculate_usage_correlation(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                   features: Dict[str, np.ndarray]) -> float:
        """Calculate correlation between usage rate and assist predictions"""
        if 'usage_rate' not in features:
            return 0.0
        
        usage_rate = features['usage_rate']
        correlation = np.corrcoef(usage_rate, y_pred)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0


class PlaymakingEvaluator:
    """
    Advanced playmaking evaluation utilities.
    
    Provides detailed analysis of playmaking efficiency and effectiveness.
    """
    
    def __init__(self):
        self.evaluation_history = []
        
    def evaluate_playmaking_efficiency(self, y_true: np.ndarray, y_pred: np.ndarray,
                                     usage_rate: np.ndarray, minutes: np.ndarray) -> Dict[str, float]:
        """Evaluate playmaking efficiency metrics"""
        
        # Assists per minute
        assists_per_minute_true = y_true / (minutes + 1e-6)
        assists_per_minute_pred = y_pred / (minutes + 1e-6)
        
        # Assists per usage
        assists_per_usage_true = y_true / (usage_rate + 1e-6)
        assists_per_usage_pred = y_pred / (usage_rate + 1e-6)
        
        return {
            'assists_per_minute_mae': mean_absolute_error(assists_per_minute_true, assists_per_minute_pred),
            'assists_per_usage_mae': mean_absolute_error(assists_per_usage_true, assists_per_usage_pred),
            'assists_per_minute_correlation': np.corrcoef(assists_per_minute_true, assists_per_minute_pred)[0, 1],
            'assists_per_usage_correlation': np.corrcoef(assists_per_usage_true, assists_per_usage_pred)[0, 1]
        }
    
    def analyze_assist_types(self, predictions: Dict[str, np.ndarray]) -> Dict[str, float]:
        """Analyze different types of assists"""
        
        assist_types = ['primary_assists', 'secondary_assists', 'potential_assists']
        analysis = {}
        
        for assist_type in assist_types:
            if assist_type in predictions:
                values = predictions[assist_type]
                analysis[f'{assist_type}_mean'] = np.mean(values)
                analysis[f'{assist_type}_std'] = np.std(values)
                analysis[f'{assist_type}_max'] = np.max(values)
        
        return analysis


class UsageRatioValidator:
    """
    Validates usage-to-assist ratio consistency.
    
    Ensures that assist predictions align with expected usage patterns.
    """
    
    def __init__(self):
        self.validation_history = []
        
    def validate_usage_ratio(self, assists_pred: np.ndarray, usage_rate: np.ndarray) -> Dict[str, float]:
        """Validate usage-to-assist ratio"""
        
        # Expected assist-to-usage ratio ranges
        expected_ratio_min = 0.1  # Very low playmaking
        expected_ratio_max = 2.0  # Very high playmaking
        
        assist_to_usage_ratio = assists_pred / (usage_rate + 1e-6)
        
        # Validation metrics
        valid_ratios = (assist_to_usage_ratio >= expected_ratio_min) & (assist_to_usage_ratio <= expected_ratio_max)
        validity_rate = np.mean(valid_ratios)
        
        # Outlier detection
        outlier_threshold = 3.0
        outliers = assist_to_usage_ratio > outlier_threshold
        outlier_rate = np.mean(outliers)
        
        return {
            'validity_rate': validity_rate,
            'outlier_rate': outlier_rate,
            'mean_ratio': np.mean(assist_to_usage_ratio),
            'std_ratio': np.std(assist_to_usage_ratio)
        }
    
    def check_consistency(self, assists_pred: np.ndarray, features: Dict[str, np.ndarray]) -> Dict[str, float]:
        """Check prediction consistency with other features"""
        
        consistency_checks = {}
        
        # Minutes consistency
        if 'minutes_per_game' in features:
            minutes = features['minutes_per_game']
            assists_per_minute = assists_pred / (minutes + 1e-6)
            consistency_checks['assists_per_minute_reasonable'] = np.mean(assists_per_minute <= 0.5)
        
        # Ball handling consistency
        if 'time_of_possession' in features:
            top = features['time_of_possession']
            assists_per_possession_time = assists_pred / (top + 1e-6)
            consistency_checks['assists_per_possession_time_reasonable'] = np.mean(assists_per_possession_time <= 2.0)
        
        return consistency_checks


class TeammateInteractionAnalyzer:
    """
    Analyzes teammate interaction effects on assists.
    
    Provides insights into how teammate abilities affect assist predictions.
    """
    
    def __init__(self):
        self.analysis_history = []
        
    def analyze_teammate_effects(self, assists_pred: np.ndarray, 
                               teammate_features: Dict[str, np.ndarray]) -> Dict[str, float]:
        """Analyze how teammate features affect assists"""
        
        effects = {}
        
        # Teammate shooting effect
        if 'teammate_fg_pct' in teammate_features:
            fg_pct = teammate_features['teammate_fg_pct']
            correlation = np.corrcoef(assists_pred, fg_pct)[0, 1]
            effects['teammate_shooting_correlation'] = correlation if not np.isnan(correlation) else 0.0
        
        # Teammate 3-point shooting effect
        if 'teammate_3p_pct' in teammate_features:
            three_pct = teammate_features['teammate_3p_pct']
            correlation = np.corrcoef(assists_pred, three_pct)[0, 1]
            effects['teammate_3p_correlation'] = correlation if not np.isnan(correlation) else 0.0
        
        return effects
    
    def calculate_synergy_score(self, assists_pred: np.ndarray, 
                              teammate_abilities: np.ndarray) -> float:
        """Calculate overall teammate synergy score"""
        
        # Normalize inputs
        assists_norm = (assists_pred - np.mean(assists_pred)) / (np.std(assists_pred) + 1e-6)
        teammate_norm = (teammate_abilities - np.mean(teammate_abilities)) / (np.std(teammate_abilities) + 1e-6)
        
        # Synergy as correlation
        synergy = np.corrcoef(assists_norm, teammate_norm)[0, 1]
        return synergy if not np.isnan(synergy) else 0.0


def evaluate_assists_model(y_true: np.ndarray, y_pred: np.ndarray, 
                          features: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, Any]:
    """
    Comprehensive assists model evaluation.
    
    Args:
        y_true: True assist values
        y_pred: Predicted assist values
        features: Optional feature dictionary for advanced metrics
        
    Returns:
        Dictionary containing all evaluation metrics
    """
    
    # Initialize evaluators
    metrics = AssistsMetrics()
    evaluator = PlaymakingEvaluator()
    validator = UsageRatioValidator()
    analyzer = TeammateInteractionAnalyzer()
    
    # Calculate core metrics
    core_metrics = metrics.calculate_core_metrics(y_true, y_pred)
    
    # Calculate advanced metrics
    advanced_metrics = metrics.calculate_advanced_metrics(y_true, y_pred, features)
    
    # Evaluation results
    evaluation_results = {
        'core_metrics': core_metrics,
        'advanced_metrics': advanced_metrics
    }
    
    # Add feature-based evaluations if features provided
    if features:
        # Usage ratio validation
        if 'usage_rate' in features:
            usage_validation = validator.validate_usage_ratio(y_pred, features['usage_rate'])
            evaluation_results['usage_validation'] = usage_validation
        
        # Playmaking efficiency
        if 'usage_rate' in features and 'minutes_per_game' in features:
            playmaking_efficiency = evaluator.evaluate_playmaking_efficiency(
                y_true, y_pred, features['usage_rate'], features['minutes_per_game']
            )
            evaluation_results['playmaking_efficiency'] = playmaking_efficiency
        
        # Teammate interaction analysis
        teammate_keys = [k for k in features.keys() if k.startswith('teammate_')]
        if teammate_keys:
            teammate_features = {k: features[k] for k in teammate_keys}
            teammate_analysis = analyzer.analyze_teammate_effects(y_pred, teammate_features)
            evaluation_results['teammate_analysis'] = teammate_analysis
    
    return evaluation_results
