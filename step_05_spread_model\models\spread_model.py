"""
Spread Model (Step 5 of 9) - Margin-Sensitive Architecture
==========================================================

This module implements a margin-sensitive spread prediction model that integrates
with the existing Totals and Moneyline models from steps 2-3.

Key Components:
- SpreadModel: Main model that uses totals and moneyline predictions
- Margin-sensitive architecture with residual learning
- Spread-moneyline consistency validation
- Hierarchical integration with previous models

Features:
- Integration with existing models (frozen)
- Margin-specific feature engineering
- Huber loss for robust spread prediction
- Consistency checks with moneyline predictions
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import logging

logger = logging.getLogger(__name__)


class SpreadModel(pl.LightningModule):
    """
    Margin-sensitive spread prediction model that integrates with 
    existing Totals and Moneyline models.
    
    This model uses frozen predictions from previous models as base features
    and learns residual adjustments for spread prediction.
    """
    
    def __init__(
        self,
        totals_model: Optional[pl.LightningModule] = None,
        moneyline_model: Optional[pl.LightningModule] = None,
        input_dim: int = 24,
        hidden_dim: int = 256,
        dropout: float = 0.2,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-4,
        spread_std: float = 8.0,
        delta: float = 3.0
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['totals_model', 'moneyline_model'])
        
        # Store base models (frozen)
        self.totals_model = totals_model
        self.moneyline_model = moneyline_model
        
        if totals_model is not None:
            self.totals_model.freeze()
        if moneyline_model is not None:
            self.moneyline_model.freeze()
        
        # Margin-sensitive network
        self.margin_net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ELU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ELU(),
            nn.LayerNorm(hidden_dim // 2),
            nn.Dropout(dropout * 0.5),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ELU(),
            
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # Loss function - Huber loss for robust spread prediction
        self.loss_fn = nn.HuberLoss(delta=delta)
        
        # Learned spread standard deviation
        self.register_buffer('spread_std', torch.tensor(spread_std))
        
        # Metrics tracking
        self.train_losses = []
        self.val_losses = []
        self.consistency_scores = []
        
    def inverse_logit(self, x: torch.Tensor) -> torch.Tensor:
        """Convert log odds to probability"""
        return torch.sigmoid(x)
    
    def spread_to_win_prob(self, spread: torch.Tensor) -> torch.Tensor:
        """Convert spread to implied win probability"""
        return torch.sigmoid(spread * 0.1)  # Rough conversion factor
    
    def forward(self, game_features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass for spread prediction
        
        Args:
            game_features: Dictionary containing game features including:
                - margin_metrics: Margin-specific features
                - base_features: Features for base models
                
        Returns:
            Dictionary with spread prediction and intermediate values
        """
        batch_size = game_features['margin_metrics'].size(0)
        
        # Get base predictions if models are available
        total_pred = torch.zeros(batch_size, 1, device=self.device)
        win_prob = torch.zeros(batch_size, 1, device=self.device)
        
        if self.totals_model is not None and 'totals_features' in game_features:
            with torch.no_grad():
                total_pred = self.totals_model(game_features['totals_features'])
                if total_pred.dim() == 1:
                    total_pred = total_pred.unsqueeze(1)
        
        if self.moneyline_model is not None and 'moneyline_features' in game_features:
            with torch.no_grad():
                win_prob = self.moneyline_model(game_features['moneyline_features'])
                if win_prob.dim() == 1:
                    win_prob = win_prob.unsqueeze(1)
        
        # Create market-implied spread
        implied_spread = self.inverse_logit(win_prob) * total_pred * 0.1
        
        # Create margin features by combining all available features
        margin_features = [game_features['margin_metrics']]
        
        if total_pred.sum() != 0:
            margin_features.append(total_pred)
        if win_prob.sum() != 0:
            margin_features.append(win_prob)
        if implied_spread.sum() != 0:
            margin_features.append(implied_spread)
        
        # Concatenate features
        combined_features = torch.cat(margin_features, dim=1)
        
        # Residual spread prediction
        spread_residual = self.margin_net(combined_features)
        
        # Final spread prediction
        spread_prediction = implied_spread + spread_residual
        
        return {
            'spread': spread_prediction,
            'implied_spread': implied_spread,
            'spread_residual': spread_residual,
            'total_pred': total_pred,
            'win_prob': win_prob
        }
    
    def calculate_consistency_score(self, spread_pred: torch.Tensor, 
                                  moneyline_pred: torch.Tensor) -> float:
        """
        Calculate consistency score between spread and moneyline predictions
        
        Args:
            spread_pred: Predicted spread values
            moneyline_pred: Predicted moneyline probabilities
            
        Returns:
            Consistency score (lower is better, should be < 0.05)
        """
        # Convert spread to implied win probability
        implied_win_prob = self.spread_to_win_prob(spread_pred)
        
        # Calculate divergence
        divergence = torch.abs(implied_win_prob - moneyline_pred).mean()
        
        return divergence.item()
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Training step"""
        game_features, target = batch
        
        # Forward pass
        outputs = self(game_features)
        spread_pred = outputs['spread']
        
        # Calculate loss
        loss = self.loss_fn(spread_pred.squeeze(), target)
        
        # Calculate consistency score if moneyline model is available
        consistency_score = 0.0
        if self.moneyline_model is not None and 'win_prob' in outputs:
            consistency_score = self.calculate_consistency_score(
                spread_pred, outputs['win_prob']
            )
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_consistency', consistency_score, on_epoch=True)
        self.log('train_spread_mean', spread_pred.mean(), on_epoch=True)
        self.log('train_spread_std', spread_pred.std(), on_epoch=True)
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Validation step"""
        game_features, target = batch
        
        # Forward pass
        outputs = self(game_features)
        spread_pred = outputs['spread']
        
        # Calculate loss
        loss = self.loss_fn(spread_pred.squeeze(), target)
        
        # Calculate additional metrics
        mae = F.l1_loss(spread_pred.squeeze(), target)
        mse = F.mse_loss(spread_pred.squeeze(), target)
        
        # Calculate consistency score
        consistency_score = 0.0
        if self.moneyline_model is not None and 'win_prob' in outputs:
            consistency_score = self.calculate_consistency_score(
                spread_pred, outputs['win_prob']
            )
        
        # Log metrics
        self.log('val_loss', loss, on_epoch=True, prog_bar=True)
        self.log('val_mae', mae, on_epoch=True)
        self.log('val_mse', mse, on_epoch=True)
        self.log('val_consistency', consistency_score, on_epoch=True)
        self.log('val_spread_mean', spread_pred.mean(), on_epoch=True)
        self.log('val_spread_std', spread_pred.std(), on_epoch=True)
        
        return loss
    
    def test_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Test step"""
        game_features, target = batch
        
        # Forward pass
        outputs = self(game_features)
        spread_pred = outputs['spread']
        
        # Calculate loss
        loss = self.loss_fn(spread_pred.squeeze(), target)
        
        # Calculate metrics
        mae = F.l1_loss(spread_pred.squeeze(), target)
        
        # Log metrics
        self.log('test_loss', loss, on_epoch=True)
        self.log('test_mae', mae, on_epoch=True)
        
        return loss
    
    def configure_optimizers(self):
        """Configure optimizer and learning rate scheduler"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=self.hparams.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }
    
    def predict_spread(self, game_features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Generate spread predictions with additional insights
        
        Args:
            game_features: Game features for prediction
            
        Returns:
            Dictionary with predictions and insights
        """
        self.eval()
        with torch.no_grad():
            outputs = self(game_features)
            
            # Calculate confidence intervals
            spread_pred = outputs['spread']
            confidence_lower = spread_pred - 1.96 * self.spread_std
            confidence_upper = spread_pred + 1.96 * self.spread_std
            
            # Calculate market alignment
            market_alignment = torch.abs(outputs['implied_spread'] - spread_pred)
            
            return {
                'spread': spread_pred,
                'confidence_lower': confidence_lower,
                'confidence_upper': confidence_upper,
                'implied_spread': outputs['implied_spread'],
                'spread_residual': outputs['spread_residual'],
                'market_alignment': market_alignment,
                'total_pred': outputs['total_pred'],
                'win_prob': outputs['win_prob']
            }


def spread_consistency_check(spread_pred: torch.Tensor, 
                           moneyline_pred: torch.Tensor) -> float:
    """
    Standalone function to ensure spread and moneyline predictions align
    
    Args:
        spread_pred: Predicted spread values
        moneyline_pred: Predicted moneyline probabilities
        
    Returns:
        Divergence score (should be < 0.05 for good consistency)
    """
    # Convert spread to implied win probability
    implied_win_prob = torch.sigmoid(spread_pred * 0.1)
    
    # Calculate divergence
    divergence = torch.abs(implied_win_prob - moneyline_pred).mean()
    
    return divergence.item()


# Spread-specific feature definitions
SPREAD_FEATURES = [
    # Team differential metrics
    'net_rating_diff',
    'point_differential_last5',
    'clutch_performance_diff',
    'offensive_efficiency_diff',
    'defensive_efficiency_diff',
    
    # Injury impacts
    'star_player_impact_diff',
    'rotation_depth_diff',
    'key_player_minutes_diff',
    
    # Matchup-specific
    'pace_mismatch_index',
    'defensive_scheme_compatibility',
    'turnover_creation_diff',
    'rebounding_advantage_diff',
    
    # Situational
    'rest_advantage_diff',
    'travel_fatigue_diff',
    'altitude_impact_diff',
    'home_court_advantage',
    
    # Market indicators
    'line_movement_volatility',
    'sharp_money_indicator',
    'public_betting_percentage',
    'reverse_line_movement'
]

# Feature categories for better organization
SPREAD_FEATURE_CATEGORIES = {
    'team_differentials': [
        'net_rating_diff',
        'point_differential_last5',
        'clutch_performance_diff',
        'offensive_efficiency_diff',
        'defensive_efficiency_diff'
    ],
    'injury_impacts': [
        'star_player_impact_diff',
        'rotation_depth_diff',
        'key_player_minutes_diff'
    ],
    'matchup_specific': [
        'pace_mismatch_index',
        'defensive_scheme_compatibility',
        'turnover_creation_diff',
        'rebounding_advantage_diff'
    ],
    'situational': [
        'rest_advantage_diff',
        'travel_fatigue_diff',
        'altitude_impact_diff',
        'home_court_advantage'
    ],
    'market_indicators': [
        'line_movement_volatility',
        'sharp_money_indicator',
        'public_betting_percentage',
        'reverse_line_movement'
    ]
}
