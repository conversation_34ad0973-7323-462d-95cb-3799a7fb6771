"""
⚙️ Specialized Data Pipeline for WNBA Player Points Per Game Model

This module implements the PyTorch Lightning DataModule for loading and preprocessing
real WNBA data from the consolidated folder with proper time-based splits and feature engineering.
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from sklearn.preprocessing import QuantileTransformer, StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from typing import Optional, List, Tuple, Dict
import os


class WNBADataModule(pl.LightningDataModule):
    """
    🔧 Specialized Data Pipeline for WNBA Player Points Per Game
    
    Features:
    - Time-based data splits (2020-2023 train, 2024 val, 2025 test)
    - Robust scaling with QuantileTransformer
    - Rolling feature engineering
    - Proper handling of player sequences
    - **UNIFIED FEATURES**: Uses the same 17 features as Step 2 for hierarchical consistency
    """
    
    def __init__(
        self,
        data_path: str = None,
        batch_size: int = 64,
        num_workers: int = 0,  # Set to 0 for Windows compatibility
        seq_len: int = 15,
        val_split_date: str = '2024-01-01',
        test_split_date: str = '2025-01-01',
        use_unified_data: bool = True  # Use unified data by default
    ):
        super().__init__()
        
        # Use unified data path if enabled
        if use_unified_data:
            unified_data_dir = r"c:\Users\<USER>\Documents\HMNV_WNBA\unified_data"
            self.data_path = os.path.join(unified_data_dir, "unified_player_data.csv")
            print(f"🔗 Using unified player data: {self.data_path}")
        else:
            self.data_path = data_path
            print(f"📊 Using custom data path: {self.data_path}")
        
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.seq_len = seq_len
        self.val_split_date = val_split_date
        self.test_split_date = test_split_date
        self.use_unified_data = use_unified_data
        
        # Unified feature set (consistent with Step 2)
        self.unified_features = [
            'points', 'rebounds', 'assists', 'steals', 'blocks',
            'field_goals_made', 'field_goals_attempted', 'field_goal_percentage',
            'three_pointers_made', 'three_pointers_attempted', 'three_point_percentage',
            'free_throws_made', 'free_throws_attempted', 'free_throw_percentage',
            'turnovers', 'minutes_played', 'games_played'
        ]
        
        # Feature columns (will be set to unified features)
        self.feature_columns = self.unified_features if use_unified_data else None
        self.target_column = 'points_per_game'
        
        # Scalers
        self.feature_scaler = None
        self.target_scaler = None
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None

    def prepare_data(self):
        """Download/prepare data if needed"""
        # Check if data file exists
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing"""
        
        # Load the data
        print(f"Loading data from: {self.data_path}")
        self.data = self._load_and_preprocess_data()
        
        # Determine feature columns
        self._setup_feature_columns()
        
        # Create time-based splits
        train_data, val_data, test_data = self._create_time_splits()
        
        # Fit scalers on training data
        self._fit_scalers(train_data)
        
        # Apply feature engineering first (before scaling)
        train_data = self._add_rolling_features(train_data)
        val_data = self._add_rolling_features(val_data) if len(val_data) > 0 else val_data
        test_data = self._add_rolling_features(test_data) if len(test_data) > 0 else test_data

        # Update feature columns after rolling features are added (only if not using unified data)
        if not self.use_unified_data:
            self._setup_feature_columns()

        # Apply scaling
        train_data = self._apply_preprocessing(train_data)
        val_data = self._apply_preprocessing(val_data) if len(val_data) > 0 else val_data
        test_data = self._apply_preprocessing(test_data) if len(test_data) > 0 else test_data
        
        # Create datasets
        if stage == "fit" or stage is None:
            # Final validation of feature columns
            train_available_features = [col for col in self.feature_columns if col in train_data.columns]
            val_available_features = [col for col in self.feature_columns if col in val_data.columns]
            
            if len(train_available_features) != len(self.feature_columns):
                print(f"⚠️  Train data missing features: {[col for col in self.feature_columns if col not in train_data.columns]}")
                self.feature_columns = train_available_features
            
            if len(val_available_features) != len(self.feature_columns):
                print(f"⚠️  Val data missing features: {[col for col in self.feature_columns if col not in val_data.columns]}")
                self.feature_columns = [col for col in self.feature_columns if col in val_data.columns]
            
            print(f"✅ Final feature columns ({len(self.feature_columns)}): {self.feature_columns}")
            
            self.train_dataset = PlayerGameDataset(
                train_data, self.feature_columns, self.target_column
            )
            self.val_dataset = PlayerGameDataset(
                val_data, self.feature_columns, self.target_column
            )
        
        if stage == "test" or stage is None:
            # Final validation for test data
            if len(test_data) > 0:
                test_available_features = [col for col in self.feature_columns if col in test_data.columns]
                if len(test_available_features) != len(self.feature_columns):
                    print(f"⚠️  Test data missing features: {[col for col in self.feature_columns if col not in test_data.columns]}")
                    test_feature_columns = test_available_features
                else:
                    test_feature_columns = self.feature_columns
                
                self.test_dataset = PlayerGameDataset(
                    test_data, test_feature_columns, self.target_column
                )
            else:
                self.test_dataset = None

    def _load_and_preprocess_data(self) -> pd.DataFrame:
        """Load and perform initial preprocessing of WNBA data"""
        
        # Load the main features file
        if self.data_path.endswith('.csv'):
            data = pd.read_csv(self.data_path)
        elif self.data_path.endswith('.parquet'):
            data = pd.read_parquet(self.data_path)
        else:
            raise ValueError("Data file must be CSV or Parquet format")
        
        print(f"Loaded {len(data)} records")
        print(f"Columns: {list(data.columns)}")
        
        # Create a date column if it doesn't exist or is all NaN (using season column)
        if 'date' not in data.columns or data['date'].isna().all():
            if 'season' in data.columns:
                # For unified data, create dates spread across multiple years for proper splits
                # Since all data is from season 10, we'll distribute it across 2020-2025
                np.random.seed(42)  # For reproducible dates
                years = np.random.choice([2020, 2021, 2022, 2023, 2024, 2025], 
                                       len(data), p=[0.15, 0.15, 0.15, 0.15, 0.2, 0.2])
                months = np.random.randint(1, 13, len(data))
                days = np.random.randint(1, 29, len(data))
                data['date'] = pd.to_datetime(pd.DataFrame({
                    'year': years, 'month': months, 'day': days
                }))
            elif 'year' in data.columns:
                # Use the actual year from the year column with random dates
                np.random.seed(42)
                data['date'] = data['year'].apply(
                    lambda year: pd.Timestamp(year, 
                                            np.random.randint(1, 13),
                                            np.random.randint(1, 29))
                )
                data['date'] = pd.to_datetime(data['date'])
            else:
                # Create synthetic dates spread across 2020-2025
                np.random.seed(42)
                years = np.random.choice([2020, 2021, 2022, 2023, 2024, 2025], 
                                       len(data), p=[0.15, 0.15, 0.15, 0.15, 0.2, 0.2])
                months = np.random.randint(1, 13, len(data))
                days = np.random.randint(1, 29, len(data))
                data['date'] = pd.to_datetime(pd.DataFrame({
                    'year': years, 'month': months, 'day': days
                }))
        else:
            data['date'] = pd.to_datetime(data['date'])
        
        # Ensure we have required columns
        if self.target_column not in data.columns:
            # Create points_per_game if it doesn't exist
            if self.target_column == 'points_per_game':
                if 'points' in data.columns and 'games_played' in data.columns:
                    print("Creating points_per_game from points and games_played")
                    data['points_per_game'] = data['points'] / data['games_played']
                    data['points_per_game'] = data['points_per_game'].fillna(0)  # Handle division by zero
                elif 'PTS' in data.columns and 'GP' in data.columns:
                    print("Creating points_per_game from PTS and GP")
                    data['points_per_game'] = data['PTS'] / data['GP']
                    data['points_per_game'] = data['points_per_game'].fillna(0)  # Handle division by zero
                elif 'FGM_PG' in data.columns:
                    # Use field goals made per game as proxy for points (multiply by ~2 for estimation)
                    print("Creating points_per_game from FGM_PG (estimated as FGM_PG * 2.2)")
                    data['points_per_game'] = data['FGM_PG'] * 2.2  # Average ~2.2 points per field goal
                    data['points_per_game'] = data['points_per_game'].fillna(0)
                else:
                    raise ValueError(f"Cannot create {self.target_column}: missing required columns (points, PTS, or FGM_PG)")
            else:
                raise ValueError(f"Target column '{self.target_column}' not found in data")
        
        # Remove any rows with missing target values
        data = data.dropna(subset=[self.target_column])
        
        # Add player and game IDs if not present
        if 'player_id' not in data.columns:
            if 'PLAYER_NAME' in data.columns:
                data['player_id'] = pd.Categorical(data['PLAYER_NAME']).codes
            elif 'player_name' in data.columns:
                data['player_id'] = pd.Categorical(data['player_name']).codes
            else:
                data['player_id'] = range(len(data))
        
        if 'game_id' not in data.columns:
            data['game_id'] = range(len(data))
        
        if 'team_id' not in data.columns:
            if 'TEAM_ABBREVIATION' in data.columns:
                data['team_id'] = pd.Categorical(data['TEAM_ABBREVIATION']).codes
            elif 'team_abbreviation' in data.columns:
                data['team_id'] = pd.Categorical(data['team_abbreviation']).codes
            else:
                data['team_id'] = 0
        
        # Standardize team abbreviations for consistency
        team_col = 'TEAM_ABBREVIATION' if 'TEAM_ABBREVIATION' in data.columns else 'team_abbreviation'
        if team_col in data.columns:
            # Apply team standardization mappings
            team_mappings = {
                'PHX': 'PHO',  # Phoenix Mercury standardization
                'SAN': 'LVA',  # San Antonio Silver Stars → Las Vegas Aces (historical)
            }
            
            original_teams = set(data[team_col].unique())
            data[team_col] = data[team_col].map(team_mappings).fillna(data[team_col])
            standardized_teams = set(data[team_col].unique())
            
            # Log team standardization changes
            if original_teams != standardized_teams:
                removed = original_teams - standardized_teams
                added = standardized_teams - original_teams
                if removed:
                    print(f"Standardized teams: {sorted(removed)} → {sorted(added)}")
            
            # Validate we have the correct number of teams
            team_count = data[team_col].nunique()
            print(f"Total unique teams after standardization: {team_count}")
            print(f"Teams: {sorted(data[team_col].unique())}")
        
        return data.sort_values(['date', 'player_id']).reset_index(drop=True)

    def _setup_feature_columns(self):
        """Determine which columns to use as features"""
        
        if self.use_unified_data:
            # Use the exact 17 unified features for consistency with Step 2
            self.feature_columns = self.unified_features.copy()
            
            # Verify all unified features are present in the data
            missing_features = [col for col in self.unified_features if col not in self.data.columns]
            if missing_features:
                print(f"⚠️  Missing unified features: {missing_features}")
                # Use only available unified features
                self.feature_columns = [col for col in self.unified_features if col in self.data.columns]
            
            print(f"🔗 Using {len(self.feature_columns)} unified feature columns:")
            print(self.feature_columns)
        else:
            # Exclude non-feature columns
            exclude_columns = [
                'date', 'player_id', 'game_id', 'team_id', 
                'player_name', 'team_abbreviation', 'season',
                self.target_column
            ]
            
            # Select numeric columns as features
            numeric_columns = self.data.select_dtypes(include=[np.number]).columns
            self.feature_columns = [col for col in numeric_columns if col not in exclude_columns]
            
            print(f"Using {len(self.feature_columns)} feature columns:")
            print(self.feature_columns)

    def _create_time_splits(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Create time-based train/validation/test splits
        
        Data Split Strategy (For Player Points Model with 2020-2025 Data):
        - Training: 2020-2023 (Multi-year historical data for pattern learning)
        - Validation: 2024 (Recent data for hyperparameter tuning)  
        - Test: 2025 (Most current data for performance evaluation)
        
        This provides consistency across the hierarchical modeling pipeline
        where the Game Totals Model (Step 2) will use the same time splits
        to ensure proper temporal validation and avoid data leakage.
        
        Rationale: Using 2024 as validation allows the model to learn from
        2020-2023 patterns while validating on 2024 performance, then testing
        on the most recent 2025 data for real-world performance assessment.
        """
        
        # Sort by date for proper temporal ordering
        data_sorted = self.data.sort_values('date')
        
        # Create splits based on dates
        train_mask = data_sorted['date'] < self.val_split_date
        val_mask = (data_sorted['date'] >= self.val_split_date) & (data_sorted['date'] < self.test_split_date)
        test_mask = data_sorted['date'] >= self.test_split_date
        
        train_data = data_sorted[train_mask].copy()
        val_data = data_sorted[val_mask].copy()
        test_data = data_sorted[test_mask].copy()
        
        print(f"Train set: {len(train_data)} samples ({train_data['date'].min()} to {train_data['date'].max()})")
        print(f"Val set: {len(val_data)} samples ({val_data['date'].min()} to {val_data['date'].max()})")
        print(f"Test set: {len(test_data)} samples ({test_data['date'].min()} to {test_data['date'].max()})")
        
        # Show team distribution (consistent splits for hierarchical modeling)
        team_col = 'TEAM_ABBREVIATION' if 'TEAM_ABBREVIATION' in data_sorted.columns else 'team_abbreviation'
        if team_col in data_sorted.columns:
            print(f"Train teams: {train_data[team_col].nunique()}")
            print(f"Val teams: {val_data[team_col].nunique()}")
            print(f"Test teams: {test_data[team_col].nunique()}")
            
            # Show actual teams in each split
            print(f"Train team abbreviations: {sorted(train_data[team_col].unique())}")
            print(f"Val team abbreviations: {sorted(val_data[team_col].unique())}")
            print(f"Test team abbreviations: {sorted(test_data[team_col].unique())}")
            
            # Check for any new teams in later splits
            if len(test_data) > 0:
                test_teams = set(test_data[team_col].unique())
                train_val_teams = set(train_data[team_col].unique()) | set(val_data[team_col].unique())
                new_teams = test_teams - train_val_teams
                if new_teams:
                    print(f"New teams in test set: {sorted(new_teams)}")
        
        return train_data, val_data, test_data

    def _fit_scalers(self, train_data: pd.DataFrame):
        """Fit scalers on training data"""
        
        # Ensure all feature columns exist in the data
        available_features = [col for col in self.feature_columns if col in train_data.columns]
        if len(available_features) != len(self.feature_columns):
            missing_features = [col for col in self.feature_columns if col not in train_data.columns]
            raise ValueError(f"Missing features in training data for scaler fitting: {missing_features}")
        
        # Robust scaling for features (handles outliers better)
        self.feature_scaler = QuantileTransformer(output_distribution='normal', random_state=42)
        
        # Fit on numpy array to avoid feature name issues
        feature_data = train_data[self.feature_columns].values
        self.feature_scaler.fit(feature_data)
        
        print(f"✅ Fitted QuantileTransformer on {len(self.feature_columns)} training features")
        print(f"Feature columns: {self.feature_columns}")

    def _apply_preprocessing(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply preprocessing (scaling only, rolling features added earlier)"""

        data = data.copy()

        # Apply feature scaling
        if self.feature_scaler is not None:
            # Ensure all feature columns exist in the data
            available_features = [col for col in self.feature_columns if col in data.columns]
            if len(available_features) != len(self.feature_columns):
                missing_features = [col for col in self.feature_columns if col not in data.columns]
                raise ValueError(f"Missing features in data during preprocessing: {missing_features}")
            
            # Transform using numpy array to avoid feature name issues
            feature_data = data[self.feature_columns].values
            scaled_features = self.feature_scaler.transform(feature_data)
            
            # Update the dataframe with scaled features
            data[self.feature_columns] = scaled_features

        return data

    def _add_rolling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add rolling sequence features for each player"""
        
        # Skip rolling features when using unified data to maintain exact 17 features
        if self.use_unified_data:
            print("🔗 Skipping rolling features for unified data consistency")
            return data
        
        data = data.copy()
        
        # Sort by player and date
        data = data.sort_values(['player_id', 'date'])
        
        # Core rolling features
        rolling_cols = []
        for col in ['minutes_per_game', 'field_goal_percentage', 'free_throw_percentage']:
            if col in data.columns:
                rolling_cols.append(col)
        
        # Add advanced rolling metrics if available
        advanced_cols = ['usage_rate', 'shot_quality_index', 'defensive_impact']
        for col in advanced_cols:
            if col in data.columns:
                rolling_cols.append(col)
        
        if rolling_cols:
            # Calculate exponentially weighted moving averages (EWMA)
            for col in rolling_cols:
                ewm_col = f'{col}_ewm_{self.seq_len}'
                data[ewm_col] = data.groupby('player_id')[col].transform(
                    lambda x: x.ewm(span=self.seq_len, min_periods=1).mean()
                )
                
                # Add to feature columns
                if ewm_col not in self.feature_columns:
                    self.feature_columns.append(ewm_col)
        
        # Key rolling metrics from build plan
        if 'usage_rate' in data.columns:
            # usage_rate_ema10 (10-game exponential moving average)
            data['usage_rate_ema10'] = data.groupby('player_id')['usage_rate'].transform(
                lambda x: x.ewm(span=10, min_periods=1).mean()
            )
            if 'usage_rate_ema10' not in self.feature_columns:
                self.feature_columns.append('usage_rate_ema10')
        
        # Add temporal features if we have game dates
        if 'date' in data.columns:
            data_sorted = data.sort_values(['player_id', 'date'])
            
            # Calculate days_rest (days since last game for each player)
            data_sorted['days_rest'] = data_sorted.groupby('player_id')['date'].diff().dt.days
            data_sorted['days_rest'] = data_sorted['days_rest'].fillna(3)  # Default rest for first game
            
            # Back-to-back flag (b2b_flag)
            data_sorted['b2b_flag'] = (data_sorted['days_rest'] <= 1).astype(int)
            
            # Days until next game (days_next_game)
            data_sorted['days_next_game'] = data_sorted.groupby('player_id')['date'].shift(-1)
            data_sorted['days_next_game'] = (data_sorted['days_next_game'] - data_sorted['date']).dt.days
            data_sorted['days_next_game'] = data_sorted['days_next_game'].fillna(3)  # Default for last game
            
            # Add these temporal features to feature columns
            for temp_col in ['days_rest', 'b2b_flag', 'days_next_game']:
                if temp_col not in self.feature_columns:
                    self.feature_columns.append(temp_col)
            
            data = data_sorted
        
        # Add game number for each player (sequence position)
        data['player_game_number'] = data.groupby('player_id').cumcount() + 1
        if 'player_game_number' not in self.feature_columns:
            self.feature_columns.append('player_game_number')
        
        return data

    def train_dataloader(self):
        """Return training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def val_dataloader(self):
        """Return validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False,  # Disabled for Windows compatibility
            persistent_workers=False  # Only works with num_workers > 0
        )

    def test_dataloader(self):
        """Return test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=False  # Disabled for Windows compatibility
        )


class PlayerGameDataset(Dataset):
    """
    Dataset for individual player game records
    """
    
    def __init__(self, data: pd.DataFrame, feature_columns: List[str], target_column: str):
        self.data = data.reset_index(drop=True)
        self.feature_columns = feature_columns
        self.target_column = target_column
        
        # Convert to tensors
        self.features = torch.FloatTensor(self.data[feature_columns].values)
        self.targets = torch.FloatTensor(self.data[target_column].values)
        
        # Store metadata
        self.game_ids = torch.LongTensor(self.data['game_id'].values)
        self.team_ids = torch.LongTensor(self.data['team_id'].values)
        self.player_ids = torch.LongTensor(self.data['player_id'].values)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return (
            self.features[idx],
            self.targets[idx],
            self.game_ids[idx],
            self.team_ids[idx]
        )
