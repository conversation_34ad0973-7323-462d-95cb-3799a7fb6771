"""
⚙️ Specialized Data Pipeline for WNBA Player Points Model

This module implements the PyTorch Lightning DataModule for loading and preprocessing
real WNBA data from the consolidated folder with proper time-based splits and feature engineering.
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from sklearn.preprocessing import QuantileTransformer, StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from typing import Optional, List, Tuple, Dict
import os


class WNBADataModule(pl.LightningDataModule):
    """
    🔧 Specialized Data Pipeline for WNBA Player Points
    
    Features:
    - Time-based data splits (2015-2022 train, 2023 val, 2024-2025 test)
    - Robust scaling with QuantileTransformer
    - Rolling feature engineering
    - Proper handling of player sequences
    """
    
    def __init__(
        self,
        data_path: str,
        batch_size: int = 64,
        num_workers: int = 4,
        seq_len: int = 15,
        val_split_date: str = '2023-01-01',
        test_split_date: str = '2024-01-01'
    ):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.seq_len = seq_len
        self.val_split_date = val_split_date
        self.test_split_date = test_split_date
        
        # Feature columns (will be determined from data)
        self.feature_columns = None
        self.target_column = 'points'
        
        # Scalers
        self.feature_scaler = None
        self.target_scaler = None
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None

    def prepare_data(self):
        """Download/prepare data if needed"""
        # Check if data file exists
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

    def setup(self, stage: Optional[str] = None):
        """Setup datasets for training, validation, and testing"""
        
        # Load the data
        print(f"Loading data from: {self.data_path}")
        self.data = self._load_and_preprocess_data()
        
        # Determine feature columns
        self._setup_feature_columns()
        
        # Create time-based splits
        train_data, val_data, test_data = self._create_time_splits()
        
        # Fit scalers on training data
        self._fit_scalers(train_data)
        
        # Apply scaling and feature engineering
        train_data = self._apply_preprocessing(train_data)
        val_data = self._apply_preprocessing(val_data)
        test_data = self._apply_preprocessing(test_data)
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = PlayerGameDataset(
                train_data, self.feature_columns, self.target_column
            )
            self.val_dataset = PlayerGameDataset(
                val_data, self.feature_columns, self.target_column
            )
        
        if stage == "test" or stage is None:
            self.test_dataset = PlayerGameDataset(
                test_data, self.feature_columns, self.target_column
            )

    def _load_and_preprocess_data(self) -> pd.DataFrame:
        """Load and perform initial preprocessing of WNBA data"""
        
        # Load the main features file
        if self.data_path.endswith('.csv'):
            data = pd.read_csv(self.data_path)
        elif self.data_path.endswith('.parquet'):
            data = pd.read_parquet(self.data_path)
        else:
            raise ValueError("Data file must be CSV or Parquet format")
        
        print(f"Loaded {len(data)} records")
        print(f"Columns: {list(data.columns)}")
        
        # Create a date column if it doesn't exist (using season info)
        if 'date' not in data.columns and 'season' in data.columns:
            # Map season numbers to actual years (season 10 = 2024, etc.)
            # Assuming season 10 represents 2024 season
            base_year = 2024
            season_offset = 10
            data['date'] = data['season'].apply(lambda x: f"{base_year - (season_offset - x)}-01-01")
            data['date'] = pd.to_datetime(data['date'])
        elif 'date' not in data.columns:
            # Create synthetic dates for time-based splitting
            data['date'] = pd.date_range('2015-01-01', periods=len(data), freq='D')
        else:
            data['date'] = pd.to_datetime(data['date'])
        
        # Ensure we have required columns
        if self.target_column not in data.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in data")
        
        # Remove any rows with missing target values
        data = data.dropna(subset=[self.target_column])
        
        # Add player and game IDs if not present
        if 'player_id' not in data.columns:
            if 'player_name' in data.columns:
                data['player_id'] = pd.Categorical(data['player_name']).codes
            else:
                data['player_id'] = range(len(data))
        
        if 'game_id' not in data.columns:
            data['game_id'] = range(len(data))
        
        if 'team_id' not in data.columns:
            if 'team_abbreviation' in data.columns:
                data['team_id'] = pd.Categorical(data['team_abbreviation']).codes
            else:
                data['team_id'] = 0
        
        return data.sort_values(['date', 'player_id']).reset_index(drop=True)

    def _setup_feature_columns(self):
        """Determine which columns to use as features"""
        
        # Exclude non-feature columns
        exclude_columns = [
            'date', 'player_id', 'game_id', 'team_id', 
            'player_name', 'team_abbreviation', 'season',
            self.target_column
        ]
        
        # Select numeric columns as features
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        self.feature_columns = [col for col in numeric_columns if col not in exclude_columns]
        
        print(f"Using {len(self.feature_columns)} feature columns:")
        print(self.feature_columns)

    def _create_time_splits(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Create train/validation/test splits

        Since we have single season data, use random splits with stratification
        to ensure balanced distribution across teams and player types.
        """

        # Sort by player_id for consistency
        data_sorted = self.data.sort_values(['player_id', 'team_id'])

        # For single season data, use percentage-based splits
        n_total = len(data_sorted)
        n_train = int(0.7 * n_total)  # 70% train
        n_val = int(0.15 * n_total)   # 15% validation
        # Remaining 15% for test

        # Create stratified splits to ensure team representation
        from sklearn.model_selection import train_test_split

        # First split: train vs (val + test)
        train_data, temp_data = train_test_split(
            data_sorted,
            test_size=0.3,
            stratify=data_sorted['team_abbreviation'],
            random_state=42
        )

        # Second split: val vs test
        val_data, test_data = train_test_split(
            temp_data,
            test_size=0.5,  # 50% of 30% = 15% of total
            stratify=temp_data['team_abbreviation'],
            random_state=42
        )

        print(f"Train set: {len(train_data)} samples ({len(train_data)/n_total*100:.1f}%)")
        print(f"Val set: {len(val_data)} samples ({len(val_data)/n_total*100:.1f}%)")
        print(f"Test set: {len(test_data)} samples ({len(test_data)/n_total*100:.1f}%)")

        # Show team distribution
        print(f"Train teams: {train_data['team_abbreviation'].nunique()}")
        print(f"Val teams: {val_data['team_abbreviation'].nunique()}")
        print(f"Test teams: {test_data['team_abbreviation'].nunique()}")

        return train_data, val_data, test_data

    def _fit_scalers(self, train_data: pd.DataFrame):
        """Fit scalers on training data"""
        
        # Robust scaling for features (handles outliers better)
        self.feature_scaler = QuantileTransformer(output_distribution='normal', random_state=42)
        self.feature_scaler.fit(train_data[self.feature_columns])
        
        print("Fitted QuantileTransformer on training features")

    def _apply_preprocessing(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply preprocessing including scaling and feature engineering"""
        
        data = data.copy()
        
        # Apply feature scaling
        if self.feature_scaler is not None:
            data[self.feature_columns] = self.feature_scaler.transform(data[self.feature_columns])
        
        # Add rolling features (player-specific)
        data = self._add_rolling_features(data)
        
        return data

    def _add_rolling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add rolling sequence features for each player"""
        
        data = data.copy()
        
        # Sort by player and date
        data = data.sort_values(['player_id', 'date'])
        
        # Rolling features to add
        rolling_cols = []
        for col in ['minutes_per_game', 'field_goal_percentage', 'free_throw_percentage']:
            if col in data.columns:
                rolling_cols.append(col)
        
        if rolling_cols:
            # Calculate exponentially weighted moving averages
            for col in rolling_cols:
                ewm_col = f'{col}_ewm_{self.seq_len}'
                data[ewm_col] = data.groupby('player_id')[col].transform(
                    lambda x: x.ewm(span=self.seq_len, min_periods=1).mean()
                )
                
                # Add to feature columns
                if ewm_col not in self.feature_columns:
                    self.feature_columns.append(ewm_col)
        
        # Add game number for each player (sequence position)
        data['player_game_number'] = data.groupby('player_id').cumcount() + 1
        if 'player_game_number' not in self.feature_columns:
            self.feature_columns.append('player_game_number')
        
        return data

    def train_dataloader(self):
        """Return training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True
        )

    def val_dataloader(self):
        """Return validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )

    def test_dataloader(self):
        """Return test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )


class PlayerGameDataset(Dataset):
    """
    Dataset for individual player game records
    """
    
    def __init__(self, data: pd.DataFrame, feature_columns: List[str], target_column: str):
        self.data = data.reset_index(drop=True)
        self.feature_columns = feature_columns
        self.target_column = target_column
        
        # Convert to tensors
        self.features = torch.FloatTensor(self.data[feature_columns].values)
        self.targets = torch.FloatTensor(self.data[target_column].values)
        
        # Store metadata
        self.game_ids = torch.LongTensor(self.data['game_id'].values)
        self.team_ids = torch.LongTensor(self.data['team_id'].values)
        self.player_ids = torch.LongTensor(self.data['player_id'].values)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return (
            self.features[idx],
            self.targets[idx],
            self.game_ids[idx],
            self.team_ids[idx]
        )
