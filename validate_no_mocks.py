#!/usr/bin/env python3
"""
Validation script to check for remaining hard-coded values, mock data, and placeholders
"""
import os
import re
import sys
from pathlib import Path

# Patterns to search for
PROBLEMATIC_PATTERNS = [
    # Mock/dummy/sample data patterns
    (r'_create_sample_data', 'Sample data generation'),
    (r'create_sample_data', 'Sample data generation'),
    (r'DummyTotalsModel', 'Dummy model usage'),
    (r'sample_data/', 'Sample data directory reference'),
    (r'mock|Mock', 'Mock data/models'),
    
    # Placeholder patterns
    (r'torch\.randn\(', 'Random tensor placeholders'),
    (r'torch\.zeros.*# Placeholder', 'Zero tensor placeholders'),
    (r'# Placeholder', 'Placeholder comments'),
    (r'# TODO', 'TODO comments'),
    (r'# FIXME', 'FIXME comments'),
    
    # Hard-coded values that should be data-driven
    (r'0\.75.*# hardcoded', 'Hard-coded constants'),
    (r'0\.25.*# hardcoded', 'Hard-coded constants'),
    (r'np\.random\.uniform\(0\.25', 'Hard-coded random ranges'),
    (r'np\.random\.normal\(0\.25', 'Hard-coded random ranges'),
    
    # Demo/test data patterns
    (r'demo_data|test_data.*torch\.randn', 'Demo data with random values'),
    (r'for.*demo', 'Demo code blocks'),
]

def scan_file(file_path: Path) -> dict:
    """Scan a file for problematic patterns"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        for i, line in enumerate(lines, 1):
            for pattern, description in PROBLEMATIC_PATTERNS:
                if re.search(pattern, line, re.IGNORECASE):
                    issues.append({
                        'line': i,
                        'content': line.strip(),
                        'pattern': pattern,
                        'description': description
                    })
                    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return issues

def scan_directory(directory: Path) -> dict:
    """Scan all Python files in a directory"""
    results = {}
    
    for file_path in directory.rglob('*.py'):
        # Skip __pycache__ and other non-source files
        if '__pycache__' in str(file_path) or 'test_' in file_path.name:
            continue
            
        issues = scan_file(file_path)
        if issues:
            results[str(file_path)] = issues
            
    return results

def main():
    """Main validation function"""
    print("🔍 HMNV WNBA PIPELINE - VALIDATION SCAN")
    print("=" * 60)
    
    # Define directories to scan
    base_dir = Path(".")
    step_dirs = [
        "step_01_player_points_model",
        "step_02_game_totals_model", 
        "step_03_moneyline_model",
        "step_04_rebounds_model",
        "step_05_spread_model",
        "step_06_assists_model",
        "step_07_threes_model",
        "step_08_steals_blocks_model"
    ]
    
    total_issues = 0
    critical_issues = 0
    
    for step_dir in step_dirs:
        step_path = base_dir / step_dir
        if not step_path.exists():
            print(f"⚠️  {step_dir} not found")
            continue
            
        print(f"\n📁 Scanning {step_dir}...")
        issues = scan_directory(step_path)
        
        if not issues:
            print(f"✅ {step_dir}: Clean")
        else:
            step_issue_count = sum(len(file_issues) for file_issues in issues.values())
            total_issues += step_issue_count
            
            print(f"🚨 {step_dir}: {step_issue_count} issues found")
            
            for file_path, file_issues in issues.items():
                rel_path = os.path.relpath(file_path, base_dir)
                print(f"  📄 {rel_path}:")
                
                for issue in file_issues:
                    print(f"    Line {issue['line']}: {issue['description']}")
                    print(f"      {issue['content']}")
                    
                    # Mark critical issues
                    if any(critical in issue['pattern'] for critical in ['randn', 'DummyTotalsModel', '_create_sample_data']):
                        critical_issues += 1
                        print(f"      🚨 CRITICAL: This must be fixed before production")
                    
                    print()
    
    print(f"\n📊 SUMMARY")
    print(f"Total issues found: {total_issues}")
    print(f"Critical issues: {critical_issues}")
    
    if critical_issues > 0:
        print(f"\n🚨 {critical_issues} CRITICAL ISSUES must be resolved before production!")
        print("Critical issues include:")
        print("  - Random tensor placeholders (torch.randn)")
        print("  - Dummy model usage (DummyTotalsModel)")
        print("  - Sample data generation (_create_sample_data)")
        return 1
    elif total_issues > 0:
        print(f"\n⚠️  {total_issues} issues found but none are critical")
        print("Consider reviewing and fixing these for production readiness")
        return 0
    else:
        print("\n✅ All scans passed! No problematic patterns found.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
