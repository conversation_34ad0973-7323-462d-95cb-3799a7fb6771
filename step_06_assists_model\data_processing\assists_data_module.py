"""
Assists Data Module (Step 6 of 9) - Playmaking Feature Engineering
==================================================================

This module implements the data processing pipeline for the Assists Model:
- Playmaking-specific feature engineering
- Teammate interaction processing
- Usage rate and opportunity modeling
- Assist-specific validation

Key Components:
- AssistsDataModule: Main PyTorch Lightning data module
- PlaymakingFeatureEngineering: Assist-specific feature creation
- TeammateProcessor: Teammate interaction modeling
- AssistValidator: Assist-specific validation utilities
"""

import torch
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader, TensorDataset
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class PlaymakingFeatureEngineering:
    """
    Playmaking-specific feature engineering for assists predictions.
    
    Creates features that capture individual playmaking abilities,
    teammate interactions, and contextual assist opportunities.
    """
    
    def __init__(self, num_teammates: int = 4):
        self.scalers = {}
        self.fitted = False
        self.num_teammates = num_teammates
        
    def create_core_playmaking_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create core playmaking features"""
        logger.info("Creating core playmaking features...")
        
        # Usage rate and assist rate
        data['usage_rate'] = data.get('usage_rate', 0.2)
        data['assist_rate_ema10'] = data.get('assist_rate_ema10', 0.15)
        
        # Assist types
        data['potential_assists'] = data.get('potential_assists', 0.0)
        data['secondary_assists'] = data.get('secondary_assists', 0.0)
        data['hockey_assists'] = data.get('hockey_assists', 0.0)
        
        # Passing metrics
        data['pass_accuracy'] = data.get('pass_accuracy', 0.85)
        data['drive_and_kick_rate'] = data.get('drive_and_kick_rate', 0.1)
        data['pick_and_roll_frequency'] = data.get('pick_and_roll_frequency', 0.2)
        
        # Assist-to-usage ratio
        data['assist_to_usage_ratio'] = data['assist_rate_ema10'] / (data['usage_rate'] + 1e-6)
        
        return data
    
    def create_ball_handling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create ball handling features"""
        logger.info("Creating ball handling features...")
        
        # Time of possession and touches
        data['time_of_possession'] = data.get('time_of_possession', 0.0)
        data['dribbles_per_possession'] = data.get('dribbles_per_possession', 0.0)
        data['passes_per_possession'] = data.get('passes_per_possession', 0.0)
        data['touches_per_possession'] = data.get('touches_per_possession', 0.0)
        
        # Court area touches
        data['frontcourt_touches'] = data.get('frontcourt_touches', 0.0)
        data['paint_touches'] = data.get('paint_touches', 0.0)
        data['post_touches'] = data.get('post_touches', 0.0)
        data['elbow_touches'] = data.get('elbow_touches', 0.0)
        
        return data
    
    def create_playmaking_context_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create playmaking context features"""
        logger.info("Creating playmaking context features...")
        
        # Minutes and possessions
        data['minutes_per_game'] = data.get('minutes_per_game', 20.0)
        data['pace_adjusted_possessions'] = data.get('pace_adjusted_possessions', 0.0)
        
        # Teammate shooting (use actual teammate data, not hardcoded defaults)
        # These should be calculated from actual teammate performance, not defaults
        data['teammate_fg_pct'] = data.get('teammate_fg_pct', data.get('team_fg_pct', 0.45))
        data['teammate_3p_pct'] = data.get('teammate_3p_pct', data.get('team_3p_pct', 0.35))
        data['teammate_ft_pct'] = data.get('teammate_ft_pct', data.get('team_ft_pct', 0.75))
        
        # Passing opportunities
        data['open_teammate_opportunities'] = data.get('open_teammate_opportunities', 0.0)
        data['contested_pass_rate'] = data.get('contested_pass_rate', 0.2)
        
        return data
    
    def create_advanced_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create advanced assist metrics"""
        logger.info("Creating advanced assist metrics...")
        
        # Impact metrics
        data['assist_points_created'] = data.get('assist_points_created', 0.0)
        data['assist_adjusted_plus_minus'] = data.get('assist_adjusted_plus_minus', 0.0)
        data['on_off_assist_diff'] = data.get('on_off_assist_diff', 0.0)
        
        # Situational assists
        data['clutch_assist_rate'] = data.get('clutch_assist_rate', 0.0)
        data['transition_assist_rate'] = data.get('transition_assist_rate', 0.0)
        data['half_court_assist_rate'] = data.get('half_court_assist_rate', 0.0)
        
        return data
    
    def create_positional_context_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create positional context features"""
        logger.info("Creating positional context features...")
        
        # Positional versatility
        data['position_versatility'] = data.get('position_versatility', 0.0)
        data['primary_position_assists'] = data.get('primary_position_assists', 0.0)
        data['secondary_position_assists'] = data.get('secondary_position_assists', 0.0)
        
        # Play type assists
        data['off_ball_movement_assists'] = data.get('off_ball_movement_assists', 0.0)
        data['screen_assists'] = data.get('screen_assists', 0.0)
        data['cut_assists'] = data.get('cut_assists', 0.0)
        data['spot_up_assists'] = data.get('spot_up_assists', 0.0)
        
        return data
    
    def create_team_context_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create team context features"""
        logger.info("Creating team context features...")
        
        # Team metrics
        data['team_assist_rate'] = data.get('team_assist_rate', 0.6)
        data['team_pace'] = data.get('team_pace', 75.0)
        data['team_offensive_rating'] = data.get('team_offensive_rating', 105.0)
        data['team_ball_movement_index'] = data.get('team_ball_movement_index', 0.0)
        
        return data
    
    def create_teammate_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create teammate interaction features"""
        logger.info("Creating teammate features...")
        
        # For each teammate (simplified - in real implementation, this would be more complex)
        for i in range(self.num_teammates):
            # Shooting ability (use team averages instead of hardcoded defaults)
            team_fg_pct = data.get('team_fg_pct', 0.45)
            team_3p_pct = data.get('team_3p_pct', 0.35)

            data[f'teammate_{i}_catch_and_shoot_pct'] = data.get(f'teammate_{i}_catch_and_shoot_pct', team_fg_pct * 0.9)
            data[f'teammate_{i}_spot_up_pct'] = data.get(f'teammate_{i}_spot_up_pct', team_fg_pct * 0.9)
            data[f'teammate_{i}_corner_3_pct'] = data.get(f'teammate_{i}_corner_3_pct', team_3p_pct * 1.1)  # Corner 3s are easier
            data[f'teammate_{i}_open_shot_pct'] = data.get(f'teammate_{i}_open_shot_pct', team_fg_pct * 1.1)  # Open shots are easier
            data[f'teammate_{i}_finishing_ability'] = data.get(f'teammate_{i}_finishing_ability', team_fg_pct * 1.3)  # Close shots easier
            data[f'teammate_{i}_free_throw_rate'] = data.get(f'teammate_{i}_free_throw_rate', 0.2)  # This is a rate, not percentage
            
            # Movement and positioning
            data[f'teammate_{i}_gravity_score'] = data.get(f'teammate_{i}_gravity_score', 0.0)
            data[f'teammate_{i}_off_movement'] = data.get(f'teammate_{i}_off_movement', 0.0)
            data[f'teammate_{i}_screen_usage'] = data.get(f'teammate_{i}_screen_usage', 0.0)
            data[f'teammate_{i}_cutting_frequency'] = data.get(f'teammate_{i}_cutting_frequency', 0.0)
            data[f'teammate_{i}_spacing_score'] = data.get(f'teammate_{i}_spacing_score', 0.0)
            
            # Synergy indicators
            data[f'teammate_{i}_assist_reception_rate'] = data.get(f'teammate_{i}_assist_reception_rate', 0.0)
            data[f'teammate_{i}_turnover_rate'] = data.get(f'teammate_{i}_turnover_rate', 0.15)
            data[f'teammate_{i}_offensive_awareness'] = data.get(f'teammate_{i}_offensive_awareness', 0.0)
            data[f'teammate_{i}_shot_selection'] = data.get(f'teammate_{i}_shot_selection', 0.0)
            data[f'teammate_{i}_rhythm_shooting'] = data.get(f'teammate_{i}_rhythm_shooting', 0.0)
            data[f'teammate_{i}_clutch_shooting'] = data.get(f'teammate_{i}_clutch_shooting', 0.0)
        
        return data
    
    def fit_transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Fit and transform data with assist-specific features"""
        logger.info("Starting playmaking feature engineering...")
        
        # Create all feature categories
        data = self.create_core_playmaking_features(data)
        data = self.create_ball_handling_features(data)
        data = self.create_playmaking_context_features(data)
        data = self.create_advanced_metrics(data)
        data = self.create_positional_context_features(data)
        data = self.create_team_context_features(data)
        data = self.create_teammate_features(data)
        
        # Define feature columns
        from ..models.assists_model import ASSIST_FEATURES, TEAMMATE_FEATURES
        
        # Fill missing values
        for feature in ASSIST_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Create teammate feature matrix
        teammate_feature_matrix = []
        for i in range(self.num_teammates):
            teammate_features = []
            for base_feature in TEAMMATE_FEATURES:
                feature_name = f'teammate_{i}_{base_feature.replace("teammate_", "")}'
                if feature_name in data.columns:
                    teammate_features.append(data[feature_name].values)
                else:
                    teammate_features.append(np.zeros(len(data)))
            teammate_feature_matrix.append(np.column_stack(teammate_features))
        
        # Stack teammate features: [n_samples, n_teammates, n_features]
        teammate_matrix = np.stack(teammate_feature_matrix, axis=1)
        
        # Fit scalers
        if not self.fitted:
            self.scalers['assist'] = StandardScaler()
            self.scalers['teammate'] = StandardScaler()
            
            # Fit assist features
            self.scalers['assist'].fit(data[ASSIST_FEATURES])
            
            # Fit teammate features (reshape to 2D)
            teammate_2d = teammate_matrix.reshape(-1, teammate_matrix.shape[-1])
            self.scalers['teammate'].fit(teammate_2d)
            
            self.fitted = True
        
        # Transform features
        data[ASSIST_FEATURES] = self.scalers['assist'].transform(data[ASSIST_FEATURES])
        
        # Transform teammate features
        teammate_2d = teammate_matrix.reshape(-1, teammate_matrix.shape[-1])
        teammate_2d_scaled = self.scalers['teammate'].transform(teammate_2d)
        teammate_matrix_scaled = teammate_2d_scaled.reshape(teammate_matrix.shape)
        
        # Store teammate matrix in data
        data['teammate_matrix'] = [teammate_matrix_scaled[i] for i in range(len(data))]
        
        logger.info(f"Playmaking feature engineering complete. "
                   f"Assist features: {len(ASSIST_FEATURES)}, "
                   f"Teammate features: {len(TEAMMATE_FEATURES)}")
        
        return data
    
    def transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform data with fitted scalers"""
        if not self.fitted:
            raise ValueError("Must fit scalers first")
        
        # Create features
        data = self.create_core_playmaking_features(data)
        data = self.create_ball_handling_features(data)
        data = self.create_playmaking_context_features(data)
        data = self.create_advanced_metrics(data)
        data = self.create_positional_context_features(data)
        data = self.create_team_context_features(data)
        data = self.create_teammate_features(data)
        
        # Fill missing values and transform
        from ..models.assists_model import ASSIST_FEATURES, TEAMMATE_FEATURES
        
        for feature in ASSIST_FEATURES:
            if feature not in data.columns:
                data[feature] = 0.0
            data[feature] = data[feature].fillna(0.0)
        
        # Create teammate feature matrix
        teammate_feature_matrix = []
        for i in range(self.num_teammates):
            teammate_features = []
            for base_feature in TEAMMATE_FEATURES:
                feature_name = f'teammate_{i}_{base_feature.replace("teammate_", "")}'
                if feature_name in data.columns:
                    teammate_features.append(data[feature_name].values)
                else:
                    teammate_features.append(np.zeros(len(data)))
            teammate_feature_matrix.append(np.column_stack(teammate_features))
        
        teammate_matrix = np.stack(teammate_feature_matrix, axis=1)
        
        # Transform features
        data[ASSIST_FEATURES] = self.scalers['assist'].transform(data[ASSIST_FEATURES])
        
        # Transform teammate features
        teammate_2d = teammate_matrix.reshape(-1, teammate_matrix.shape[-1])
        teammate_2d_scaled = self.scalers['teammate'].transform(teammate_2d)
        teammate_matrix_scaled = teammate_2d_scaled.reshape(teammate_matrix.shape)
        
        # Store teammate matrix in data
        data['teammate_matrix'] = [teammate_matrix_scaled[i] for i in range(len(data))]
        
        return data


class AssistsDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning data module for assists prediction.
    
    Handles data loading, feature engineering, and batch creation
    for the playmaking-focused assists model.
    """
    
    def __init__(
        self,
        data_path: str = "data/assists_data.parquet",
        batch_size: int = 32,
        test_size: float = 0.2,
        val_size: float = 0.2,
        num_workers: int = 4,
        pin_memory: bool = True,
        num_teammates: int = 4
    ):
        super().__init__()
        self.data_path = Path(data_path)
        self.batch_size = batch_size
        self.test_size = test_size
        self.val_size = val_size
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        self.num_teammates = num_teammates
        
        # Data components
        self.data = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        
        # Feature engineering
        self.feature_engineer = PlaymakingFeatureEngineering(num_teammates=num_teammates)
        
        # Datasets
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
    def prepare_data(self):
        """Prepare data (download, process, etc.)"""
        logger.info("Preparing assists data...")
        
        # Create sample data if file doesn't exist
        if not self.data_path.exists():
            logger.warning("No data file found, creating sample data...")
            self.create_sample_data()
        
    def create_sample_data(self):
        """Create sample data for demonstration"""
        logger.info("Creating sample assists data...")
        
        np.random.seed(42)
        n_samples = 1000
        
        # Create sample player data
        data = {
            # Core playmaking
            'usage_rate': np.random.uniform(0.1, 0.35, n_samples),
            'assist_rate_ema10': np.random.uniform(0.05, 0.4, n_samples),
            'potential_assists': np.random.uniform(0, 5, n_samples),
            'secondary_assists': np.random.uniform(0, 3, n_samples),
            'hockey_assists': np.random.uniform(0, 2, n_samples),
            'pass_accuracy': np.random.uniform(0.7, 0.95, n_samples),
            'drive_and_kick_rate': np.random.uniform(0.05, 0.3, n_samples),
            'pick_and_roll_frequency': np.random.uniform(0.1, 0.5, n_samples),
            
            # Ball handling
            'time_of_possession': np.random.uniform(0, 8, n_samples),
            'dribbles_per_possession': np.random.uniform(0, 5, n_samples),
            'passes_per_possession': np.random.uniform(1, 6, n_samples),
            'touches_per_possession': np.random.uniform(2, 8, n_samples),
            'frontcourt_touches': np.random.uniform(0, 20, n_samples),
            'paint_touches': np.random.uniform(0, 10, n_samples),
            'post_touches': np.random.uniform(0, 5, n_samples),
            'elbow_touches': np.random.uniform(0, 8, n_samples),
            
            # Context
            'minutes_per_game': np.random.uniform(10, 35, n_samples),
            'pace_adjusted_possessions': np.random.uniform(0, 20, n_samples),
            'teammate_fg_pct': np.random.uniform(0.35, 0.55, n_samples),
            'teammate_3p_pct': np.random.uniform(0.25, 0.45, n_samples),
            'teammate_ft_pct': np.random.uniform(0.65, 0.85, n_samples),
            'open_teammate_opportunities': np.random.uniform(0, 10, n_samples),
            'contested_pass_rate': np.random.uniform(0.1, 0.4, n_samples),
            
            # Team context
            'team_assist_rate': np.random.uniform(0.5, 0.7, n_samples),
            'team_pace': np.random.uniform(70, 80, n_samples),
            'team_offensive_rating': np.random.uniform(95, 115, n_samples),
            'team_ball_movement_index': np.random.uniform(0, 5, n_samples),
        }
        
        # Add teammate features
        for i in range(self.num_teammates):
            for base_feature in ['catch_and_shoot_pct', 'spot_up_pct', 'corner_3_pct', 
                               'open_shot_pct', 'finishing_ability', 'free_throw_rate',
                               'gravity_score', 'off_movement', 'screen_usage',
                               'cutting_frequency', 'spacing_score',
                               'assist_reception_rate', 'turnover_rate',
                               'offensive_awareness', 'shot_selection',
                               'rhythm_shooting', 'clutch_shooting']:
                if 'pct' in base_feature or 'rate' in base_feature:
                    data[f'teammate_{i}_{base_feature}'] = np.random.uniform(0, 1, n_samples)
                else:
                    data[f'teammate_{i}_{base_feature}'] = np.random.uniform(0, 5, n_samples)
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Create target variable (assists)
        assists_base = df['assist_rate_ema10'] * df['minutes_per_game'] * 0.1
        usage_factor = df['usage_rate'] * 2
        teammate_factor = df['teammate_fg_pct'] * 0.5
        
        df['assists'] = assists_base + usage_factor + teammate_factor + \
                       np.random.normal(0, 0.5, n_samples)
        df['assists'] = np.clip(df['assists'], 0, 15)  # Realistic range
        
        # Save data
        self.data_path.parent.mkdir(parents=True, exist_ok=True)
        df.to_parquet(self.data_path)
        
        logger.info(f"Sample assists data created: {self.data_path}")
        
    def setup(self, stage: Optional[str] = None):
        """Setup data for training, validation, and testing"""
        logger.info("Setting up assists data module...")
        
        # Try to load from unified data structure first
        unified_data_path = Path("../consolidated_wnba")
        if unified_data_path.exists():
            logger.info("Loading from unified data structure...")
            self.data = self._load_unified_data(unified_data_path)
        else:
            # Fallback to specified data path
            try:
                if self.data_path.suffix == '.parquet':
                    self.data = pd.read_parquet(self.data_path)
                else:
                    self.data = pd.read_csv(self.data_path)
                logger.info(f"Loaded data shape: {self.data.shape}")
            except Exception as e:
                logger.error(f"Error loading data: {e}")
                # Create demo data if file doesn't exist
                logger.info("Creating demo data...")
                self.data = self._create_demo_data()
        
        # Feature engineering
        self.data = self.feature_engineer.fit_transform(self.data)
        
        # Split data
        train_val_data, self.test_data = train_test_split(
            self.data, test_size=self.test_size, random_state=42
        )
        
        self.train_data, self.val_data = train_test_split(
            train_val_data, test_size=self.val_size, random_state=42
        )
        
        logger.info(f"Data splits - Train: {len(self.train_data)}, "
                   f"Val: {len(self.val_data)}, Test: {len(self.test_data)}")
        
        # Create datasets
        if stage == "fit" or stage is None:
            self.train_dataset = self._create_dataset(self.train_data)
            self.val_dataset = self._create_dataset(self.val_data)
            
        if stage == "test" or stage is None:
            self.test_dataset = self._create_dataset(self.test_data)
    
    def _create_dataset(self, data: pd.DataFrame) -> TensorDataset:
        """Create tensor dataset from DataFrame"""
        from ..models.assists_model import ASSIST_FEATURES
        
        # Get player features
        player_features = torch.tensor(
            data[ASSIST_FEATURES].values, 
            dtype=torch.float32
        )
        
        # Get teammate features
        teammate_features = torch.tensor(
            np.stack(data['teammate_matrix'].values),
            dtype=torch.float32
        )
        
        # Target
        target = torch.tensor(data['assists'].values, dtype=torch.float32)
        
        return TensorDataset(player_features, teammate_features, target)
    
    def _collate_fn(self, batch):
        """Custom collate function for handling player and teammate features"""
        # Extract features and targets
        player_features = [item[0] for item in batch]
        teammate_features = [item[1] for item in batch]
        targets = [item[2] for item in batch]
        
        # Stack tensors
        player_features = torch.stack(player_features)
        teammate_features = torch.stack(teammate_features)
        targets = torch.stack(targets)
        
        return (player_features, teammate_features), targets
    
    def train_dataloader(self) -> DataLoader:
        """Create training dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def val_dataloader(self) -> DataLoader:
        """Create validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def test_dataloader(self) -> DataLoader:
        """Create test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def predict_dataloader(self) -> DataLoader:
        """Create prediction dataloader"""
        return self.test_dataloader()
