"""
Shot Profile Data Integration Pipeline
====================================

This module integrates real WNBA shot profile data from the consolidated_wnba
tracking data with the unified player dataset to create authentic spatial
shooting features for the threes model.

Real Data Sources:
- wnba_shots_by_zone_*.csv: Shot zone percentages (corner 3, above break, etc.)
- wnba_shots_5ft_*.csv: Shot distance percentages
- wnba_shots_8ft_*.csv: Additional distance data

Output:
- Enhanced unified_player_data.csv with real spatial shooting features
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Optional
import glob

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ShotProfileIntegrator:
    """Integrates real shot profile data with unified player data."""
    
    def __init__(self, 
                 consolidated_path: str = "../consolidated_wnba",
                 unified_path: str = "unified_player_data.csv"):
        self.consolidated_path = Path(consolidated_path)
        self.unified_path = Path(unified_path)
        self.shot_tracking_path = self.consolidated_path / "05_tracking_data" / "shot_tracking"
        
        # Real shot zone features we'll extract
        self.shot_zone_features = {
            'restricted_area': ['Restricted Area'],
            'paint_non_ra': ['In The Paint (Non-RA)'],
            'mid_range': ['Mid-Range'],
            'left_corner_3': ['Left Corner 3'],
            'right_corner_3': ['Right Corner 3'],
            'above_break_3': ['Above the Break 3'],
            'corner_3_combined': ['Corner 3']
        }
        
        # Distance ranges from shots_5ft files
        self.distance_ranges = [
            'Less Than 5 ft',
            '5-9 ft',
            '10-14 ft', 
            '15-19 ft',
            '20-24 ft',
            '25-29 ft',
            '30-34 ft',
            '35-39 ft',
            '40+ ft'
        ]
        
    def load_unified_data(self) -> pd.DataFrame:
        """Load the current unified player data."""
        logger.info(f"Loading unified data from {self.unified_path}")
        
        if not self.unified_path.exists():
            raise FileNotFoundError(f"Unified data not found: {self.unified_path}")
            
        df = pd.read_csv(self.unified_path)
        logger.info(f"Loaded {len(df)} players from unified data")
        
        return df
    
    def load_shot_zone_data(self, years: List[int] = None) -> pd.DataFrame:
        """Load real shot zone data from consolidated_wnba."""
        if years is None:
            years = list(range(2015, 2025))  # 2015-2024
            
        logger.info(f"Loading shot zone data for years: {years}")
        
        all_zone_data = []
        
        for year in years:
            zone_file = self.shot_tracking_path / f"wnba_shots_by_zone_{year}.csv"
            
            if zone_file.exists():
                try:
                    # Read the file with proper header handling
                    df = pd.read_csv(zone_file, skiprows=1)  # Skip the zone name row
                    
                    # Add year column
                    df['season'] = year
                    
                    # Clean player names and ensure we have required columns
                    if 'PLAYER_NAME' in df.columns:
                        df['player_name'] = df['PLAYER_NAME'].str.strip()
                        all_zone_data.append(df)
                        logger.info(f"Loaded {len(df)} players from {year} zone data")
                    else:
                        logger.warning(f"No PLAYER_NAME column in {zone_file}")
                        
                except Exception as e:
                    logger.error(f"Error loading {zone_file}: {e}")
            else:
                logger.warning(f"Zone file not found: {zone_file}")
        
        if not all_zone_data:
            logger.error("No shot zone data loaded!")
            return pd.DataFrame()
            
        # Combine all years
        combined_zones = pd.concat(all_zone_data, ignore_index=True)
        logger.info(f"Combined shot zone data: {len(combined_zones)} total records")
        
        return combined_zones
    
    def load_shot_distance_data(self, years: List[int] = None) -> pd.DataFrame:
        """Load real shot distance data from consolidated_wnba."""
        if years is None:
            years = list(range(2015, 2025))
            
        logger.info(f"Loading shot distance data for years: {years}")
        
        all_distance_data = []
        
        for year in years:
            distance_file = self.shot_tracking_path / f"wnba_shots_5ft_{year}.csv"
            
            if distance_file.exists():
                try:
                    # Read with proper header handling
                    df = pd.read_csv(distance_file, skiprows=1)
                    df['season'] = year
                    
                    if 'PLAYER_NAME' in df.columns:
                        df['player_name'] = df['PLAYER_NAME'].str.strip()
                        all_distance_data.append(df)
                        logger.info(f"Loaded {len(df)} players from {year} distance data")
                        
                except Exception as e:
                    logger.error(f"Error loading {distance_file}: {e}")
            else:
                logger.warning(f"Distance file not found: {distance_file}")
        
        if not all_distance_data:
            logger.warning("No shot distance data loaded")
            return pd.DataFrame()
            
        combined_distances = pd.concat(all_distance_data, ignore_index=True)
        logger.info(f"Combined shot distance data: {len(combined_distances)} total records")
        
        return combined_distances
    
    def extract_zone_features(self, zone_data: pd.DataFrame) -> pd.DataFrame:
        """Extract shot zone features from the raw zone data."""
        logger.info("Extracting shot zone features...")
        
        if zone_data.empty:
            logger.warning("No zone data to process")
            return pd.DataFrame()
        
        # Group by player and season to get latest data
        zone_features = []
        
        for (player, season), group in zone_data.groupby(['player_name', 'season']):
            if len(group) > 1:
                # Take the first record if multiple (shouldn't happen)
                group = group.iloc[:1]
            
            player_features = {
                'player_name': player,
                'season': season
            }
            
            # Extract zone shooting percentages
            # The CSV has columns like: FGM, FGA, FG_PCT for each zone
            # We need to map the column positions to zones
            
            try:
                # Restricted Area (columns 6-8: FGM, FGA, FG_PCT)
                ra_fgm = pd.to_numeric(group.iloc[0, 6], errors='coerce') or 0
                ra_fga = pd.to_numeric(group.iloc[0, 7], errors='coerce') or 0
                player_features['restricted_area_fgm'] = ra_fgm
                player_features['restricted_area_fga'] = ra_fga
                player_features['restricted_area_pct'] = pd.to_numeric(group.iloc[0, 8], errors='coerce') or 0
                
                # In The Paint Non-RA (columns 9-11)
                paint_fgm = pd.to_numeric(group.iloc[0, 9], errors='coerce') or 0
                paint_fga = pd.to_numeric(group.iloc[0, 10], errors='coerce') or 0
                player_features['paint_non_ra_fgm'] = paint_fgm
                player_features['paint_non_ra_fga'] = paint_fga
                player_features['paint_non_ra_pct'] = pd.to_numeric(group.iloc[0, 11], errors='coerce') or 0
                
                # Mid-Range (columns 12-14)
                mid_fgm = pd.to_numeric(group.iloc[0, 12], errors='coerce') or 0
                mid_fga = pd.to_numeric(group.iloc[0, 13], errors='coerce') or 0
                player_features['mid_range_fgm'] = mid_fgm
                player_features['mid_range_fga'] = mid_fga
                player_features['mid_range_pct'] = pd.to_numeric(group.iloc[0, 14], errors='coerce') or 0
                
                # Left Corner 3 (columns 15-17)
                lc3_fgm = pd.to_numeric(group.iloc[0, 15], errors='coerce') or 0
                lc3_fga = pd.to_numeric(group.iloc[0, 16], errors='coerce') or 0
                player_features['left_corner_3_fgm'] = lc3_fgm
                player_features['left_corner_3_fga'] = lc3_fga
                player_features['left_corner_3_pct'] = pd.to_numeric(group.iloc[0, 17], errors='coerce') or 0
                
                # Right Corner 3 (columns 18-20)
                rc3_fgm = pd.to_numeric(group.iloc[0, 18], errors='coerce') or 0
                rc3_fga = pd.to_numeric(group.iloc[0, 19], errors='coerce') or 0
                player_features['right_corner_3_fgm'] = rc3_fgm
                player_features['right_corner_3_fga'] = rc3_fga
                player_features['right_corner_3_pct'] = pd.to_numeric(group.iloc[0, 20], errors='coerce') or 0
                
                # Above the Break 3 (columns 21-23)
                ab3_fgm = pd.to_numeric(group.iloc[0, 21], errors='coerce') or 0
                ab3_fga = pd.to_numeric(group.iloc[0, 22], errors='coerce') or 0
                player_features['above_break_3_fgm'] = ab3_fgm
                player_features['above_break_3_fga'] = ab3_fga
                player_features['above_break_3_pct'] = pd.to_numeric(group.iloc[0, 23], errors='coerce') or 0
                
                # Combined Corner 3 (columns 27-29)
                if group.shape[1] > 27:
                    corner_fgm = pd.to_numeric(group.iloc[0, 27], errors='coerce') or (lc3_fgm + rc3_fgm)
                    corner_fga = pd.to_numeric(group.iloc[0, 28], errors='coerce') or (lc3_fga + rc3_fga)
                    player_features['corner_3_fgm'] = corner_fgm
                    player_features['corner_3_fga'] = corner_fga
                    player_features['corner_3_pct'] = pd.to_numeric(group.iloc[0, 29], errors='coerce') or 0
                else:
                    # Calculate combined corner 3
                    player_features['corner_3_fgm'] = lc3_fgm + rc3_fgm
                    player_features['corner_3_fga'] = lc3_fga + rc3_fga
                    total_corner_fga = lc3_fga + rc3_fga
                    if total_corner_fga > 0:
                        player_features['corner_3_pct'] = (lc3_fgm + rc3_fgm) / total_corner_fga
                    else:
                        player_features['corner_3_pct'] = 0
                
                zone_features.append(player_features)
                
            except Exception as e:
                logger.warning(f"Error processing zone data for {player}: {e}")
                continue
        
        if not zone_features:
            logger.warning("No zone features extracted")
            return pd.DataFrame()
            
        zone_df = pd.DataFrame(zone_features)
        logger.info(f"Extracted zone features for {len(zone_df)} player-seasons")
        
        return zone_df

    def extract_distance_features(self, distance_data: pd.DataFrame) -> pd.DataFrame:
        """Extract shot distance features from the raw distance data."""
        logger.info("Extracting shot distance features...")

        if distance_data.empty:
            logger.warning("No distance data to process")
            return pd.DataFrame()

        distance_features = []

        for (player, season), group in distance_data.groupby(['player_name', 'season']):
            if len(group) > 1:
                group = group.iloc[:1]

            player_features = {
                'player_name': player,
                'season': season
            }

            try:
                # Extract distance range shooting (columns 6+ in groups of 3: FGM, FGA, FG_PCT)
                distance_ranges = [
                    ('less_than_5ft', 6),
                    ('5_to_9ft', 9),
                    ('10_to_14ft', 12),
                    ('15_to_19ft', 15),
                    ('20_to_24ft', 18),
                    ('25_to_29ft', 21),
                    ('30_to_34ft', 24),
                    ('35_to_39ft', 27),
                    ('40_plus_ft', 30)
                ]

                for range_name, col_start in distance_ranges:
                    if group.shape[1] > col_start + 2:
                        fgm = pd.to_numeric(group.iloc[0, col_start], errors='coerce') or 0
                        fga = pd.to_numeric(group.iloc[0, col_start + 1], errors='coerce') or 0
                        pct = pd.to_numeric(group.iloc[0, col_start + 2], errors='coerce') or 0

                        player_features[f'{range_name}_fgm'] = fgm
                        player_features[f'{range_name}_fga'] = fga
                        player_features[f'{range_name}_pct'] = pct

                distance_features.append(player_features)

            except Exception as e:
                logger.warning(f"Error processing distance data for {player}: {e}")
                continue

        if not distance_features:
            logger.warning("No distance features extracted")
            return pd.DataFrame()

        distance_df = pd.DataFrame(distance_features)
        logger.info(f"Extracted distance features for {len(distance_df)} player-seasons")

        return distance_df

    def aggregate_shot_profile_by_player(self, zone_df: pd.DataFrame,
                                        distance_df: pd.DataFrame) -> tuple:
        """Aggregate shot profile data by player across all seasons."""
        logger.info("Aggregating shot profile data by player across all seasons...")

        # Aggregate zone data by player
        if not zone_df.empty:
            zone_agg = zone_df.groupby('player_name').agg({
                'restricted_area_fgm': 'sum',
                'restricted_area_fga': 'sum',
                'paint_non_ra_fgm': 'sum',
                'paint_non_ra_fga': 'sum',
                'mid_range_fgm': 'sum',
                'mid_range_fga': 'sum',
                'left_corner_3_fgm': 'sum',
                'left_corner_3_fga': 'sum',
                'right_corner_3_fgm': 'sum',
                'right_corner_3_fga': 'sum',
                'above_break_3_fgm': 'sum',
                'above_break_3_fga': 'sum',
                'corner_3_fgm': 'sum',
                'corner_3_fga': 'sum'
            }).reset_index()

            # Calculate aggregated percentages
            for zone in ['restricted_area', 'paint_non_ra', 'mid_range', 'left_corner_3',
                        'right_corner_3', 'above_break_3', 'corner_3']:
                fgm_col = f'{zone}_fgm'
                fga_col = f'{zone}_fga'
                pct_col = f'{zone}_pct'

                zone_agg[pct_col] = np.where(
                    zone_agg[fga_col] > 0,
                    zone_agg[fgm_col] / zone_agg[fga_col],
                    0
                )

            logger.info(f"Aggregated zone data for {len(zone_agg)} players")
        else:
            zone_agg = pd.DataFrame()

        # Aggregate distance data by player
        if not distance_df.empty:
            distance_cols = {}
            for range_name in ['less_than_5ft', '5_to_9ft', '10_to_14ft', '15_to_19ft',
                              '20_to_24ft', '25_to_29ft', '30_to_34ft', '35_to_39ft', '40_plus_ft']:
                distance_cols[f'{range_name}_fgm'] = 'sum'
                distance_cols[f'{range_name}_fga'] = 'sum'

            distance_agg = distance_df.groupby('player_name').agg(distance_cols).reset_index()

            # Calculate aggregated percentages
            for range_name in ['less_than_5ft', '5_to_9ft', '10_to_14ft', '15_to_19ft',
                              '20_to_24ft', '25_to_29ft', '30_to_34ft', '35_to_39ft', '40_plus_ft']:
                fgm_col = f'{range_name}_fgm'
                fga_col = f'{range_name}_fga'
                pct_col = f'{range_name}_pct'

                distance_agg[pct_col] = np.where(
                    distance_agg[fga_col] > 0,
                    distance_agg[fgm_col] / distance_agg[fga_col],
                    0
                )

            logger.info(f"Aggregated distance data for {len(distance_agg)} players")
        else:
            distance_agg = pd.DataFrame()

        return zone_agg, distance_agg

    def merge_shot_profile_data(self, unified_df: pd.DataFrame,
                               zone_df: pd.DataFrame,
                               distance_df: pd.DataFrame) -> pd.DataFrame:
        """Merge shot profile data with unified player data."""
        logger.info("Merging shot profile data with unified player data...")

        # Aggregate shot profile data by player (since unified data uses season=10)
        zone_agg, distance_agg = self.aggregate_shot_profile_by_player(zone_df, distance_df)

        # Start with unified data
        enhanced_df = unified_df.copy()

        # Merge aggregated zone data
        if not zone_agg.empty:
            logger.info(f"Merging aggregated zone data for {len(zone_agg)} players")
            enhanced_df = enhanced_df.merge(
                zone_agg,
                on='player_name',
                how='left',
                suffixes=('', '_zone')
            )
            logger.info(f"After zone merge: {len(enhanced_df)} records")

        # Merge aggregated distance data
        if not distance_agg.empty:
            logger.info(f"Merging aggregated distance data for {len(distance_agg)} players")
            enhanced_df = enhanced_df.merge(
                distance_agg,
                on='player_name',
                how='left',
                suffixes=('', '_dist')
            )
            logger.info(f"After distance merge: {len(enhanced_df)} records")

        # Fill missing shot profile data with 0
        shot_profile_columns = [col for col in enhanced_df.columns
                               if any(term in col for term in ['corner_3', 'above_break', 'restricted_area',
                                                              'paint_non_ra', 'mid_range', '_ft_'])]

        for col in shot_profile_columns:
            if enhanced_df[col].dtype in ['float64', 'int64']:
                enhanced_df[col] = enhanced_df[col].fillna(0)

        logger.info(f"Added {len(shot_profile_columns)} shot profile features")

        return enhanced_df

    def create_derived_shot_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create derived shot profile features from the raw data."""
        logger.info("Creating derived shot profile features...")

        enhanced_df = df.copy()

        # Three-point shooting efficiency by zone
        if 'corner_3_fgm' in enhanced_df.columns and 'corner_3_fga' in enhanced_df.columns:
            enhanced_df['corner_3_frequency'] = enhanced_df['corner_3_fga'] / np.maximum(enhanced_df.get('three_pointers_attempted', 1), 1)
            enhanced_df['corner_3_efficiency'] = enhanced_df['corner_3_pct'] / np.maximum(enhanced_df.get('three_point_percentage', 0.35), 0.01)

        if 'above_break_3_fgm' in enhanced_df.columns:
            enhanced_df['above_break_frequency'] = enhanced_df['above_break_3_fga'] / np.maximum(enhanced_df.get('three_pointers_attempted', 1), 1)
            enhanced_df['above_break_efficiency'] = enhanced_df['above_break_3_pct'] / np.maximum(enhanced_df.get('three_point_percentage', 0.35), 0.01)

        # Shot selection indicators
        if 'mid_range_fga' in enhanced_df.columns:
            enhanced_df['mid_range_frequency'] = enhanced_df['mid_range_fga'] / np.maximum(enhanced_df.get('field_goals_attempted', 1), 1)

        if 'restricted_area_fga' in enhanced_df.columns:
            enhanced_df['restricted_area_frequency'] = enhanced_df['restricted_area_fga'] / np.maximum(enhanced_df.get('field_goals_attempted', 1), 1)

        # Shot versatility score (how many zones a player shoots from)
        zone_columns = ['corner_3_fga', 'above_break_3_fga', 'mid_range_fga', 'restricted_area_fga', 'paint_non_ra_fga']
        available_zones = [col for col in zone_columns if col in enhanced_df.columns]

        if available_zones:
            enhanced_df['shot_versatility'] = sum([(enhanced_df[col] > 0).astype(int) for col in available_zones])

        # Three-point shot distribution
        three_point_zones = ['corner_3_fga', 'above_break_3_fga']
        available_3pt_zones = [col for col in three_point_zones if col in enhanced_df.columns]

        if len(available_3pt_zones) >= 2:
            total_3pt_attempts = sum([enhanced_df[col] for col in available_3pt_zones])
            enhanced_df['corner_3_preference'] = enhanced_df.get('corner_3_fga', 0) / np.maximum(total_3pt_attempts, 1)

        logger.info("Created derived shot profile features")

        return enhanced_df

    def integrate_shot_profile_data(self, years: List[int] = None) -> pd.DataFrame:
        """Main method to integrate all shot profile data."""
        logger.info("🏀 Starting shot profile data integration...")

        # Load unified data
        unified_df = self.load_unified_data()

        # Load shot profile data
        zone_df = self.load_shot_zone_data(years)
        distance_df = self.load_shot_distance_data(years)

        # Extract features
        zone_features = self.extract_zone_features(zone_df)
        distance_features = self.extract_distance_features(distance_df)

        # Merge all data
        enhanced_df = self.merge_shot_profile_data(unified_df, zone_features, distance_features)

        # Create derived features
        final_df = self.create_derived_shot_features(enhanced_df)

        logger.info(f"✅ Integration complete: {len(final_df)} players with shot profile data")

        return final_df

    def save_enhanced_unified_data(self, enhanced_df: pd.DataFrame,
                                  output_path: str = "unified_player_data_with_shot_profiles.csv"):
        """Save the enhanced unified data with shot profiles."""
        output_file = Path(output_path)

        logger.info(f"Saving enhanced unified data to {output_file}")
        enhanced_df.to_csv(output_file, index=False)

        # Create summary
        shot_profile_cols = [col for col in enhanced_df.columns
                            if any(term in col for term in ['corner_3', 'above_break', 'restricted_area',
                                                           'paint_non_ra', 'mid_range', '_ft_', 'versatility', 'frequency'])]

        logger.info(f"📊 Enhanced dataset summary:")
        logger.info(f"   Total players: {len(enhanced_df)}")
        logger.info(f"   Original features: {len(enhanced_df.columns) - len(shot_profile_cols)}")
        logger.info(f"   Shot profile features: {len(shot_profile_cols)}")
        logger.info(f"   Total features: {len(enhanced_df.columns)}")

        return output_file


def main():
    """Run the shot profile integration pipeline."""
    integrator = ShotProfileIntegrator()

    # Integrate shot profile data
    enhanced_df = integrator.integrate_shot_profile_data()

    # Save enhanced dataset
    output_file = integrator.save_enhanced_unified_data(enhanced_df)

    print(f"🏀 Shot profile integration complete!")
    print(f"📁 Enhanced dataset saved: {output_file}")
    print(f"📊 Total features: {len(enhanced_df.columns)}")

    # Show sample of new features
    shot_profile_cols = [col for col in enhanced_df.columns
                        if any(term in col for term in ['corner_3', 'above_break', 'restricted_area'])][:10]

    if shot_profile_cols:
        print(f"\n🎯 Sample shot profile features:")
        for col in shot_profile_cols:
            print(f"   {col}")


if __name__ == "__main__":
    main()
