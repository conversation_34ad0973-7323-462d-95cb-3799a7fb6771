"""
Player Rebounds Model (Step 4 of 9) - Position-Sensitive Architecture
========================================================================

This module implements a position-sensitive player rebounds model with:
- Position embedding for different roles (Guards, Forwards, Centers)
- Zero-Inflated Poisson output for rebound count modeling
- Opponent interaction features
- Boxout impact and opportunity index calculation
- Hierarchical feature integration from previous models

Key Components:
- PlayerReboundsModel: Main PyTorch Lightning model
- ZeroInflatedPoissonLayer: Custom output layer
- PositionEmbedding: Position-specific feature transformation
- BoxoutImpact: Opponent interaction modeling
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.distributions import Poisson, Bernoulli
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class ZeroInflatedPoissonLayer(nn.Module):
    """
    Zero-Inflated Poisson output layer for rebound modeling.
    
    Models the probability of zero rebounds separately from positive counts,
    accounting for games where players don't get rebounds due to limited minutes
    or specific game situations.
    """
    
    def __init__(self, input_dim: int):
        super().__init__()
        self.input_dim = input_dim
        
        # Gate network for zero-inflation (probability of "structural" zero)
        self.gate_net = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # Poisson rate parameter network
        self.rate_net = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Softplus()  # Ensures positive rate
        )
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass returning gate probability and Poisson rate.
        
        Args:
            x: Input features [batch_size, input_dim]
            
        Returns:
            gate_prob: Probability of structural zero [batch_size, 1]
            rate: Poisson rate parameter [batch_size, 1]
        """
        gate_prob = self.gate_net(x)
        rate = self.rate_net(x)
        
        return gate_prob, rate
    
    def sample(self, x: torch.Tensor, num_samples: int = 1) -> torch.Tensor:
        """Sample from the zero-inflated Poisson distribution."""
        gate_prob, rate = self.forward(x)
        
        # Sample from Bernoulli for gate
        gate_samples = Bernoulli(gate_prob).sample([num_samples])
        
        # Sample from Poisson for counts
        poisson_samples = Poisson(rate).sample([num_samples])
        
        # Combine: if gate=1, return 0; otherwise return Poisson sample
        samples = torch.where(gate_samples == 1, 
                             torch.zeros_like(poisson_samples), 
                             poisson_samples)
        
        return samples.squeeze(-1)


class PositionEmbedding(nn.Module):
    """
    Position-specific embedding layer for different player roles.
    
    Learns position-specific transformations for Guards, Forwards, and Centers,
    accounting for different rebounding patterns and responsibilities.
    """
    
    def __init__(self, input_dim: int, embedding_dim: int = 32):
        super().__init__()
        self.input_dim = input_dim
        self.embedding_dim = embedding_dim
        
        # Position categories: 0=Guard, 1=Forward, 2=Center
        self.position_embeddings = nn.Embedding(3, embedding_dim)
        
        # Position-specific transformations
        self.position_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, 64),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(64, 32)
            ) for _ in range(3)
        ])
        
        # Combine position embedding with transformed features
        self.combine_layer = nn.Sequential(
            nn.Linear(32 + embedding_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32)
        )
        
    def forward(self, x: torch.Tensor, position_indices: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with position-specific processing.
        
        Args:
            x: Input features [batch_size, input_dim]
            position_indices: Position categories [batch_size] (0=Guard, 1=Forward, 2=Center)
            
        Returns:
            Position-enhanced features [batch_size, 32]
        """
        batch_size = x.size(0)
        
        # Get position embeddings
        pos_embeddings = self.position_embeddings(position_indices)
        
        # Apply position-specific transformations
        transformed_features = []
        for i in range(batch_size):
            pos_idx = position_indices[i].item()
            transformed = self.position_transforms[pos_idx](x[i:i+1])
            transformed_features.append(transformed)
        
        transformed_features = torch.cat(transformed_features, dim=0)
        
        # Combine with position embeddings
        combined = torch.cat([transformed_features, pos_embeddings], dim=1)
        output = self.combine_layer(combined)
        
        return output


class BoxoutImpact(nn.Module):
    """
    Opponent interaction module for modeling boxout impact and opportunity index.
    
    Captures how opponent characteristics affect rebounding opportunities,
    including size mismatches, pace factors, and defensive rebounding strength.
    """
    
    def __init__(self, opponent_dim: int = 16):
        super().__init__()
        self.opponent_dim = opponent_dim
        
        # Opponent strength modeling
        self.opponent_encoder = nn.Sequential(
            nn.Linear(opponent_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 16)
        )
        
        # Matchup interaction
        self.matchup_net = nn.Sequential(
            nn.Linear(32 + 16, 32),  # player features + opponent features
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 16)
        )
        
        # Opportunity index calculation
        self.opportunity_net = nn.Sequential(
            nn.Linear(16, 8),
            nn.ReLU(),
            nn.Linear(8, 1),
            nn.Sigmoid()  # Opportunity index between 0 and 1
        )
        
    def forward(self, player_features: torch.Tensor, 
                opponent_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for opponent interaction modeling.
        
        Args:
            player_features: Player features [batch_size, 32]
            opponent_features: Opponent team features [batch_size, opponent_dim]
            
        Returns:
            matchup_features: Enhanced features with opponent interaction [batch_size, 16]
            opportunity_index: Rebounding opportunity score [batch_size, 1]
        """
        # Encode opponent features
        opponent_encoded = self.opponent_encoder(opponent_features)
        
        # Combine player and opponent features
        combined = torch.cat([player_features, opponent_encoded], dim=1)
        matchup_features = self.matchup_net(combined)
        
        # Calculate opportunity index
        opportunity_index = self.opportunity_net(matchup_features)
        
        return matchup_features, opportunity_index


class PlayerReboundsModel(pl.LightningModule):
    """
    Main Player Rebounds Model with position-sensitive architecture.
    
    Integrates hierarchical features from previous models with position-specific
    processing, opponent interaction, and Zero-Inflated Poisson output.
    """
    
    def __init__(
        self,
        input_dim: int,
        position_embedding_dim: int = 32,
        opponent_dim: int = 16,
        hidden_dim: int = 128,
        learning_rate: float = 0.001,
        weight_decay: float = 0.01,
        dropout_rate: float = 0.3,
        **kwargs
    ):
        super().__init__()
        self.save_hyperparameters()
        
        self.input_dim = input_dim
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        
        # Position embedding layer
        self.position_embedding = PositionEmbedding(
            input_dim=input_dim,
            embedding_dim=position_embedding_dim
        )
        
        # Main feature processing network
        self.feature_net = nn.Sequential(
            nn.Linear(32, hidden_dim),  # From position embedding
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, 64)
        )
        
        # Opponent interaction module
        self.boxout_impact = BoxoutImpact(opponent_dim=opponent_dim)
        
        # Final combination layer
        self.combine_net = nn.Sequential(
            nn.Linear(64 + 16 + 1, 64),  # features + matchup + opportunity
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(64, 32)
        )
        
        # Zero-Inflated Poisson output
        self.zip_layer = ZeroInflatedPoissonLayer(input_dim=32)
        
        # Training metrics
        self.train_losses = []
        self.val_losses = []
        
    def forward(self, x: torch.Tensor, position_indices: torch.Tensor,
                opponent_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the complete model.
        
        Args:
            x: Input features [batch_size, input_dim]
            position_indices: Position categories [batch_size]
            opponent_features: Opponent team features [batch_size, opponent_dim]
            
        Returns:
            gate_prob: Probability of zero rebounds [batch_size, 1]
            rate: Poisson rate parameter [batch_size, 1]
        """
        # Position-specific processing
        pos_features = self.position_embedding(x, position_indices)
        
        # Main feature processing
        main_features = self.feature_net(pos_features)
        
        # Opponent interaction
        matchup_features, opportunity_index = self.boxout_impact(
            pos_features, opponent_features
        )
        
        # Combine all features
        combined = torch.cat([main_features, matchup_features, opportunity_index], dim=1)
        final_features = self.combine_net(combined)
        
        # Zero-Inflated Poisson output
        gate_prob, rate = self.zip_layer(final_features)
        
        return gate_prob, rate
    
    def zero_inflated_poisson_loss(self, gate_prob: torch.Tensor, rate: torch.Tensor,
                                   targets: torch.Tensor) -> torch.Tensor:
        """
        Calculate Zero-Inflated Poisson loss.
        
        Args:
            gate_prob: Probability of structural zero [batch_size, 1]
            rate: Poisson rate parameter [batch_size, 1]
            targets: Target rebound counts [batch_size, 1]
            
        Returns:
            Loss value
        """
        # Squeeze dimensions for easier computation
        gate_prob = gate_prob.squeeze(-1)
        rate = rate.squeeze(-1)
        targets = targets.squeeze(-1)
        
        # Probability of zero from Poisson
        poisson_zero_prob = torch.exp(-rate)
        
        # Total probability of zero
        total_zero_prob = gate_prob + (1 - gate_prob) * poisson_zero_prob
        
        # For zero targets
        zero_mask = (targets == 0)
        zero_log_prob = torch.log(total_zero_prob + 1e-8)
        
        # For non-zero targets
        non_zero_mask = (targets > 0)
        non_zero_log_prob = (
            torch.log(1 - gate_prob + 1e-8) +
            targets * torch.log(rate + 1e-8) - 
            rate - 
            torch.lgamma(targets + 1)
        )
        
        # Combined loss
        log_prob = torch.zeros_like(targets)
        log_prob[zero_mask] = zero_log_prob[zero_mask]
        log_prob[non_zero_mask] = non_zero_log_prob[non_zero_mask]
        
        return -log_prob.mean()
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Training step."""
        x = batch['features']
        position_indices = batch['position_indices']
        opponent_features = batch['opponent_features']
        targets = batch['rebounds']
        
        gate_prob, rate = self.forward(x, position_indices, opponent_features)
        loss = self.zero_inflated_poisson_loss(gate_prob, rate, targets)
        
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.train_losses.append(loss.item())
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Validation step."""
        x = batch['features']
        position_indices = batch['position_indices']
        opponent_features = batch['opponent_features']
        targets = batch['rebounds']
        
        gate_prob, rate = self.forward(x, position_indices, opponent_features)
        loss = self.zero_inflated_poisson_loss(gate_prob, rate, targets)
        
        # Calculate predictions for metrics
        predictions = (1 - gate_prob) * rate
        
        # Calculate metrics
        mae = F.l1_loss(predictions.squeeze(), targets.squeeze())
        mse = F.mse_loss(predictions.squeeze(), targets.squeeze())
        
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_mae', mae, on_step=False, on_epoch=True)
        self.log('val_mse', mse, on_step=False, on_epoch=True)
        
        self.val_losses.append(loss.item())
        
        return loss
    
    def test_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """Test step."""
        x = batch['features']
        position_indices = batch['position_indices']
        opponent_features = batch['opponent_features']
        targets = batch['rebounds']
        
        gate_prob, rate = self.forward(x, position_indices, opponent_features)
        loss = self.zero_inflated_poisson_loss(gate_prob, rate, targets)
        
        # Calculate predictions for metrics
        predictions = (1 - gate_prob) * rate
        
        # Calculate metrics
        mae = F.l1_loss(predictions.squeeze(), targets.squeeze())
        mse = F.mse_loss(predictions.squeeze(), targets.squeeze())
        
        self.log('test_loss', loss, on_step=False, on_epoch=True)
        self.log('test_mae', mae, on_step=False, on_epoch=True)
        self.log('test_mse', mse, on_step=False, on_epoch=True)
        
        return loss
    
    def configure_optimizers(self):
        """Configure optimizer."""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',
                'interval': 'epoch'
            }
        }
    
    def predict_rebounds(self, x: torch.Tensor, position_indices: torch.Tensor,
                        opponent_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Generate rebound predictions with uncertainty estimates.
        
        Args:
            x: Input features [batch_size, input_dim]
            position_indices: Position categories [batch_size]
            opponent_features: Opponent team features [batch_size, opponent_dim]
            
        Returns:
            Dictionary with predictions and uncertainty estimates
        """
        self.eval()
        with torch.no_grad():
            gate_prob, rate = self.forward(x, position_indices, opponent_features)
            
            # Expected value
            expected_rebounds = (1 - gate_prob) * rate
            
            # Variance
            variance = (1 - gate_prob) * rate * (1 + gate_prob * rate)
            
            # Sample multiple predictions for uncertainty
            samples = self.zip_layer.sample(
                self.combine_net(
                    torch.cat([
                        self.feature_net(self.position_embedding(x, position_indices)),
                        self.boxout_impact(
                            self.position_embedding(x, position_indices),
                            opponent_features
                        )[0],
                        self.boxout_impact(
                            self.position_embedding(x, position_indices),
                            opponent_features
                        )[1]
                    ], dim=1)
                ),
                num_samples=100
            )
            
            prediction_std = samples.std(dim=0)
            
            return {
                'expected_rebounds': expected_rebounds,
                'variance': variance,
                'std': prediction_std,
                'gate_prob': gate_prob,
                'rate': rate,
                'samples': samples
            }
