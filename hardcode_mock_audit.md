🔍 HMNV WNBA PIPELINE - HARD-CODED VALUES & MOCK DATA AUDIT
============================================================

This report identifies all instances of hard-coded values, mock data, placeholders, and sample data generation that need to be replaced with real unified data.

## 🚨 CRITICAL ISSUES FOUND:

### 1. DUMMY/MOCK MODELS
- **Location**: `step_03_moneyline_model/training/train_moneyline.py`
- **Issue**: Uses `DummyTotalsModel` when real Game Totals Model unavailable
- **Fix**: Ensure proper model loading from checkpoints

### 2. PLACEH<PERSON>DER TENSOR FEATURES
- **Location**: `step_05_spread_model/data_processing/spread_data_module.py`
- **Issue**: Uses `torch.randn()` for totals_features and moneyline_features
- **Lines**: 574, 579, 607, 612
- **Fix**: Replace with actual model predictions

### 3. SAMPLE DATA GENERATION
- **Location**: Multiple models use `_create_sample_data()` when unified data unavailable
- **Models**: rebounds, spread, assists, threes, steals_blocks
- **Fix**: Ensure all models load from consolidated_wnba/

### 4. HARD-CODED BASKETBALL CONSTANTS
- **Location**: Multiple files
- **Examples**:
  - Position weights: [0.15, 0.15, 0.2, 0.25, 0.25]
  - Rebound split ratios: 0.75 (defensive), 0.25 (offensive)
  - Shooting percentages: 0.75, 0.35, etc.
  - Pace values: 95-105 range

### 5. HARD-CODED VALIDATION THRESHOLDS
- **Location**: Various validation files
- **Examples**:
  - AUC > 0.75 for overtime detection
  - MAE < 2.5 for player points
  - Accuracy > 85% for various metrics

## 📋 DETAILED FINDINGS:

### Step 01 - Player Points Model
✅ **Status**: CLEAN - Uses unified data properly
- Correctly prioritizes unified_data/unified_player_data.csv
- No sample data generation fallbacks

### Step 02 - Game Totals Model  
✅ **Status**: CLEAN - Uses unified data properly
- Loads from consolidated_wnba structure
- No placeholder values in core logic

### Step 03 - Moneyline Model
🚨 **Status**: CRITICAL ISSUES
- Uses DummyTotalsModel when real model unavailable
- Mock team/player features creation
- Placeholder torch.zeros() for date features

### Step 04 - Rebounds Model
⚠️ **Status**: MIXED - Has fallbacks
- Uses unified data when available
- Falls back to _create_sample_data() when missing
- Hard-coded rebound split ratios (0.75/0.25)

### Step 05 - Spread Model
🚨 **Status**: CRITICAL ISSUES
- torch.randn() placeholders for totals_features
- torch.randn() placeholders for moneyline_features
- Sample data generation when unified data missing

### Step 06 - Assists Model
⚠️ **Status**: MIXED - Has fallbacks
- Uses unified data when available
- Falls back to create_sample_data() when missing
- Hard-coded teammate shooting percentages

### Step 07 - Threes Model
⚠️ **Status**: MIXED - Has fallbacks
- Falls back to _create_sample_data() when missing
- Should use consolidated_wnba/05_tracking_data/shot_tracking/

### Step 08 - Steals & Blocks Model
⚠️ **Status**: MIXED - Has fallbacks
- Falls back to _create_sample_data() when missing
- Should use consolidated_wnba/05_tracking_data/player_tracking/

## 🔧 REQUIRED FIXES:

### 1. Remove Dummy Models
- Replace DummyTotalsModel with proper model loading
- Ensure all inter-model dependencies use real checkpoints

### 2. Replace Placeholder Tensors
- Remove torch.randn() placeholders
- Use actual model predictions for hierarchical features

### 3. Ensure Unified Data Usage
- Remove all _create_sample_data() fallbacks
- Ensure all models load from consolidated_wnba/

### 4. Replace Hard-coded Constants
- Load basketball constants from data or configuration
- Use data-driven thresholds instead of hard-coded values

### 5. Fix File Paths
- Ensure all models use correct consolidated_wnba paths
- Remove sample_data/ directory references

## 📊 BASKETBALL CONSTANTS TO REVIEW:

### Position-Based Constants
- Rebound rates by position
- Shooting percentages by position
- Playing time expectations

### Game Constants
- Pace ranges (95-105)
- Shooting percentage ranges
- Statistical thresholds

### Performance Thresholds
- All validation benchmarks
- Performance alerts
- Model quality gates

## 🎯 ACTION ITEMS:

1. **IMMEDIATE**: Remove all torch.randn() placeholders
2. **IMMEDIATE**: Replace DummyTotalsModel with real model loading
3. **HIGH**: Remove sample data generation fallbacks
4. **MEDIUM**: Replace hard-coded constants with data-driven values
5. **LOW**: Review and validate all performance thresholds

## 📁 FILES REQUIRING IMMEDIATE ATTENTION:

1. `step_03_moneyline_model/training/train_moneyline.py` - Remove dummy model
2. `step_05_spread_model/data_processing/spread_data_module.py` - Remove placeholders
3. `step_04_rebounds_model/data_processing/rebound_data_module.py` - Remove sample data
4. `step_06_assists_model/data_processing/assists_data_module.py` - Remove sample data
5. `step_07_threes_model/data_processing/threes_data_module.py` - Remove sample data
6. `step_08_steals_blocks_model/data_processing/steals_blocks_data_module.py` - Remove sample data

## ✅ VALIDATION REQUIRED:

After fixes, validate:
- All models load real unified data
- No sample data generation occurs
- All inter-model dependencies use real checkpoints
- Performance metrics remain within expected ranges
- Basketball logic remains sound

Generated: July 8, 2025
