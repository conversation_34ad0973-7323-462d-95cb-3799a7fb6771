"""
📊 Create Comprehensive WNBA Game Totals Dataset

This script combines all WNBA games data from multiple years to create
a comprehensive team-level game dataset for the Step 2 model.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import os
import glob
from datetime import datetime


def combine_wnba_games_data(consolidated_path: Path, output_path: str) -> None:
    """
    Combine all WNBA games files into a single comprehensive dataset.
    
    Args:
        consolidated_path: Path to consolidated_wnba directory
        output_path: Where to save the combined game data CSV
    """
    
    # Find all WNBA games files
    games_pattern = str(consolidated_path / "03_game_data" / "schedules" / "wnba_games_*.csv")
    games_files = glob.glob(games_pattern)
    
    print(f"📊 Found {len(games_files)} WNBA games files")
    
    if not games_files:
        print("❌ No WNBA games files found!")
        return
    
    all_games = []
    
    for file_path in sorted(games_files):
        try:
            # Load games for this year
            games_df = pd.read_csv(file_path)
            
            if games_df.empty:
                continue
            
            print(f"📅 Loaded {len(games_df)} games from {os.path.basename(file_path)}")
            all_games.append(games_df)
                
        except Exception as e:
            print(f"⚠️  Error processing {file_path}: {e}")
            continue
    
    if not all_games:
        print("❌ No game data could be loaded!")
        return
    
    # Combine all games
    combined_df = pd.concat(all_games, ignore_index=True)
    
    # Clean and standardize the data
    combined_df = clean_wnba_games_data(combined_df)
    
    # Sort by date and game_id
    combined_df = combined_df.sort_values(['date', 'game_id', 'team_id'])
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save to CSV
    combined_df.to_csv(output_path, index=False)
    
    print(f"✅ Created comprehensive WNBA game totals data: {len(combined_df)} team games saved to {output_path}")
    print(f"📈 Data covers {combined_df['game_id'].nunique()} unique games")
    print(f"📅 Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
    print(f"🏀 Teams: {sorted(combined_df['team_abbreviation'].unique())}")
    print(f"🗓️ Years: {sorted(combined_df['season'].unique())}")


def clean_wnba_games_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean and standardize the WNBA games data.
    
    Args:
        df: Raw combined games dataframe
        
    Returns:
        Cleaned dataframe with standardized columns
    """
    
    # Make a copy
    cleaned_df = df.copy()
    
    # Convert GAME_DATE to proper datetime and create date column
    cleaned_df['date'] = pd.to_datetime(cleaned_df['GAME_DATE'])
    
    # Rename columns to match the expected format
    column_mapping = {
        'GAME_ID': 'game_id',
        'TEAM_ID': 'team_id', 
        'TEAM_ABBREVIATION': 'team_abbreviation',
        'PTS': 'team_total_points',
        'FGM': 'field_goals_made',
        'FGA': 'field_goals_attempted',
        'FG_PCT': 'field_goal_percentage',
        'FG3M': 'three_pointers_made',
        'FG3A': 'three_pointers_attempted', 
        'FG3_PCT': 'three_point_percentage',
        'FTM': 'free_throws_made',
        'FTA': 'free_throws_attempted',
        'FT_PCT': 'free_throw_percentage',
        'REB': 'rebounds',
        'AST': 'assists',
        'STL': 'steals',
        'BLK': 'blocks',
        'TOV': 'turnovers'
    }
    
    # Apply column mapping
    for old_col, new_col in column_mapping.items():
        if old_col in cleaned_df.columns:
            cleaned_df[new_col] = cleaned_df[old_col]
    
    # Create opponent_id by finding the other team in each game
    opponent_mapping = {}
    for game_id, game_group in cleaned_df.groupby('game_id'):
        teams = game_group['team_id'].tolist()
        if len(teams) == 2:
            # Map each team to its opponent
            opponent_mapping[f"{game_id}_{teams[0]}"] = teams[1]
            opponent_mapping[f"{game_id}_{teams[1]}"] = teams[0]
    
    # Add opponent_id column
    cleaned_df['opponent_id'] = cleaned_df.apply(
        lambda row: opponent_mapping.get(f"{row['game_id']}_{row['team_id']}", row['team_id']), 
        axis=1
    )
    
    # Handle missing values
    numeric_cols = ['field_goals_made', 'field_goals_attempted', 'three_pointers_made',
                   'three_pointers_attempted', 'free_throws_made', 'free_throws_attempted',
                   'rebounds', 'assists', 'steals', 'blocks', 'turnovers']
    
    for col in numeric_cols:
        if col in cleaned_df.columns:
            cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
    
    # Handle percentage columns
    pct_cols = ['field_goal_percentage', 'three_point_percentage', 'free_throw_percentage']
    for col in pct_cols:
        if col in cleaned_df.columns:
            cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
    
    # Ensure team_total_points is numeric
    cleaned_df['team_total_points'] = pd.to_numeric(cleaned_df['team_total_points'], errors='coerce').fillna(0)
    
    # Remove rows where team_total_points is 0 (likely bad data)
    cleaned_df = cleaned_df[cleaned_df['team_total_points'] > 0]
    
    # Keep only the columns we need
    keep_columns = [
        'game_id', 'date', 'team_id', 'opponent_id', 'team_abbreviation',
        'team_total_points', 'field_goals_made', 'field_goals_attempted', 
        'field_goal_percentage', 'three_pointers_made', 'three_pointers_attempted',
        'three_point_percentage', 'free_throws_made', 'free_throws_attempted',
        'free_throw_percentage', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers',
        'season'
    ]
    
    # Only keep columns that exist
    keep_columns = [col for col in keep_columns if col in cleaned_df.columns]
    cleaned_df = cleaned_df[keep_columns]
    
    return cleaned_df


def main():
    """Main function to create comprehensive WNBA game data"""
    
    # Set up paths
    current_dir = Path(__file__).parent
    base_path = current_dir.parent.parent  # Go up to HMNV_WNBA directory
    consolidated_path = base_path / "consolidated_wnba"
    output_path = current_dir.parent / "data" / "comprehensive_wnba_game_totals.csv"
    
    # Process the data
    combine_wnba_games_data(consolidated_path, str(output_path))


if __name__ == "__main__":
    main()
