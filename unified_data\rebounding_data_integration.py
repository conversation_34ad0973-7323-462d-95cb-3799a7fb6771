"""
Rebounding Data Integration Pipeline
===================================

This module integrates real WNBA rebounding data from the consolidated_wnba
dataset with the unified player dataset to create authentic rebounding
features for the rebounds model.

Real Data Sources:
- wnba_advanced_stats_*.csv: Advanced rebounding percentages (OREB_PCT, DREB_PCT, REB_PCT)
- real_wnba_rebounds_training_data.csv: Player rebounding training data
- boxscore_advanced_v2_*.csv: Game-level rebounding data

Output:
- Enhanced unified_player_data.csv with real rebounding features
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Optional
import glob

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReboundingDataIntegrator:
    """Integrates real rebounding data with unified player data."""
    
    def __init__(self, 
                 consolidated_path: str = "../consolidated_wnba",
                 unified_path: str = "unified_player_data_with_shot_profiles.csv"):
        self.consolidated_path = Path(consolidated_path)
        self.unified_path = Path(unified_path)
        self.advanced_stats_path = self.consolidated_path / "01_player_data" / "advanced_stats"
        self.training_data_path = self.consolidated_path / "04_training_data" / "player_props"
        
        # Real rebounding features we'll extract
        self.rebounding_features = {
            'oreb_pct': 'OREB_PCT',      # Offensive rebound percentage
            'dreb_pct': 'DREB_PCT',      # Defensive rebound percentage  
            'reb_pct': 'REB_PCT',        # Total rebound percentage
            'ast_pct': 'AST_PCT',        # Assist percentage (related to rebounding role)
            'usg_pct': 'USG_PCT',        # Usage percentage (rebounding opportunity)
            'pace': 'PACE',              # Team pace (affects rebounding opportunities)
            'pie': 'PIE'                 # Player Impact Estimate
        }
        
    def load_unified_data(self) -> pd.DataFrame:
        """Load the current unified player data with shot profiles."""
        logger.info(f"Loading unified data from {self.unified_path}")
        
        if not self.unified_path.exists():
            raise FileNotFoundError(f"Unified data not found: {self.unified_path}")
            
        df = pd.read_csv(self.unified_path)
        logger.info(f"Loaded {len(df)} players from unified data")
        
        return df
    
    def load_advanced_rebounding_data(self, years: List[int] = None) -> pd.DataFrame:
        """Load real advanced rebounding data from consolidated_wnba."""
        if years is None:
            years = list(range(2015, 2025))  # 2015-2024
            
        logger.info(f"Loading advanced rebounding data for years: {years}")
        
        all_rebounding_data = []
        
        for year in years:
            advanced_file = self.advanced_stats_path / f"wnba_advanced_stats_{year}.csv"
            
            if advanced_file.exists():
                try:
                    df = pd.read_csv(advanced_file)
                    
                    # Add year column
                    df['season'] = year
                    
                    # Clean player names and ensure we have required columns
                    if 'PLAYER_NAME' in df.columns:
                        df['player_name'] = df['PLAYER_NAME'].str.strip()
                        all_rebounding_data.append(df)
                        logger.info(f"Loaded {len(df)} players from {year} advanced stats")
                    else:
                        logger.warning(f"No PLAYER_NAME column in {advanced_file}")
                        
                except Exception as e:
                    logger.error(f"Error loading {advanced_file}: {e}")
            else:
                logger.warning(f"Advanced stats file not found: {advanced_file}")
        
        if not all_rebounding_data:
            logger.error("No advanced rebounding data loaded!")
            return pd.DataFrame()
            
        # Combine all years
        combined_rebounding = pd.concat(all_rebounding_data, ignore_index=True)
        logger.info(f"Combined advanced rebounding data: {len(combined_rebounding)} total records")
        
        return combined_rebounding
    
    def load_training_rebounding_data(self) -> pd.DataFrame:
        """Load real rebounding training data."""
        logger.info("Loading rebounding training data...")
        
        training_files = [
            "real_wnba_rebounds_training_data.csv",
            "real_wnba_rebounds_training_data_backup.csv"
        ]
        
        for filename in training_files:
            file_path = self.training_data_path / filename
            if file_path.exists():
                try:
                    df = pd.read_csv(file_path)
                    logger.info(f"Loaded rebounding training data: {len(df)} records from {filename}")
                    return df
                except Exception as e:
                    logger.error(f"Error loading {file_path}: {e}")
        
        logger.warning("No rebounding training data found")
        return pd.DataFrame()
    
    def extract_rebounding_features(self, advanced_data: pd.DataFrame) -> pd.DataFrame:
        """Extract rebounding features from the advanced stats data."""
        logger.info("Extracting rebounding features...")
        
        if advanced_data.empty:
            logger.warning("No advanced data to process")
            return pd.DataFrame()
        
        rebounding_features = []
        
        for (player, season), group in advanced_data.groupby(['player_name', 'season']):
            if len(group) > 1:
                # Take the first record if multiple (shouldn't happen)
                group = group.iloc[:1]
            
            player_features = {
                'player_name': player,
                'season': season
            }
            
            try:
                # Extract rebounding percentages
                player_features['oreb_pct'] = pd.to_numeric(group.iloc[0].get('OREB_PCT', 0), errors='coerce') or 0
                player_features['dreb_pct'] = pd.to_numeric(group.iloc[0].get('DREB_PCT', 0), errors='coerce') or 0
                player_features['reb_pct'] = pd.to_numeric(group.iloc[0].get('REB_PCT', 0), errors='coerce') or 0
                
                # Extract related performance metrics
                player_features['ast_pct'] = pd.to_numeric(group.iloc[0].get('AST_PCT', 0), errors='coerce') or 0
                player_features['usg_pct'] = pd.to_numeric(group.iloc[0].get('USG_PCT', 0), errors='coerce') or 0
                player_features['pace'] = pd.to_numeric(group.iloc[0].get('PACE', 0), errors='coerce') or 0
                player_features['pie'] = pd.to_numeric(group.iloc[0].get('PIE', 0), errors='coerce') or 0
                
                # Extract games and minutes for context
                player_features['games_played_adv'] = pd.to_numeric(group.iloc[0].get('GP', 0), errors='coerce') or 0
                player_features['minutes_adv'] = pd.to_numeric(group.iloc[0].get('MIN', 0), errors='coerce') or 0
                
                # Extract team context
                player_features['team_abbreviation_adv'] = group.iloc[0].get('TEAM_ABBREVIATION', '')
                
                rebounding_features.append(player_features)
                
            except Exception as e:
                logger.warning(f"Error processing rebounding data for {player}: {e}")
                continue
        
        if not rebounding_features:
            logger.warning("No rebounding features extracted")
            return pd.DataFrame()
            
        rebounding_df = pd.DataFrame(rebounding_features)
        logger.info(f"Extracted rebounding features for {len(rebounding_df)} player-seasons")
        
        return rebounding_df
    
    def aggregate_rebounding_by_player(self, rebounding_df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate rebounding data by player across all seasons."""
        logger.info("Aggregating rebounding data by player across all seasons...")
        
        if rebounding_df.empty:
            return pd.DataFrame()
        
        # Aggregate rebounding data by player (weighted by games played)
        rebounding_agg = rebounding_df.groupby('player_name').apply(
            lambda group: pd.Series({
                'oreb_pct_avg': np.average(group['oreb_pct'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['oreb_pct'].mean(),
                'dreb_pct_avg': np.average(group['dreb_pct'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['dreb_pct'].mean(),
                'reb_pct_avg': np.average(group['reb_pct'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['reb_pct'].mean(),
                'ast_pct_avg': np.average(group['ast_pct'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['ast_pct'].mean(),
                'usg_pct_avg': np.average(group['usg_pct'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['usg_pct'].mean(),
                'pace_avg': np.average(group['pace'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['pace'].mean(),
                'pie_avg': np.average(group['pie'], weights=group['games_played_adv']) if group['games_played_adv'].sum() > 0 else group['pie'].mean(),
                'total_games_adv': group['games_played_adv'].sum(),
                'total_minutes_adv': group['minutes_adv'].sum(),
                'seasons_played': len(group)
            })
        ).reset_index()
        
        logger.info(f"Aggregated rebounding data for {len(rebounding_agg)} players")
        
        return rebounding_agg
    
    def merge_rebounding_data(self, unified_df: pd.DataFrame, 
                             rebounding_agg: pd.DataFrame) -> pd.DataFrame:
        """Merge rebounding data with unified player data."""
        logger.info("Merging rebounding data with unified player data...")
        
        # Start with unified data
        enhanced_df = unified_df.copy()
        
        # Merge rebounding data
        if not rebounding_agg.empty:
            logger.info(f"Merging rebounding data for {len(rebounding_agg)} players")
            enhanced_df = enhanced_df.merge(
                rebounding_agg, 
                on='player_name', 
                how='left',
                suffixes=('', '_reb')
            )
            logger.info(f"After rebounding merge: {len(enhanced_df)} records")
        
        # Fill missing rebounding data with 0
        rebounding_columns = [col for col in enhanced_df.columns 
                             if any(term in col for term in ['oreb_pct', 'dreb_pct', 'reb_pct', 
                                                            'ast_pct', 'usg_pct', 'pace', 'pie'])]
        
        for col in rebounding_columns:
            if enhanced_df[col].dtype in ['float64', 'int64']:
                enhanced_df[col] = enhanced_df[col].fillna(0)
        
        logger.info(f"Added {len(rebounding_columns)} rebounding features")
        
        return enhanced_df

    def create_derived_rebounding_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create derived rebounding features from the real data."""
        logger.info("Creating derived rebounding features...")

        enhanced_df = df.copy()

        # Rebounding role indicators (using real percentages)
        if 'oreb_pct_avg' in enhanced_df.columns:
            enhanced_df['offensive_rebounder'] = (enhanced_df['oreb_pct_avg'] > 0.05).astype(int)
            enhanced_df['elite_offensive_rebounder'] = (enhanced_df['oreb_pct_avg'] > 0.10).astype(int)

        if 'dreb_pct_avg' in enhanced_df.columns:
            enhanced_df['defensive_rebounder'] = (enhanced_df['dreb_pct_avg'] > 0.15).astype(int)
            enhanced_df['elite_defensive_rebounder'] = (enhanced_df['dreb_pct_avg'] > 0.20).astype(int)

        if 'reb_pct_avg' in enhanced_df.columns:
            enhanced_df['total_rebounder'] = (enhanced_df['reb_pct_avg'] > 0.12).astype(int)
            enhanced_df['elite_total_rebounder'] = (enhanced_df['reb_pct_avg'] > 0.18).astype(int)

        # Rebounding efficiency relative to usage
        if 'reb_pct_avg' in enhanced_df.columns and 'usg_pct_avg' in enhanced_df.columns:
            enhanced_df['rebounding_efficiency'] = enhanced_df['reb_pct_avg'] / np.maximum(enhanced_df['usg_pct_avg'], 0.01)

        # Rebounding balance (offensive vs defensive)
        if 'oreb_pct_avg' in enhanced_df.columns and 'dreb_pct_avg' in enhanced_df.columns:
            total_reb = enhanced_df['oreb_pct_avg'] + enhanced_df['dreb_pct_avg']
            enhanced_df['rebounding_balance'] = np.where(
                total_reb > 0,
                enhanced_df['dreb_pct_avg'] / total_reb,  # 0.5 = balanced, >0.5 = defensive focused
                0.5
            )

        # Pace-adjusted rebounding
        if 'reb_pct_avg' in enhanced_df.columns and 'pace_avg' in enhanced_df.columns:
            league_avg_pace = 100.0  # Approximate WNBA pace
            enhanced_df['pace_adjusted_rebounding'] = enhanced_df['reb_pct_avg'] * (enhanced_df['pace_avg'] / league_avg_pace)

        # Player impact through rebounding
        if 'pie_avg' in enhanced_df.columns and 'reb_pct_avg' in enhanced_df.columns:
            enhanced_df['rebounding_impact'] = enhanced_df['pie_avg'] * enhanced_df['reb_pct_avg']

        logger.info("Created derived rebounding features")

        return enhanced_df

    def integrate_rebounding_data(self, years: List[int] = None) -> pd.DataFrame:
        """Main method to integrate all rebounding data."""
        logger.info("🏀 Starting rebounding data integration...")

        # Load unified data
        unified_df = self.load_unified_data()

        # Load rebounding data
        advanced_rebounding = self.load_advanced_rebounding_data(years)
        training_rebounding = self.load_training_rebounding_data()

        # Extract features
        rebounding_features = self.extract_rebounding_features(advanced_rebounding)

        # Aggregate by player
        rebounding_agg = self.aggregate_rebounding_by_player(rebounding_features)

        # Merge all data
        enhanced_df = self.merge_rebounding_data(unified_df, rebounding_agg)

        # Create derived features
        final_df = self.create_derived_rebounding_features(enhanced_df)

        logger.info(f"✅ Integration complete: {len(final_df)} players with rebounding data")

        return final_df

    def save_enhanced_unified_data(self, enhanced_df: pd.DataFrame,
                                  output_path: str = "unified_player_data_with_rebounding.csv"):
        """Save the enhanced unified data with rebounding features."""
        output_file = Path(output_path)

        logger.info(f"Saving enhanced unified data to {output_file}")
        enhanced_df.to_csv(output_file, index=False)

        # Create summary
        rebounding_cols = [col for col in enhanced_df.columns
                          if any(term in col for term in ['oreb_pct', 'dreb_pct', 'reb_pct',
                                                         'rebounding_', 'rebounder', 'pace_', 'pie_'])]

        logger.info(f"📊 Enhanced dataset summary:")
        logger.info(f"   Total players: {len(enhanced_df)}")
        logger.info(f"   Original features: {len(enhanced_df.columns) - len(rebounding_cols)}")
        logger.info(f"   Rebounding features: {len(rebounding_cols)}")
        logger.info(f"   Total features: {len(enhanced_df.columns)}")

        return output_file


def main():
    """Run the rebounding data integration pipeline."""
    integrator = ReboundingDataIntegrator()

    # Integrate rebounding data
    enhanced_df = integrator.integrate_rebounding_data()

    # Save enhanced dataset
    output_file = integrator.save_enhanced_unified_data(enhanced_df)

    print(f"🏀 Rebounding data integration complete!")
    print(f"📁 Enhanced dataset saved: {output_file}")
    print(f"📊 Total features: {len(enhanced_df.columns)}")

    # Show sample of new features
    rebounding_cols = [col for col in enhanced_df.columns
                      if any(term in col for term in ['oreb_pct', 'dreb_pct', 'reb_pct'])][:10]

    if rebounding_cols:
        print(f"\n🎯 Sample rebounding features:")
        for col in rebounding_cols:
            print(f"   {col}")


if __name__ == "__main__":
    main()
